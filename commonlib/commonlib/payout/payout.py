import json
from datetime import datetime, timezone
from time import sleep
from typing import (
    Any,
    Dict,
    List,
    NamedTuple,
    NoReturn,
    Optional,
    Tu<PERSON>,
    TypedDict,
    Union,
)

from bson.objectid import ObjectId
from google.cloud import recaptchaenterprise_v1 as google_recaptcha
from paypalhttp.http_error import HttpError
from pymongo import DESCENDING
from pytasched import Task
from typing_extensions import assert_never

from commonlib import live_config
from commonlib.basic.price import cent_to_dollar
from commonlib.basic.structured_log import LogContentType, get_structured_logger
from commonlib.clevertap.clevertap import Event
from commonlib.exceptions import BlidzException, LimitExceeded
from commonlib.models.fincoin_wallet import FincoinExchangeRate, FincoinWallet
from commonlib.models.membership import Membership, MembershipStatus, Tier
from commonlib.models.payout.payout_defs import (
    GiftCardParameter,
    PayoutParameters,
    PayoutSource,
    PayoutType,
    PayPalParameter,
)
from commonlib.models.payout.payout_log import PayoutLog
from commonlib.models.payout.payout_transaction import PayoutOfferLog, PayoutTransaction
from commonlib.models.payout.spin_wheel import WithdrawalOfSpinWheel
from commonlib.models.payout.user_payout_offers import UserPayoutOffer
from commonlib.models.paypal import PaypalAccount
from commonlib.models.user import User
from commonlib.payout.gift_card import create_tremendous_order, wrap_tremendous_request
from commonlib.payout.is_manual_payout_logic import is_manual_payout_logic
from commonlib.payout.payout_turn import (
    CalculateMaximumPayoutTurnAmountCustomAttributes,
    calculate_maximum_payout_turn_amount,
)
from commonlib.paypal import create_payout
from commonlib.paypal_payout import PayPalPayoutRequestBody, TransactionStatus
from commonlib.redis_client import get_redis_client
from commonlib.settings import DEFAULT_CURRENCY
from commonlib.settings_analytics_events import EVENT_PAYOUT_CREATED
from commonlib.utils.time import get_js_time
from commonlib.utils.time_constants import HOUR, JS_DAY, JS_HOUR, MINUTE
from commonlib.utils.utils import (
    check_rate_limit,
    choose_value_for_env,
    is_version_below,
    send_analytics_event,
)


def _get_paypal_email(param: PayoutParameters, user: User):
    if not isinstance(param, PayPalParameter):
        return None

    if param.email:
        return param.email

    if param.paypal_id:
        paypal_account = PaypalAccount.find_one({"paypal_id": param.paypal_id})
        if paypal_account and paypal_account.email:
            return paypal_account.email

    return user.get_email()


def general_payout(
    user: User,
    amount: int,
    fincoin_amount: int,
    payout_type: PayoutType,
    parameter: PayoutParameters,
    currency: str = DEFAULT_CURRENCY,
    payout_source: PayoutSource = PayoutSource.USER,
    transaction_id: Optional[str] = None,  # create one if it's None,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    assessment: Optional[google_recaptcha.Assessment] = None,
    allocation_id: Optional[str] = None,
    note: Optional[str] = None,
) -> PayoutTransaction:
    from worker.tasks_users import schedule_clevertap_event

    user_id = user.get_id_assert()
    WithdrawalOfSpinWheel.unset_last_failed_payout_before_account_reset(user_id=user.get_id_assert())

    event_data: Dict[str, Any] = {
        "amount": cent_to_dollar(amount),
        "payout_type": payout_type.value,
    }
    if allocation_id:
        event_data["allocation_id"] = allocation_id
    if note:
        event_data["reason"] = note

    if payout_type == payout_type.PAYPAL:
        paypal_email, is_manual_payout, transaction = _generate_paypal_payout(
            user,
            amount,
            fincoin_amount,
            parameter,
            currency,
            payout_source,
            transaction_id,
            extra_offers,
            assessment,
            allocation_id,
            note,
        )

        event_data = {
            "payout_channel": "paypal",
            "is_manual_payout": is_manual_payout,
            "to_paypal_email": paypal_email,
            **event_data,
        }
    elif payout_type == payout_type.GIFT_CARD:
        email, transaction = _generate_gift_card_payout(
            user,
            amount,
            fincoin_amount,
            parameter,
            currency,
            payout_source,
            transaction_id,
            extra_offers,
            allocation_id,
            note,
        )

        event_data = {
            "payout_channel": "tremendous",
            "deliver_email": email,
            **event_data,
        }
    else:
        assert_never(payout_type)

    send_analytics_event(
        user_id,
        EVENT_PAYOUT_CREATED,
        event_properties=event_data
    )
    schedule_clevertap_event.delay(
        user_id,
        Event.Names.EVENT_PAYOUT_CREATED,
        data=event_data
    )
    return transaction


def _generate_paypal_payout(
    user: User,
    amount: int,
    fincoin_amount: int,
    parameter: PayoutParameters,
    currency: str = DEFAULT_CURRENCY,
    payout_source: PayoutSource = PayoutSource.USER,
    transaction_id: Optional[str] = None,  # create one if it's None,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    assessment: Optional[google_recaptcha.Assessment] = None,
    allocation_id: Optional[str] = None,
    note: Optional[str] = None,
) -> Tuple[Optional[str], bool, PayoutTransaction]:
    if not isinstance(parameter, PayPalParameter):
        raise Exception("Payout invalid")

    paypal_email = _get_paypal_email(parameter, user)
    is_manual_payout = _is_manual_payout(
        user,
        amount,
        currency,
        payout_source,
        paypal_email,
        assessment,
    )
    if is_manual_payout:
        payout_transaction = _paypal_manual_payouts(
            user,
            parameter,
            amount,
            fincoin_amount,
            currency,
            payout_source,
            extra_offers,
            allocation_id,
            note,
        )
    else:
        sender_batch_id = str(ObjectId())
        sender_item_id = transaction_id or str(ObjectId())
        # do paypal instant payout
        payout_transaction = _paypal_instant_payouts(
            user,
            parameter,
            amount,
            fincoin_amount,
            sender_batch_id,
            sender_item_id,
            currency,
            payout_source,
            extra_offers,
            allocation_id,
            note,
        )

    return paypal_email, is_manual_payout, payout_transaction


def _generate_gift_card_payout(
    user: User,
    amount: int,
    fincoin_amount: int,
    parameter: PayoutParameters,
    currency: str = DEFAULT_CURRENCY,
    payout_source: PayoutSource = PayoutSource.USER,
    transaction_id: Optional[str] = None,  # create one if it's None,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    allocation_id: Optional[str] = None,
    note: Optional[str] = None,
) -> Tuple[str, PayoutTransaction]:
    payout_type = PayoutType.GIFT_CARD

    if not isinstance(parameter, GiftCardParameter):
        raise Exception("Gift card payout invalid")
    if not parameter.email:
        raise BlidzException(status=400, code="payout_receiver_missing")

    config = live_config.get("gift_card_payout_config")
    if not config.get("enabled", False):
        raise Exception("Gift card payout disabled")

    gift_card_id = transaction_id or str(ObjectId())
    req = wrap_tremendous_request(
        parameter=parameter,
        amount=amount,
        currency=currency,
        gift_card_id=gift_card_id,
    )

    log = PayoutLog()
    log.receiver_user_id = user.get_id()
    log.receiver_email = parameter.email
    log.amount = str(cent_to_dollar(amount))
    log.currency = currency
    log.payout_type = payout_type
    log.is_manual_payout = False
    log.request = req.model_dump()
    log.payout_time = get_js_time()
    log.payout_source = payout_source
    if extra_offers:
        log.extra_offers = [p.get_log_data() for p in extra_offers]
    if note:
        log.internal_note = note

    payout_transaction: Optional[PayoutTransaction] = None
    retry = 3
    while retry >= 0:
        log.retry = 3 - retry
        try:
            rsp = create_tremendous_order(req)
            if not rsp:
                continue
            log.response = rsp.model_dump()
            payout_transaction = _create_gift_card_payout_transaction(
                user=user,
                gift_card_id=gift_card_id,
                receiver_email=parameter.email,
                amount=amount,
                fincoin_amount=fincoin_amount,
                currency=currency,
                payout_source=payout_source,
                extra_offers=extra_offers,
                abtest=note,
                allocation_id=allocation_id,
            )
            break
        except Exception as e:
            log.response = {"exception": str(e)}
            log.is_error = True

            sleep(1)
            retry -= 1

            if retry < 0:
                raise e
        finally:
            log.create()

    assert payout_transaction is not None
    return parameter.email, payout_transaction


def _get_extra_offer_note(extra_offers: Optional[List[UserPayoutOffer]] = None):
    ret = "Extra offers:\n"
    if not extra_offers:
        return ""
    for offer in extra_offers:
        ret += f"reason: {offer.reason}, amount: {offer.amount}\n"
    return ret


def _paypal_instant_payouts(
    user: User,
    parameter: PayPalParameter,
    amount: int,
    fincoin_amount: int,
    sender_batch_id: str,
    sender_item_id: str,
    currency: str = DEFAULT_CURRENCY,
    payout_source: PayoutSource = PayoutSource.USER,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    allocation_id: Optional[str] = None,
    note: Optional[str] = None,
) -> PayoutTransaction:
    if parameter.paypal_id:
        receiver = parameter.paypal_id
        recipient_type = "PAYPAL_ID"
    else:
        receiver = parameter.email
        recipient_type = "EMAIL"

    if not receiver:
        raise BlidzException(status=400, code="payout_receiver_missing")

    req = PayPalPayoutRequestBody(
        recipient_type=recipient_type,
        amount=str(cent_to_dollar(amount)),  # Convert to string here.
        currency=currency,
        receiver=receiver,
        recipient_wallet="PAYPAL",
    )
    req.sender_batch_id = sender_batch_id
    req.email_subject = "Your money payout from Blidz!"
    req.email_message = "Congratulations! You just received your reward from Blidz."
    req.sender_item_id = sender_item_id
    if extra_offers:
        req.note = _get_extra_offer_note(extra_offers)
    elif note:
        req.note = note
    payout_type = PayoutType.PAYPAL

    log = PayoutLog()
    log.receiver_user_id = user.get_id()
    log.receiver_email = parameter.email
    log.receiver_id = parameter.paypal_id
    log.amount = req.amount
    log.currency = req.currency
    log.payout_type = payout_type
    log.is_manual_payout = False
    log.request = req.dict()
    log.payout_time = get_js_time()
    log.payout_source = payout_source
    if extra_offers:
        log.extra_offers = [p.get_log_data() for p in extra_offers]
    if note:
        log.internal_note = note

    payout_transaction: Optional[PayoutTransaction] = None
    retry = 3
    while retry >= 0:
        log.retry = 3 - retry
        try:
            response = create_payout(req, parameter.paypal_settings)
            if response:
                log.response = response.dict()
                log.create()

                payout_transaction = _create_instant_payout_transaction(
                    user, req, payout_type, amount, fincoin_amount,
                    payout_source=payout_source, extra_offers=extra_offers,
                    abtest=note,
                    allocation_id=allocation_id,
                )
                break

            log.create()

        except HttpError as e:
            try:
                log.response = json.loads(e.message)
            except Exception as loads_e:
                log.response = {
                    "exception": str(e),
                    "json_loads_exception": str(loads_e),
                }
            log.is_error = True
            log.create()

            sleep(1)
            retry -= 1

            if retry < 0:
                raise e

        except Exception as e:
            log.response = {"exception": str(e)}
            log.is_error = True
            log.create()
            raise e

    assert payout_transaction is not None
    return payout_transaction


def _paypal_manual_payouts(
    user: User,
    parameter: PayPalParameter,
    amount: int,
    fincoin_amount: int,
    currency: str = DEFAULT_CURRENCY,
    payout_source: PayoutSource = PayoutSource.USER,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    allocation_id: Optional[str] = None,
    note: Optional[str] = None,
) -> PayoutTransaction:
    if parameter.paypal_id:
        receiver = parameter.paypal_id
        recipient_type = "PAYPAL_ID"
    else:
        receiver = parameter.email
        recipient_type = "EMAIL"

    log = PayoutLog()
    log.receiver_user_id = user.get_id()
    log.receiver_email = parameter.email
    log.receiver_id = parameter.paypal_id
    log.amount = str(cent_to_dollar(amount))
    log.currency = currency
    log.payout_type = PayoutType.PAYPAL
    log.is_manual_payout = True
    log.request = {}
    log.payout_time = get_js_time()
    log.payout_source = payout_source
    if extra_offers:
        log.extra_offers = [p.get_log_data() for p in extra_offers]
    if note:
        log.internal_note = note

    log.create()

    tx = PayoutTransaction()
    tx.user_id = user.get_id()
    tx.amount = amount
    tx.fincoin_amount = fincoin_amount
    tx.payout_time = get_js_time()
    tx.payout_type = PayoutType.PAYPAL
    tx.payout_source = payout_source
    tx.transaction_status = TransactionStatus.WAITING_MANUAL_PROCESS
    if extra_offers:
        tx.extra_offers = _get_offer_logs(extra_offers)
    if note:
        tx.internal_note = note

    tx.recipient_type = recipient_type
    tx.currency = currency
    tx.receiver = receiver
    tx.recipient_wallet = "PAYPAL"
    tx.allocation_id = allocation_id
    tx.create()
    return tx


def _get_offer_logs(offers: List[UserPayoutOffer]) -> List[PayoutOfferLog]:
    ret = [PayoutOfferLog(o.get_log_data()) for o in offers]
    return ret


def _create_instant_payout_transaction(
    user: User,
    payout_request: PayPalPayoutRequestBody,
    payout_type: PayoutType,
    amount: int,
    fincoin_amount: int,
    payout_source: PayoutSource = PayoutSource.USER,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    abtest: Optional[str] = None,
    allocation_id: Optional[str] = None,
) -> PayoutTransaction:
    """
    Creates a payout transaction.

    :param user: The user model.
    :param payout_request: The request that was sent.
    :param payout_type: The payout type.
    """
    tx = PayoutTransaction()
    tx.user_id = user.get_id()
    tx.amount = amount
    tx.fincoin_amount = fincoin_amount
    tx.payout_time = get_js_time()
    tx.payout_type = payout_type
    tx.payout_source = payout_source
    tx.transaction_status = TransactionStatus.PENDING  # default to PENDING, we update this later.
    if extra_offers:
        tx.extra_offers = _get_offer_logs(extra_offers)
        tx.internal_note = abtest

    if payout_type == PayoutType.PAYPAL:
        tx.recipient_type = payout_request.recipient_type
        tx.currency = payout_request.currency
        tx.receiver = payout_request.receiver
        tx.sender_batch_id = payout_request.sender_batch_id
        tx.sender_item_id = payout_request.sender_item_id
        tx.recipient_wallet = payout_request.recipient_wallet

    tx.allocation_id = allocation_id
    tx.create()

    task = Task(
        "worker.tasks_payout:update_payout_status.delay",
        kwargs={"payout_transaction_id": tx.get_id()},
        wait=3,  # execute in 3s
    )
    task.add()
    return tx


def _create_gift_card_payout_transaction(
    user: User,
    gift_card_id: str,
    receiver_email: str,
    amount: int,
    fincoin_amount: int,
    currency: str,
    payout_source: PayoutSource = PayoutSource.USER,
    extra_offers: Optional[List[UserPayoutOffer]] = None,
    abtest: Optional[str] = None,
    allocation_id: Optional[str] = None,
) -> PayoutTransaction:
    tx = PayoutTransaction()
    tx.user_id = user.get_id()
    tx.amount = amount
    tx.fincoin_amount = fincoin_amount
    tx.currency = currency
    tx.receiver = receiver_email
    tx.payout_time = get_js_time()
    tx.payout_type = PayoutType.GIFT_CARD
    tx.payout_source = payout_source
    tx.transaction_status = TransactionStatus.PENDING  # default to PENDING, we update this later.
    if extra_offers:
        tx.extra_offers = _get_offer_logs(extra_offers)
        tx.internal_note = abtest

    tx.gift_card_id = gift_card_id
    tx.allocation_id = allocation_id
    tx.create()

    task = Task(
        "worker.tasks_payout:update_payout_status.delay",
        kwargs={"payout_transaction_id": tx.get_id()},
        wait=3,  # execute in 3s
    )
    task.add()
    return tx


class RateLimitSetting(TypedDict):
    redis_key: str
    retry_count: int
    max_api_calls: int
    expire_key: int
    error_message: str
    error_code: str
    error_key: str
    error_value: str


def check_payout_rate_limits(user: User, device_id: Optional[str], ip_address: Optional[str]) -> None:
    """
    Checks the necessary rate limits for the payout endpoint.

    The checks are:
    1. IP address limitation, max_api_calls: 5
    2. Device ID limitation, max_api_calls: 2
    3. Each payment method fingerprint, max_api_calls: 2 per fingerprint for client version < 6.6.3, 10 per fingerprint for client version >= 6.6.3.

    Fail the rate limit check when a failure happens, but decrease the previous key's rate limit.

    :param user: The logged-in user.
    :param device_id:
    :param ip_address:
    :raises BlidzException: If a rate limit check fails.
    """
    logger = get_structured_logger("paypal_payout")
    last_client_version = user.last_client_version

    if not device_id or not ip_address:
        raise BlidzException(
            status=429,
            message=f"Cannot detect user {user.get_id_assert()}",
            code="cannot_detect_user_info"
        )

    user_id = user.get_id()
    today = datetime.now(tz=timezone.utc).strftime("%Y-%m-%d")
    expire_key = choose_value_for_env(24 * HOUR, 10 * MINUTE)

    # Build the rate limit settings.
    rate_limit_settings: List[RateLimitSetting] = [
        {
            "redis_key": f"payout_ratelimit:{ip_address}:{today}",
            "retry_count": 1,
            "max_api_calls": 10,
            "expire_key": expire_key,
            "error_message": "IP address rate limit reached.",
            "error_code": "ip_address_rate_limit_exceeded",
            "error_key": "ip_address",
            "error_value": ip_address,
        },
        {
            "redis_key": f"payout_ratelimit:{device_id}:{today}",
            "retry_count": 1,
            "max_api_calls": 10,
            "expire_key": expire_key,
            "error_message": "Device ID rate limit reached.",
            "error_code": "device_id_rate_limit_exceeded",
            "error_key": "device_id",
            "error_value": device_id,
        },
    ]
    payment_methods = user.stripe.get("paymentMethods", {})
    added_payment_methods = []
    for _, card in payment_methods.items():
        card_fingerprint = card.get("fingerprint")
        if card_fingerprint and card_fingerprint not in added_payment_methods:
            added_payment_methods.append(card_fingerprint)
            if not last_client_version or is_version_below("6.6.3", last_client_version):
                max_api_calls = 2
            else:
                max_api_calls = 10
            rate_limit_settings.append(
                {
                    "redis_key": f"payout_ratelimit:{card_fingerprint}:{today}",
                    "retry_count": 1,
                    "max_api_calls": max_api_calls,
                    "expire_key": expire_key,
                    "error_message": "Card fingerprint rate limit reached.",
                    "error_code": "card_fingerprint_rate_limit_exceeded",
                    "error_key": "card_fingerprint",
                    "error_value": card_fingerprint,
                }
            )

    status = 429
    rd = get_redis_client()
    # Rollback the previous check if later checks fails.
    rollback_pl = rd.pipeline()
    for _setting in rate_limit_settings:

        try:
            check_rate_limit(
                redis_key=_setting["redis_key"],
                retry_count=_setting["retry_count"],
                max_api_calls=_setting["max_api_calls"],
                expire_key=_setting["expire_key"],
            )
            rollback_pl.decr(_setting["redis_key"])
        except LimitExceeded as ex:
            message = _setting["error_message"]
            code = _setting["error_code"]
            log_data: LogContentType = {
                "user_id": user_id,
                "max_api_calls": _setting["max_api_calls"],
                "expire_key": expire_key,
                "error_message": message,
                "error_code": code,
            }

            if _setting["error_key"]:
                log_data[_setting["error_key"]] = _setting["error_value"]
            logger.info(log_data)
            rollback_pl.execute()
            raise BlidzException(message=message, status=status, code=code) from ex


def _is_manual_payout(
    user: User,
    amount: int,
    currency: str,
    payout_source: PayoutSource,
    payout_email: Optional[str],
    assessment: Optional[google_recaptcha.Assessment] = None,
) -> bool:
    manual_payout_config = live_config.get("manual_payout_config")
    manual_payout_enabled = manual_payout_config.get("enabled", False)
    membership = user.get_or_create_membership()
    recaptcha_score = assessment.risk_analysis.score if assessment else None

    def fn_get_instant_domains():
        return live_config.get("instant_payout_domains")["domains"]

    def fn_is_active_member_status():
        return membership.is_active_member(False)

    def fn_is_pure_active_member_status():
        return membership.is_active_member(True)

    def fn_is_first_ever_payout():
        txn = PayoutTransaction.find_one({"user_id": user.get_id_assert()})
        return txn is None

    return is_manual_payout_logic(
        manual_payout_enabled=manual_payout_enabled,
        amount=amount,
        currency=currency,
        payout_source=payout_source,
        payout_email=payout_email,
        signup_email=user.get_email(),
        recaptcha_risk_analysis_score=recaptcha_score,
        fn_get_instant_domains=fn_get_instant_domains,
        fn_is_active_member_status=fn_is_active_member_status,
        fn_is_pure_active_member_status=fn_is_pure_active_member_status,
        fn_is_first_ever_payout=fn_is_first_ever_payout
    )


def get_payout_info(user: User, payout_type: PayoutType):
    payout_account = None
    parameter = None
    if payout_type == PayoutType.PAYPAL:
        paypal_account_info = PaypalAccount.by_user_id(user.get_id_assert())
        if paypal_account_info:
            payout_account = paypal_account_info.paypal_id or paypal_account_info.email
        if not payout_account:
            payout_account = user.paypal.payout_account if user.paypal else None
        if payout_account:
            parameter = PayPalParameter(
                email=payout_account if "@" in payout_account else None,
                paypal_id=paypal_account_info.paypal_id if paypal_account_info else None
            )
    elif payout_type == PayoutType.GIFT_CARD:
        email = user.get_email(allow_facebook_fake_email=False)
        payout_account = email
        parameter = GiftCardParameter(
            name=user.get_name(),
            email=email,
        )
    return payout_account, parameter


class VerifyOrGetAvailablePayoutAmountCustomAttributes(NamedTuple):
    fincoin_balance: Optional[int] = None
    payout_turn: Optional[int] = None
    tier: Optional[Tier] = None

    @classmethod
    def from_calculate_maximum_payout_turn_amount_custom_attributes(
        cls,
        custom_attributes: CalculateMaximumPayoutTurnAmountCustomAttributes,
        fincoin_balance: Optional[int] = None,
    ) -> "VerifyOrGetAvailablePayoutAmountCustomAttributes":
        return cls(
            fincoin_balance=fincoin_balance,
            payout_turn=custom_attributes.payout_turn,
            tier=custom_attributes.tier,
        )

    def to_calculate_maximum_payout_turn_amount_custom_attributes(
        self,
    ) -> CalculateMaximumPayoutTurnAmountCustomAttributes:
        return CalculateMaximumPayoutTurnAmountCustomAttributes(
            payout_turn=self.payout_turn,
            tier=self.tier,
        )


def verify_or_get_available_payout_amount(
    user: User,
    membership: Optional[Membership] = None,
    specify_payout_amount: Optional[int] = None,
    custom_attributes: Optional[VerifyOrGetAvailablePayoutAmountCustomAttributes] = None,
) -> Union[NoReturn, int]:
    user_id = user.get_id_assert()
    currency = user.get_currency()
    if not membership:
        membership = user.get_or_create_membership()

    # get current wallet balance
    if custom_attributes and custom_attributes.fincoin_balance is not None:
        current_fincoin_balance = custom_attributes.fincoin_balance
    else:
        fincoin_wallet = FincoinWallet.by_user_id(user_id)
        current_fincoin_balance = fincoin_wallet.amount if fincoin_wallet else 0

    current_payout_balance = FincoinExchangeRate.to_cent(current_fincoin_balance or 0, currency).to_target

    if current_payout_balance == 0:
        raise BlidzException(
            status=400,
            message=f"Not enough balance user_id: {user.get_id_assert()}",
            code="not_enough_balance",
        )

    # check payout turn limit
    calculate_maximum_payout_turn_amount_result = calculate_maximum_payout_turn_amount(
        membership,
        custom_attributes.to_calculate_maximum_payout_turn_amount_custom_attributes() if custom_attributes else None
    )
    if calculate_maximum_payout_turn_amount_result.no_payout_turn:
        raise BlidzException(
            status=400,
            code="no_payout_turn",
            message=f"No payout turn available"
        )

    available_amount = min(current_payout_balance, calculate_maximum_payout_turn_amount_result.amount)

    if specify_payout_amount:
        specify_payout_amount = max(specify_payout_amount, 0)
        if specify_payout_amount > available_amount:
            raise BlidzException(
                status=400,
                code="payout_turn_limit_exceeded",
                message=f"Payout turn limit exceeded: amount({available_amount}) < wants({specify_payout_amount})"
            )
        return specify_payout_amount
    else:
        return available_amount


class PayoutCooldown:
    logger = get_structured_logger("payout_cooldown")

    _COOLDOWN_MATRIX = {
        MembershipStatus.TRIAL_MEMBER: {
            Tier.BLIDZ_PLUS: 8 * JS_HOUR,
        },
        MembershipStatus.TRIAL_MEMBER_NO_RENEWAL: {
            Tier.BLIDZ_PLUS: 8 * JS_HOUR,
        },
        MembershipStatus.ACTIVE_MEMBER: {
            Tier.BLIDZ_PLUS: 8 * JS_HOUR,
            Tier.BLIDZ_PLUS_EXTRA: 2 * JS_HOUR,
            Tier.BLIDZ_PLUS_ULTRA: -1,  # unlimited
        },
        MembershipStatus.ACTIVE_MEMBER_NO_RENEWAL: {
            Tier.BLIDZ_PLUS: 8 * JS_HOUR,
            Tier.BLIDZ_PLUS_EXTRA: 2 * JS_HOUR,
            Tier.BLIDZ_PLUS_ULTRA: -1,  # unlimited
        },
    }

    class ResetData(TypedDict):
        reason: str
        reset_at: int

    @classmethod
    def _redis_key(cls, user_id: str):
        return f"payout_cooldown:{user_id}"

    @classmethod
    def add_payout_cooldown_flag_of_reset(cls, user_id: str, reason: str):
        rd = get_redis_client()
        reset_data = cls.ResetData(
            reason=reason,
            reset_at=get_js_time()
        )
        rd.set(cls._redis_key(user_id), json.dumps(reset_data), ex=24 * 3600)  # 24 hours
        cls.logger.info({
            "user_id": user_id,
            "action": "reset_payout_cooldown",
            "reason": reason,
            "reset_at": reset_data["reset_at"],
        })

    @classmethod
    def remove_payout_cooldown_flag_of_reset(cls, user_id: str):
        rd = get_redis_client()
        reset_data_json = rd.getdel(cls._redis_key(user_id))
        if not reset_data_json:
            return

        reset_data = json.loads(reset_data_json)  # type: ignore

        cls.logger.info({
            "user_id": user_id,
            "action": "consume_cooldown_reset",
            "reason": reset_data["reason"],
            "reset_at": reset_data["reset_at"],
            "consumed_at": get_js_time(),
        })

    @classmethod
    def get_remaining_cooldown_milliseconds(cls, user_id: str, status: MembershipStatus, tier: Optional[Tier]) -> int:
        rd = get_redis_client()
        reset_data_json = rd.get(cls._redis_key(user_id))
        if reset_data_json:
            return 0

        # Default to 24 hours if no match
        interval = JS_DAY
        if config := cls._COOLDOWN_MATRIX.get(status):
            if tier and tier in config:
                interval = config[tier]

        if interval <= 0:
            return 0

        now = get_js_time()
        payout_transaction = PayoutTransaction.find_one(
            query={
                "user_id": user_id,
                "transaction_status": {
                    "$in": list(TransactionStatus.non_failure_status()),
                },
                "payout_time": {
                    "$gt": now - interval,
                },
            },
            sort=[("payout_time", DESCENDING)],
        )
        if not payout_transaction:
            # no payout transaction in the last interval or first ever payout
            return 0

        last_payout_time = payout_transaction.payout_time
        assert last_payout_time is not None

        return last_payout_time + interval - now

    @classmethod
    def check_payout_cooldown(cls, user: User, membership: Membership):
        user_id = user.get_id_assert()
        status = membership.status
        tier = membership.tier
        interval = cls.get_remaining_cooldown_milliseconds(user_id, status, tier)
        if interval > 0:
            hours = max(1, int(interval / JS_HOUR))
            raise BlidzException(
                status=400,
                code="limit_exceeded",
                message=f"You have made 1 payout today. Payout will become available again in {hours} hours"
            )
