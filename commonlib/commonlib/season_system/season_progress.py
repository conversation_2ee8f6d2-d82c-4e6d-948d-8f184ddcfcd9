import json
import random
from functools import lru_cache
from typing import List, Named<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

from sherlock import Lock

from commonlib.basic.maybe import maybe
from commonlib.basic.structured_log import LogContentType, get_structured_logger
from commonlib.models.fincoin_wallet import FincoinExchangeRate
from commonlib.models.membership import Membership
from commonlib.models.payout.spin_wheel import WithdrawalOfSpinWheel
from commonlib.models.season_system.season_progress import (
    SeasonProgress,
    SeasonProgressAction,
    SeasonProgressLineItem,
)
from commonlib.models.user import User
from commonlib.models.user_stats import UserStats
from commonlib.payout.payout_turn import (
    add_payout_turn,
    get_payout_turn_amount,
    get_payout_turn_base_payout_amount,
)
from commonlib.publisher import publish
from commonlib.reward.reward_definition import RewardType
from commonlib.reward.util import RewardParam, RewardUtil
from commonlib.season_system.season_config import (
    MARGIN_LOWER_THRESHOLD,
    MARGIN_LOWER_THRESHOLD_B_M0,
    MARGIN_LOWER_THRESHOLD_B_OTHER,
    MARGIN_UPPER_THRESHOLD,
    PRESENTATION_LEVEL_REWARD_DISPLAY_AMOUNT,
    PRESENTATION_LEVEL_REWARD_DISPLAY_TYPE,
    get_season_level_config,
)
from commonlib.settings import CURRENCY_USD
from commonlib.utils.firebase_abtest import get_abtest_value_for_user
from commonlib.utils.time import get_js_time
from commonlib.utils.time_constants import JS_MONTH


class PLLevelData(NamedTuple):
    revenue: int
    actual_payout: int
    current_payout_turn: int
    base_payout_amount: int
    fulfillable_cogs: int
    refunded_cogs: int


class AdjustedActionRequiredData(NamedTuple):
    membership: Optional[Membership]
    membership_status: Optional[str]
    non_refunded_membership_revenue: int


class ActionRequiredCalculationResult(NamedTuple):
    action_required: int
    lower_threshold: int
    upper_threshold: int
    margin_rate: Optional[float]
    rule_applied: str


class PLLevelCalculationResult(NamedTuple):
    pl_level: int
    revenue: int
    # All parameters used in calculation
    actual_payout: int
    current_payout_turn: int
    base_payout_amount: int
    withdrawable_payout_amount: int
    fulfillable_cogs: int
    refunded_cogs: int


class AdjustedActionRequiredCalculationResult(NamedTuple):
    action_required: int
    # All parameters used in calculation
    level_constant: int
    pl_level: int
    revenue: int
    non_refunded_membership_revenue: int
    membership_month: int
    lower_threshold: int
    upper_threshold: int
    margin_rate: Optional[float]
    rule_applied: str


def _get_pl_level_data(user_id: str) -> PLLevelData:
    """
    Get all the data needed for PL level calculation from MongoDB.

    Returns:
        PLLevelData: Contains all raw data needed for calculation
    """
    # Get revenue (all non-refunded charges)
    user_stats = UserStats.by_user_id(user_id)
    revenue = user_stats.money_spent_without_refund_to_bank if user_stats and user_stats.money_spent_without_refund_to_bank else 0

    # Get actual payout amount
    withdrawal = WithdrawalOfSpinWheel.by_user_id(user_id)
    actual_payout = withdrawal.total_withdraw_amount if withdrawal and withdrawal.total_withdraw_amount else 0

    # Get withdrawable payout amount (current payout turn balance)
    membership = Membership.by_user_id(user_id)
    tier = membership.tier if membership and membership.is_active_member() else None
    current_payout_turn = get_payout_turn_amount(user_id, tier)
    base_payout_amount = get_payout_turn_base_payout_amount(user_id, tier)

    # Get fulfillable and refunded COGS
    fulfillable_cogs = user_stats.cost_of_shipped_orders if user_stats and user_stats.cost_of_shipped_orders else 0
    refunded_cogs = user_stats.cost_of_refunded_complete_duo_deal_orders if user_stats and user_stats.cost_of_refunded_complete_duo_deal_orders else 0

    return PLLevelData(
        revenue=revenue,
        actual_payout=actual_payout,
        current_payout_turn=current_payout_turn,
        base_payout_amount=base_payout_amount,
        fulfillable_cogs=fulfillable_cogs,
        refunded_cogs=refunded_cogs,
    )


def _calculate_pl_level_from_data(
    revenue: int,
    actual_payout: int,
    fulfillable_cogs: int,
    refunded_cogs: int,
    current_payout_turn: int,
    base_payout_amount: int
) -> Tuple[int, int]:
    """
    Pure calculation function for PL level.

    Args:
        revenue: Total non-refunded revenue in cents
        actual_payout: Total withdrawn amount in cents
        fulfillable_cogs: Cost of shipped orders in cents
        refunded_cogs: Cost of refunded complete duo deal orders in cents
        current_payout_turn: Current payout turn amount
        base_payout_amount: Base payout amount per turn

    Returns:
        tuple[int, int]: (pl_level, revenue) in cents
    """
    withdrawable_balance = current_payout_turn * base_payout_amount
    pl_level = revenue - (actual_payout + fulfillable_cogs + withdrawable_balance) + refunded_cogs
    return pl_level, revenue


def calculate_pl_level(user_id: str) -> PLLevelCalculationResult:
    """
    Calculate profit-loss level for margin-based mechanics.

    Returns:
        PLLevelCalculationResult: Contains pl_level, revenue and all parameters used in calculation
    """
    data = _get_pl_level_data(user_id)
    pl_level, revenue = _calculate_pl_level_from_data(
        data.revenue,
        data.actual_payout,
        data.fulfillable_cogs,
        data.refunded_cogs,
        data.current_payout_turn,
        data.base_payout_amount
    )

    withdrawable_payout_amount = data.current_payout_turn * data.base_payout_amount

    return PLLevelCalculationResult(
        pl_level=pl_level,
        revenue=revenue,
        actual_payout=data.actual_payout,
        current_payout_turn=data.current_payout_turn,
        base_payout_amount=data.base_payout_amount,
        withdrawable_payout_amount=withdrawable_payout_amount,
        fulfillable_cogs=data.fulfillable_cogs,
        refunded_cogs=data.refunded_cogs,
    )


def _get_adjusted_action_required_data(user_id: str) -> AdjustedActionRequiredData:
    """
    Get all the data needed for adjusted action required calculation from MongoDB.

    Returns:
        AdjustedActionRequiredData: Contains all raw data needed for calculation
    """
    membership = Membership.by_user_id(user_id)
    user_stats = UserStats.by_user_id(user_id)

    return AdjustedActionRequiredData(
        membership=membership,
        membership_status=membership.status if membership else None,
        non_refunded_membership_revenue=maybe(user_stats).non_refunded_membership_revenue or 0,
    )


def _calculate_adjusted_action_required_from_data(
    level_constant: int,
    pl_level: int,
    revenue: int,
    non_refunded_membership_revenue: int,
    user_bucket: str,
    membership_month: int,
) -> ActionRequiredCalculationResult:
    """
    Pure calculation function for adjusted action required.

    Args:
        level_constant: Base action_required from level config
        pl_level: Profit-loss level in cents
        revenue: Total revenue in cents
        non_refunded_membership_revenue: Non-refunded membership revenue

    Returns:
        ActionRequiredCalculationResult: Detailed calculation results including intermediate values
    """
    # Calculate thresholds regardless of membership status for reporting
    lower_threshold, upper_threshold = calculate_margin_thresholds(revenue, user_bucket, membership_month)
    margin_rate = pl_level / revenue if revenue != 0 else 0

    # Check if user has paid membership fee
    if non_refunded_membership_revenue <= 0:
        return ActionRequiredCalculationResult(
            action_required=level_constant,
            lower_threshold=lower_threshold,
            upper_threshold=upper_threshold,
            margin_rate=None,
            rule_applied="No membership revenue"
        )

    # Apply margin-based logic for users who have paid membership revenue
    if pl_level < lower_threshold:
        # Low margin -> high difficulty
        return ActionRequiredCalculationResult(
            action_required=level_constant * 3,
            lower_threshold=lower_threshold,
            upper_threshold=upper_threshold,
            margin_rate=margin_rate,
            rule_applied="Low margin"
        )
    elif pl_level >= upper_threshold:
        # High margin -> easy difficulty
        return ActionRequiredCalculationResult(
            action_required=1,
            lower_threshold=lower_threshold,
            upper_threshold=upper_threshold,
            margin_rate=margin_rate,
            rule_applied="High margin"
        )
    elif pl_level < 0:
        # Negative margin treatment
        #
        # You may think we should check negative first then check low_threshold,
        # well, you are wrong. It's specced like this on purpose.
        #
        return ActionRequiredCalculationResult(
            action_required=level_constant,
            lower_threshold=lower_threshold,
            upper_threshold=upper_threshold,
            margin_rate=margin_rate,
            rule_applied="Negative margin"
        )
    else:
        # Formula: MAX(1; round(level_constant - (margin_rate*5)^3, 0))
        adjusted_action_required = level_constant - (margin_rate * 5) ** 3
        return ActionRequiredCalculationResult(
            action_required=max(1, round(adjusted_action_required)),
            lower_threshold=lower_threshold,
            upper_threshold=upper_threshold,
            margin_rate=margin_rate,
            rule_applied="Mid margin"
        )


def calculate_adjusted_action_required(
    user_id: str,
    level_constant: int,
    pl_level: int,
    revenue: int,
) -> AdjustedActionRequiredCalculationResult:
    """
    Calculate action_required based on margin thresholds and membership status.

    Args:
        user_id: User ID
        level_constant: Base action_required from level config
        pl_level: Profit-loss level in cents
        revenue: Total revenue in cents

    Returns:
        AdjustedActionRequiredCalculationResult: Contains action_required and all parameters used in calculation
    """
    data = _get_adjusted_action_required_data(user_id)
    membership_month = 0
    if data.membership and data.membership.started_at:
        membership_month = (get_js_time() - data.membership.started_at) // JS_MONTH

    user_bucket = get_abtest_value_for_user(user_id, "reward_curve_m0", "baseline")

    res = _calculate_adjusted_action_required_from_data(
        level_constant,
        pl_level,
        revenue,
        data.non_refunded_membership_revenue,
        user_bucket,
        membership_month,
    )

    return AdjustedActionRequiredCalculationResult(
        action_required=res.action_required,
        level_constant=level_constant,
        pl_level=pl_level,
        revenue=revenue,
        non_refunded_membership_revenue=data.non_refunded_membership_revenue,
        membership_month=membership_month,
        lower_threshold=res.lower_threshold,
        upper_threshold=res.upper_threshold,
        margin_rate=res.margin_rate,
        rule_applied=res.rule_applied
    )


def _log_action_required_calculation(
    user_id: str,
    context: str,
    pl_calculation_result: PLLevelCalculationResult,
    action_required_result: AdjustedActionRequiredCalculationResult,
    level_config_action_required: int,
    level_target: Optional[int],
):
    """
    Log all parameters used in action_required calculation.

    Args:
        user_id: User ID
        context: Context string (e.g., "season_start_calculation", "level_up_calculation")
        pl_calculation_result: Result from calculate_pl_level
        action_required_result: Result from calculate_adjusted_action_required
        level_config_action_required: action_required from level config
        level_target: level target from level config
        **additional_context: Additional context-specific parameters to log
    """
    logger = get_structured_logger("season_action_required_calculation", expire_days=90)

    log_data: LogContentType = {
        "user_id": user_id,
        "context": context,
        "level_config_action_required": level_config_action_required,
        "level_target": level_target,
        # PL Level calculation parameters
        "pl_level": pl_calculation_result.pl_level,
        "revenue": pl_calculation_result.revenue,
        "actual_payout": pl_calculation_result.actual_payout,
        "current_payout_turn": pl_calculation_result.current_payout_turn,
        "base_payout_amount": pl_calculation_result.base_payout_amount,
        "withdrawable_payout_amount": pl_calculation_result.withdrawable_payout_amount,
        # Action required calculation parameters
        "calculated_action_required": action_required_result.action_required,
        "level_constant": action_required_result.level_constant,
        "non_refunded_membership_revenue": action_required_result.non_refunded_membership_revenue,
        "membership_month": action_required_result.membership_month,
        "lower_threshold": action_required_result.lower_threshold,
        "upper_threshold": action_required_result.upper_threshold,
        "margin_rate": action_required_result.margin_rate,
        "rule_applied": action_required_result.rule_applied
    }

    logger.info(log_data)


class _Reward(NamedTuple):
    reason: str
    reward_cents: int
    payout_turn_amount: int


def fill_up_season_progress(user_id: str, reason: Optional[str] = None):
    remaining_progress = 0
    current_progress = 0
    current_level = 0

    with Lock(f"season_progress_{user_id}"):
        season_progress = SeasonProgress.get_season_progress(user_id)
        if season_progress:
            current_level = season_progress.level
            current_progress = season_progress.progress

        level_config = get_season_level_config(user_id, current_level)
        if not level_config:
            return

        target = level_config.level_target
        if not target:
            return

        remaining_progress = target - current_progress

        _add_season_progress_and_give_reward_internal(
            user_id,
            remaining_progress,
            fincoin_line_item_id=None,
            without_action_count=True,
            reason=f"fill_up_season_progress_{reason}" if reason else "fill_up_season_progress",
        )


def add_season_progress_and_give_reward(
    user_id: str,
    fincoin: int,
    fincoin_line_item_id: Optional[str] = None,
    without_action_count: bool = False,
    reason: Optional[str] = None,
):
    with Lock(f"season_progress_{user_id}"):
        _add_season_progress_and_give_reward_internal(
            user_id,
            fincoin,
            fincoin_line_item_id,
            without_action_count,
            reason,
        )


def _add_season_progress_and_give_reward_internal(
    user_id: str,
    fincoin: int,
    fincoin_line_item_id: Optional[str] = None,
    without_action_count: bool = False,
    reason: Optional[str] = None,
):
    progress = fincoin  # the unit of progress and target is fincoin
    if rewards := _add_season_progress(user_id, progress, fincoin_line_item_id, without_action_count, reason):
        user = User()
        user.load_by_id(user_id)
        for reward in rewards:
            if reward.reward_cents > 0:
                reward_params = RewardParam(
                    user=user,
                    amount=FincoinExchangeRate.to_fincoin(reward.reward_cents, CURRENCY_USD).to_target,
                    reason=reward.reason,
                )
                RewardUtil.give_reward(RewardType.FINCOIN, reward_params)
            if reward.payout_turn_amount > 0:
                add_payout_turn(user_id, reward.payout_turn_amount, reward.reason)

    season_progress = SeasonProgress.get_season_progress(user_id)
    assert season_progress is not None
    level_config = get_season_level_config(user_id, season_progress.level)
    data = {
        "startTime": season_progress.season_start_time,
        "endTime": season_progress.season_end_time,

        "level": season_progress.level,
        "fincoinProgress": season_progress.progress,
        "fincoinTarget": level_config.level_target,
        "reward": {
            "rewardType": RewardType.FINCOIN.value,
            "rewardAmount": FincoinExchangeRate.to_fincoin(
                level_config.level_reward_cents or 0,
                CURRENCY_USD,
            ).to_target,
            "monetaryValue": level_config.level_reward_cents or 0,
        },
        "presentationReward": {
            "rewardType": PRESENTATION_LEVEL_REWARD_DISPLAY_TYPE,
            "rewardAmount": PRESENTATION_LEVEL_REWARD_DISPLAY_AMOUNT,
            "monetaryValue": level_config.presentation_level_reward_cents or 0,
        },

        "milestones": [
            {
                "percentage": m.percentage,
                "isCompleted": season_progress.progress >= m.progress if m.progress else False,
                "milestoneName": m.milestone_name,
                "reward": {
                    "rewardType": RewardType.FINCOIN.value,
                    "rewardAmount": FincoinExchangeRate.to_fincoin(
                        m.reward_cents or 0,
                        CURRENCY_USD,
                    ).to_target,
                    "monetaryValue": m.reward_cents or 0,
                },
            }
            for m in level_config.milestone_configs or []
        ],
    }
    season_progress_channel = f"season_progress.{user_id}"

    publish(json.dumps(data), channel=season_progress_channel)


def _add_season_progress(
    user_id: str,
    progress: int,
    fincoin_line_item_id: Optional[str],
    without_action_count: bool,
    reason: Optional[str] = None,
) -> List[_Reward]:
    season_progress = SeasonProgress.get_season_progress(user_id)
    if not season_progress:
        current_start, current_end = SeasonProgress.get_season_times()
        start_level = get_season_start_level(user_id)

        # Calculate adjusted action_required for the starting level
        level_config = get_season_level_config(user_id, start_level)
        calculated_action_required = None
        if level_config and level_config.action_required and level_config.level_target:
            pl_calculation_result = calculate_pl_level(user_id)
            action_required_result = calculate_adjusted_action_required(
                user_id,
                level_config.action_required,
                pl_calculation_result.pl_level,
                pl_calculation_result.revenue
            )
            calculated_action_required = action_required_result.action_required

            # Log action_required calculation parameters
            _log_action_required_calculation(
                user_id,
                "season_start_calculation",
                pl_calculation_result,
                action_required_result,
                level_config.action_required,
                level_config.level_target,
            )

        season_data = {
            "user_id": user_id,
            "season_start_time": current_start,
            "season_end_time": current_end,
            "level": start_level,
            "progress": 0,
            "action_count": 0,
        }

        if calculated_action_required is not None:
            season_data["calculated_action_required"] = calculated_action_required

        season_progress = SeasonProgress(season_data)
        season_progress.create()

    level_before = season_progress.level
    progress_before = season_progress.progress

    final_level = level_before
    final_progress = progress_before + progress
    leveled_up = False
    rewards: List[_Reward] = []

    increase_action_count = 0 if without_action_count else 1

    level_config = get_season_level_config(user_id, final_level)
    if not level_config:
        return rewards

    while level_config.level_target and final_progress >= level_config.level_target:
        leveled_up = True
        final_level += 1
        final_progress -= level_config.level_target
        rewards.append(_Reward(
            reason=f"Season level up to {final_level}",
            reward_cents=level_config.level_reward_cents or 0,
            payout_turn_amount=level_config.level_reward_payout_turn_amount or 0,
        ))
        level_config = get_season_level_config(user_id, final_level)

    rewards.extend(_get_milestone_reward_cents(user_id, level_before, progress_before, final_level, final_progress))

    # Create line item
    SeasonProgressLineItem({
        "user_id": season_progress.user_id,
        "progress": progress,
        "progress_before": progress_before,
        "progress_after": final_progress,
        "level_before": level_before,
        "level_after": final_level,
        "target": level_config.level_target if level_config else None,
        "target_reached": leveled_up,
        "action_count": season_progress.action_count + increase_action_count,
        "season_start_time": season_progress.season_start_time,
        "season_end_time": season_progress.season_end_time,
        "action": SeasonProgressAction.ADD,
        "fincoin_line_item_id": fincoin_line_item_id,
        "reason": reason,
    }).create()

    if leveled_up:
        # Calculate adjusted action_required for the new level
        new_level_config = get_season_level_config(user_id, final_level)
        if new_level_config and new_level_config.action_required and new_level_config.level_target:
            pl_calculation_result = calculate_pl_level(user_id)
            action_required_result = calculate_adjusted_action_required(
                user_id,
                new_level_config.action_required,
                pl_calculation_result.pl_level,
                pl_calculation_result.revenue
            )
            adjusted_action_required = action_required_result.action_required

            # Log action_required calculation parameters
            _log_action_required_calculation(
                user_id,
                "level_up_calculation",
                pl_calculation_result,
                action_required_result,
                new_level_config.action_required,
                new_level_config.level_target,
            )
        else:
            adjusted_action_required = None

        update_data = {
            "progress": final_progress,
            "level": final_level,
            "action_count": 0,
        }

        if adjusted_action_required is not None:
            update_data["calculated_action_required"] = adjusted_action_required

        SeasonProgress.find_one_and_update(
            {
                "user_id": season_progress.user_id,
                "season_start_time": season_progress.season_start_time,
                "season_end_time": season_progress.season_end_time
            },
            {
                "$set": update_data,
                "$unset": {
                    "next_action_outcome": True,
                }
            },
        )
        return rewards
    else:
        # Just add progress if no level up
        SeasonProgress.find_one_and_update(
            {
                "user_id": season_progress.user_id,
                "season_start_time": season_progress.season_start_time,
                "season_end_time": season_progress.season_end_time
            },
            {
                "$inc": {
                    "progress": progress,
                    "action_count": increase_action_count,
                },
                "$unset": {
                    "next_action_outcome": True,
                }
            },
        )
        return rewards


def _get_milestone_reward_cents(
        user_id: str,
        level_before: int,
        progress_before: int,
        final_level: int,
        final_progress: int) -> List[_Reward]:
    rewards: List[_Reward] = []
    assert level_before <= final_level
    for i in range(level_before, final_level + 1):
        level_config = get_season_level_config(user_id, i)
        assert level_config.level_target is not None
        if i == level_before and i == final_level:
            # no level up
            start_progress = progress_before
            end_progress = final_progress
        elif i == level_before:
            start_progress = progress_before
            end_progress = level_config.level_target
        elif i == final_level:
            start_progress = 0
            end_progress = final_progress
        else:
            start_progress = 0
            end_progress = level_config.level_target

        for m in level_config.milestone_configs or []:
            if not m.progress:
                continue

            if start_progress < m.progress <= end_progress:
                rewards.append(_Reward(
                    reason=f"Season level {i} Milestone {m.milestone_name} reached",
                    reward_cents=m.reward_cents or 0,
                    payout_turn_amount=0,
                ))

    return rewards


def get_season_start_level(user_id: str) -> int:
    season_progress = SeasonProgress.find_one({
        "user_id": user_id,
    })
    return 1 if season_progress else 0


def get_fincoin_outcome(user_id: str) -> int:
    """
    there is random behavior in the calculating function
    use cache to avoid that the preview and the real outcome are different
    """
    season_progress = SeasonProgress.get_season_progress(user_id)
    if season_progress and season_progress.next_action_outcome:
        return season_progress.next_action_outcome  # this field will be reset after add_season_progress

    current_level = season_progress.level if season_progress else get_season_start_level(user_id)
    current_progress = season_progress.progress if season_progress else 0
    current_action_count = season_progress.action_count if season_progress else 0
    outcome = calculate_fincoin_outcome_range(user_id, current_level, current_progress, current_action_count).outcome
    if season_progress:
        SeasonProgress.find_one_and_update(
            {
                "user_id": season_progress.user_id,
                "season_start_time": season_progress.season_start_time,
                "season_end_time": season_progress.season_end_time
            },
            {
                "$set": {
                    "next_action_outcome": outcome,  # cache outcome
                }
            },
        )
    return outcome


_MIN_PROGRESS = 2


class FincoinOutcomeRange(NamedTuple):
    outcome: int
    lower_bound: int
    upper_bound: int


def calculate_fincoin_outcome_range(
        user_id: Optional[str],
        current_level: int,
        current_progress: int,
        current_action_count: int,
) -> FincoinOutcomeRange:
    season_config = get_season_level_config(user_id, current_level)
    assert season_config.level_target is not None
    assert season_config.action_required is not None

    if current_level == 0:
        # hard code for level 0
        assert season_config.action_required == 4
        if current_action_count == 0:
            return FincoinOutcomeRange(lower_bound=250, upper_bound=250, outcome=250)
        elif current_action_count == 1:
            return FincoinOutcomeRange(lower_bound=15, upper_bound=15, outcome=15)
        elif current_action_count == 2:
            return FincoinOutcomeRange(lower_bound=20, upper_bound=20, outcome=20)
        elif current_action_count == 3:
            return FincoinOutcomeRange(lower_bound=25, upper_bound=25, outcome=25)
        else:
            raise ValueError(f"Invalid action count for season level 0: {current_action_count}")

    # Use calculated_action_required if available (margin-based), otherwise fall back to base action_required
    action_required = season_config.action_required
    if user_id:
        season_progress = SeasonProgress.get_season_progress(user_id)
        if season_progress and season_progress.calculated_action_required:
            action_required = season_progress.calculated_action_required

    remaining_actions = action_required - current_action_count
    if remaining_actions <= 1:
        # Normally the remaining_actions will always be positive number.
        # However, if we change the config, and reduce the action_required number,
        # then remaining_actions may be less then 1 or even less then 0
        outcome = max(season_config.level_target - current_progress, 0)
        return FincoinOutcomeRange(lower_bound=outcome, upper_bound=outcome, outcome=outcome)

    lower_bound = _MIN_PROGRESS
    upper_bound = season_config.level_target - current_progress - _MIN_PROGRESS * (remaining_actions - 1)

    assert season_config.first_action_weight is not None
    if season_config.random_dist:
        # first_action_weight is used as a upper bound limit, so that it doesn't consume the progress to fast
        upper_bound = int(upper_bound * season_config.first_action_weight)
    else:
        if current_action_count == 0:
            factor = season_config.first_action_weight
        else:
            factor = 1 / ((current_action_count + 1) * _harmonic_progression(action_required))
        lower_bound = max(_MIN_PROGRESS, int(season_config.level_target * factor))
        assert season_config.variance_cap is not None
        upper_bound = min(upper_bound, int(lower_bound * (1 + season_config.variance_cap)))

    lower_bound = min(lower_bound, upper_bound)
    lower_bound = max(lower_bound, _MIN_PROGRESS)
    upper_bound = max(upper_bound, lower_bound)

    outcome = random.randint(lower_bound, upper_bound)
    return FincoinOutcomeRange(lower_bound=lower_bound, upper_bound=upper_bound, outcome=outcome)


@lru_cache(maxsize=None)
def _harmonic_progression(n: int) -> float:
    return sum(1 / i for i in range(1, n + 1))


def calculate_margin_thresholds(
    revenue: int,
    user_bucket: str,
    membership_month: int
) -> Tuple[int, int]:
    """
    Calculate margin thresholds for profit-loss level evaluation.

    Args:
        revenue: Total revenue in cents

    Returns:
        tuple[int, int]: (lower_threshold, upper_threshold) in cents
    """
    lower_threshold = int(MARGIN_LOWER_THRESHOLD * revenue)
    if user_bucket == "b":
        if membership_month == 0:
            lower_threshold = int(MARGIN_LOWER_THRESHOLD_B_M0 * revenue)
        else:
            lower_threshold = int(MARGIN_LOWER_THRESHOLD_B_OTHER * revenue)
    upper_threshold = int(min(15000, MARGIN_UPPER_THRESHOLD * revenue))  # 15000 cents = $150

    return lower_threshold, upper_threshold
