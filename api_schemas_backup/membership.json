{"openapi": "3.1.0", "info": {"title": "Blidz Membership API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/membership/get_membership": {"post": {"description": "Get membership info", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetMembershipResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "SubscriptionPlatform": {"title": "SubscriptionPlatform", "description": "An enumeration.", "enum": ["stripe", "revenue_cat_apple_app_store", "revenue_cat_google_play_store"], "type": "string"}, "Tier": {"title": "Tier", "description": "An enumeration.", "enum": ["blidz_plus", "blidz_plus_extra", "blidz_plus_ultra"], "type": "string"}, "MembershipStatus": {"title": "MembershipStatus", "description": "An enumeration.", "enum": ["non_member", "trial_member", "trial_member_no_renewal", "active_member", "active_member_no_renewal", "active_member_paused"], "type": "string"}, "MembershipBenefits": {"title": "MembershipBenefits", "description": "An enumeration.", "enum": ["free_shipping", "unlimited_cash_coupon_use"], "type": "string"}, "InvoiceStatus": {"title": "InvoiceStatus", "description": "An enumeration.", "enum": ["draft", "open", "paid", "uncollectible", "void"], "type": "string"}, "PaymentIntentStatus": {"title": "PaymentIntentStatus", "description": "An enumeration.", "enum": ["canceled", "processing", "requires_action", "requires_capture", "requires_confirmation", "requires_payment_method", "succeeded", "payment_failed"], "type": "string"}, "DtoPaymentStatus": {"title": "DtoPaymentStatus", "type": "object", "properties": {"attemptedAt": {"description": "Attempt time", "type": "integer"}, "amountDue": {"description": "Amount due", "type": "integer"}, "attemptCount": {"description": "Amount count", "type": "integer"}, "nextPaymentAttempt": {"description": "Next payment attempt time", "type": "integer"}, "invoiceId": {"description": "Invoide ID", "type": "string"}, "invoiceStatus": {"description": "Invoice status", "allOf": [{"$ref": "#/components/schemas/InvoiceStatus"}]}, "paymentIntentStatus": {"description": "Payment intent status", "allOf": [{"$ref": "#/components/schemas/PaymentIntentStatus"}]}, "lastPaymentError": {"description": "Last payment error", "type": "object"}, "lastFailedInvoiceUrl": {"description": "Last failed invoice url", "type": "string"}}}, "DtoMemberOrderCount": {"title": "DtoMemberOrderCount", "type": "object", "properties": {"freeProduct": {"description": "Free product count", "type": "integer"}, "paidProduct": {"description": "Paid product count", "type": "integer"}, "coinpack": {"description": "Coinpack count", "type": "integer"}, "cashpack": {"description": "Cashpack count", "type": "integer"}, "subscription": {"description": "Subscription count", "type": "integer"}}}, "DtoMixedCartOrder": {"title": "DtoMixedCartOrder", "type": "object", "properties": {"orderId": {"description": "The order ID", "type": "string"}, "name": {"description": "The product name", "type": "string"}, "image": {"description": "The product image", "type": "string"}, "finalCheckoutPrice": {"description": "The checkout price", "type": "integer"}, "reqId": {"description": "Req ID", "type": "string"}, "algoChannel": {"description": "Algo channel", "type": "string"}}}, "DtoCashEarned": {"title": "DtoCashEarned", "type": "object", "properties": {"free": {"description": "Free cash earned", "type": "integer"}, "paid": {"description": "Paid cash earned", "type": "integer"}}}, "DtoSpecialOffer": {"title": "DtoSpecialOffer", "type": "object", "properties": {"firstMonthFree": {"description": "Is first month free", "type": "boolean"}, "authenticationCount": {"description": "Count of authentication", "type": "integer"}}}, "Schedule": {"title": "Schedule", "description": "An enumeration.", "enum": ["daily", "weekly", "monthly", "yearly"], "type": "string"}, "DtoMembership": {"title": "DtoMembership", "type": "object", "properties": {"subscriptionPlatform": {"description": "Subscription platform", "allOf": [{"$ref": "#/components/schemas/SubscriptionPlatform"}]}, "userId": {"description": "User ID", "type": "string"}, "tier": {"description": "Tier", "allOf": [{"$ref": "#/components/schemas/Tier"}]}, "trialUsed": {"description": "Is trial used", "type": "boolean"}, "trialCount": {"description": "Trial used count", "type": "integer"}, "status": {"description": "The status of membership", "allOf": [{"$ref": "#/components/schemas/MembershipStatus"}]}, "createdAt": {"description": "The first created time of this membership", "type": "integer"}, "price": {"description": "The price of membership", "type": "integer"}, "benefits": {"description": "The benefit list", "type": "array", "items": {"$ref": "#/components/schemas/MembershipBenefits"}}, "startedAt": {"description": "Start time of current subscription", "type": "integer"}, "renewedAt": {"description": "Renew time", "type": "integer"}, "canceledAt": {"description": "Cancel time", "type": "integer"}, "endedAt": {"description": "End time", "type": "integer"}, "paymentStatus": {"description": "Payment status", "allOf": [{"$ref": "#/components/schemas/DtoPaymentStatus"}]}, "periodStart": {"description": "Current period start time", "type": "integer"}, "periodEnd": {"description": "Current period end time", "type": "integer"}, "trialStart": {"description": "Trial start time", "type": "integer"}, "trialEnd": {"description": "Trial end time", "type": "integer"}, "memberOrderCounts": {"description": "Stats for order during membership", "allOf": [{"$ref": "#/components/schemas/DtoMemberOrderCount"}]}, "mixedCartOrder": {"description": "The order bought with membership", "allOf": [{"$ref": "#/components/schemas/DtoMixedCartOrder"}]}, "memberSavingsAmount": {"description": "The saved money during membership", "type": "integer"}, "memberCashEarned": {"description": "Cash earned during membership", "allOf": [{"$ref": "#/components/schemas/DtoCashEarned"}]}, "memberCashStashed": {"description": "Cash stashed during membership", "type": "integer"}, "specialOffer": {"description": "The special offer of this membership", "allOf": [{"$ref": "#/components/schemas/DtoSpecialOffer"}]}, "freeCashOnTrialGiven": {"description": "Will user get free cash on trial", "type": "boolean"}, "lookupKey": {"description": "Lookup key", "type": "string"}, "schedule": {"description": "The schedule of membership", "allOf": [{"$ref": "#/components/schemas/Schedule"}]}}}, "GetMembershipResp": {"title": "GetMembershipResp", "type": "object", "properties": {"membership": {"description": "The membership info", "allOf": [{"$ref": "#/components/schemas/DtoMembership"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}