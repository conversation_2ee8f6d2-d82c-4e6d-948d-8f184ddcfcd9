{"openapi": "3.1.0", "info": {"title": "Blidz Mini_Game API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/mini-game/play": {"get": {"description": "Make Mystery Box available.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MysteryBoxResponse"}}}}}}}}}, "/mini-game/finish": {"get": {"description": "Open Mystery Box", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MysteryBoxResponse"}}}}}}}}}}, "components": {"schemas": {"MysteryBoxRequest": {"title": "MysteryBoxRequest", "type": "object", "properties": {"type": {"description": "The game type. Currently only 'mysterybox' is supported.", "type": "string"}, "chosenReward": {"description": "The chosen reward. Only used in finish endpoint.", "type": "string"}}, "required": ["type"]}, "MysteryBoxData": {"title": "MysteryBoxData", "type": "object", "properties": {"given": {"description": "When the Mystery Box was given.", "exclusiveMinimum": 0, "type": "integer"}, "expires": {"description": "When the Mystery Box expires.", "exclusiveMinimum": 0, "type": "integer"}, "started": {"description": "The started time of the Mystery Box.", "exclusiveMinimum": 0, "type": "integer"}, "inviteLink": {"description": "The invite link for this Mystery Box.", "type": "string"}, "inviter": {"description": "The user id of the inviter.", "type": "string"}, "fromInviteLink": {"description": "From which invite link the game was given.", "type": "string"}}}, "ActiveMysteryBoxData": {"title": "ActiveMysteryBoxData", "type": "object", "properties": {"given": {"description": "When the Mystery Box was given.", "exclusiveMinimum": 0, "type": "integer"}, "expires": {"description": "When the Mystery Box expires.", "exclusiveMinimum": 0, "type": "integer"}, "started": {"description": "The started time of the Mystery Box.", "exclusiveMinimum": 0, "type": "integer"}, "inviteLink": {"description": "The invite link for this Mystery Box.", "type": "string"}, "inviter": {"description": "The user id of the inviter.", "type": "string"}, "fromInviteLink": {"description": "From which invite link the game was given.", "type": "string"}, "id": {"description": "The reward id.", "type": "string"}, "type": {"description": "The type. Only 'mysterybox' supported.", "type": "string"}, "rewardType": {"description": "The reward type.", "type": "string"}, "name": {"description": "The reward name.", "type": "string"}, "image": {"description": "The reward image name.", "type": "string"}, "freeCoins": {"description": "If reward type is coin, how much coins to give.", "exclusiveMinimum": 0, "type": "integer"}, "freeCash": {"description": "If reward type is cash, how much cash to give.", "exclusiveMinimum": 0, "type": "integer"}}}, "MiniGame": {"title": "MiniGame", "type": "object", "properties": {"availableMiniGames": {"description": "The available mini games.", "type": "object", "additionalProperties": {"anyOf": [{"$ref": "#/components/schemas/MysteryBoxData"}, {"type": "array", "items": {"$ref": "#/components/schemas/MysteryBoxData"}}]}}, "activeMiniGames": {"description": "The active mini games.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/ActiveMysteryBoxData"}}, "gameCount": {"description": "The game count.", "type": "object", "additionalProperties": {"type": "integer", "exclusiveMinimum": 0}}}}, "MysteryBoxResponse": {"title": "MysteryBoxResponse", "type": "object", "properties": {"miniGame": {"description": "The minigame data.", "allOf": [{"$ref": "#/components/schemas/MiniGame"}]}}, "required": ["miniGame"]}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}