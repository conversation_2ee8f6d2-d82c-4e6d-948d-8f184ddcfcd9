{"openapi": "3.1.0", "info": {"title": "Blidz Dashboard API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/dashboard/watch": {"get": {"description": "Get video watch dashboard", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DashboardWatchResponse"}}}}}}}}}, "/dashboard/special_deals": {"get": {"description": "Get special deals dashboard", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DashboardSpecialDealsResponse"}}}}}}}}}, "/dashboard/subsidy_deals": {"get": {"description": "Get subsidy deals", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DashboardSubsidyDealsResponse"}}}}}}}}}, "/dashboard/brands": {"get": {"description": "List brands, or get a list of deals belonging to a brand.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DashboardBrandResponse"}}}}}}}}}, "/dashboard/stats": {"post": {"description": "Get dashboard stats", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DashboardStatsResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoDashboardWatchItemOwner": {"title": "DtoDashboardWatchItemOwner", "type": "object", "properties": {"userId": {"description": "The user ID of the user who uploaded this video", "type": "string"}, "avatar": {"description": "The avatar URL of the user who uploaded this video", "type": "string"}, "fullname": {"description": "The fullname of user", "type": "string"}, "playerName": {"description": "The player name of user", "type": "string"}}}, "DtoPriceData": {"title": "DtoPriceData", "type": "object", "properties": {"USD": {"description": "The price of USD", "minimum": 0, "type": "integer"}, "AUD": {"description": "The price of AUD", "minimum": 0, "type": "integer"}, "CAD": {"description": "The price of CAD", "minimum": 0, "type": "integer"}}}, "DtoDashboardWatchItemDealInfo": {"title": "DtoDashboardWatchItemDealInfo", "type": "object", "properties": {"dealId": {"description": "The id of deal", "type": "string"}, "productId": {"description": "The product id of deal", "type": "string"}, "name": {"description": "The name of deal", "type": "string"}, "image": {"description": "The image of deal", "type": "string"}, "btnPrice": {"description": "The BTN price of deal", "allOf": [{"$ref": "#/components/schemas/DtoPriceData"}]}, "reqId": {"description": "Request id", "type": "string"}}}, "LockCost": {"title": "LockCost", "type": "object", "properties": {"coins": {"title": "Coins", "default": 0, "type": "integer"}}}, "PriceDetail": {"title": "PriceDetail", "type": "object", "properties": {"coins": {"title": "Coins", "type": "integer"}, "price": {"title": "Price", "type": "integer"}, "shipping": {"title": "Shipping", "type": "integer"}, "shippingAsCoins": {"title": "Shippingascoins", "type": "integer"}}, "required": ["coins", "price", "shipping", "shippingAsCoins"]}, "PriceProperty": {"title": "PriceProperty", "type": "object", "properties": {"lockCost": {"$ref": "#/components/schemas/LockCost"}, "restricted": {"title": "Restricted", "type": "boolean"}, "price": {"title": "Price", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceDetail"}}}}, "PriceLevelSet": {"title": "PriceLevelSet", "type": "object", "properties": {"solo": {"$ref": "#/components/schemas/PriceProperty"}, "target": {"$ref": "#/components/schemas/PriceProperty"}, "join": {"$ref": "#/components/schemas/PriceProperty"}, "retail": {"$ref": "#/components/schemas/PriceProperty"}, "sale": {"$ref": "#/components/schemas/PriceProperty"}, "min": {"$ref": "#/components/schemas/PriceProperty"}, "max": {"$ref": "#/components/schemas/PriceProperty"}}}, "DealAttributes": {"title": "DealAttributes", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "optionCount": {"title": "Optioncount", "type": "integer"}}}, "DealClientListData": {"title": "DealClientListData", "type": "object", "properties": {"_id": {"pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "state": {"type": "string"}, "priceLevels": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "array", "items": {}}}}, "lowestPriceInUsdCent": {"type": "integer"}, "reallocatedPrice": {"type": "integer"}, "price": {"type": "integer"}, "fincoinPrice": {"type": "integer"}, "strikethroughPrice": {"type": "integer"}, "_productId": {"type": "string"}, "productName": {"type": "string"}, "image": {"type": "string"}, "rating": {"type": "number"}, "ratingCount": {"type": "integer"}, "reviews": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "retailPrice": {"type": "object", "additionalProperties": {"type": "integer"}}, "duoDeal": {"default": true, "type": "boolean"}, "gamified": {"default": false, "type": "boolean"}, "dealRace": {"default": false, "type": "boolean"}, "quantity": {"default": {}, "type": "object"}, "minLockCost": {"default": 1, "type": "integer"}, "priceCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "coinCostCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "imageKitEnabled": {"default": true, "type": "boolean"}, "lastUpdated": {"type": "integer"}, "duoDealEntries": {"type": "array", "items": {"type": "object"}}, "timerStart": {"type": "integer"}, "timerEnd": {"type": "integer"}, "endTime": {"type": "integer"}, "recentRacers": {"type": "object"}, "amountOfRacers": {"type": "integer"}, "usersWon": {"type": "object"}, "maxRacer": {"type": "integer"}, "leaderboardActivated": {"type": "boolean"}, "productLikes": {"type": "integer"}, "productCost": {"type": "integer"}, "supplierName": {"type": "string"}, "supplierStoreId": {"type": "string"}, "fromProducer": {"type": "string"}, "reqId": {"type": "string"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/DealAttributes"}}, "soldCount": {"type": "integer"}, "data": {"description": "Client List Data as a string.", "type": "string"}, "branded": {"type": "boolean"}, "startTime": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}, "hasAdultContent": {"type": "boolean"}}, "required": ["_id", "state", "_productId", "productName", "type"]}, "DtoDashboardWatchItem": {"title": "DtoDashboardWatchItem", "type": "object", "properties": {"feedItemId": {"description": "The unique id of feed item", "type": "string"}, "thumbnail": {"description": "Thumbnail of the video", "type": "string"}, "duration": {"description": "Duration of the video", "type": "integer"}, "videoUrl": {"description": "The video url if provided", "type": "string"}, "title": {"description": "The title of video if from youtube or product name if from CJ", "type": "string"}, "owner": {"description": "The user who uploaded the video", "allOf": [{"$ref": "#/components/schemas/DtoDashboardWatchItemOwner"}]}, "dealInfo": {"description": "The infomation of deal realated the video if provided", "allOf": [{"$ref": "#/components/schemas/DtoDashboardWatchItemDealInfo"}]}, "deal": {"description": "The deal realated the video if provided", "allOf": [{"$ref": "#/components/schemas/DealClientListData"}]}, "viewCount": {"description": "The view count of this video", "minimum": 0, "type": "integer"}, "videoSource": {"description": "Source of the video", "type": "string"}, "reqId": {"description": "Request id that used for algo.", "type": "string"}}}, "DashboardWatchResponse": {"title": "DashboardWatchResponse", "type": "object", "properties": {"items": {"description": "List of video items to show in dashboard.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DtoDashboardWatchItem"}}, "totalVideoCountForSpin": {"description": "The total count of videos user has to watch to get spin reward", "minimum": 0, "type": "integer"}, "videoCountLeftForSpin": {"description": "The count of videos left user has to watch to get spin reward", "minimum": 0, "type": "integer"}}}, "DashboardSpecialDealsResponse": {"title": "DashboardSpecialDealsResponse", "type": "object", "properties": {"deals": {"description": "List of special deals", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}, "title": {"description": "The title for the special deals in the dashboard.", "default": "Special deals", "type": "string"}}}, "DashboardSubsidyDealsRequest": {"title": "DashboardSubsidyDealsRequest", "type": "object", "properties": {"isCarousel": {"description": "Whether it's from carousel", "type": "boolean"}, "channel": {"description": "The channel defined in client", "type": "string"}, "googleCategoryLv1Id": {"description": "The google category lv1 id", "type": "string"}}}, "DashboardSubsidyDealsResponse": {"title": "DashboardSubsidyDealsResponse", "type": "object", "properties": {"deals": {"description": "List of subsidy deals", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "DtoBrand": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"name": {"description": "The brand name.", "type": "string"}, "iconUrl": {"description": "The icon url.", "type": "string"}, "imageUrl": {"description": "The brand image url.", "type": "string"}, "heading": {"description": "The brand heading.", "type": "string"}, "subheading": {"description": "The brand subheading.", "type": "string"}, "isSuperBrand": {"description": "Whether this is a super brand or not.", "default": false, "type": "boolean"}}, "required": ["name"]}, "DashboardBrandResponse": {"title": "DashboardBrandResponse", "type": "object", "properties": {"brands": {"description": "Brands", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/Dto<PERSON>rand"}}}}, "DtoStatsItemType": {"title": "DtoStatsItemType", "description": "An enumeration.", "enum": ["spin", "mystery_box", "key", "snake_turn", "candy_jam_turn", "fincoin", "my_duo_deal_entry", "checkin_calendar", "duo_deal_auto_completion_ticket"], "type": "string"}, "DtoStatsItem": {"title": "DtoStatsItem", "type": "object", "properties": {"itemType": {"description": "The type of item", "allOf": [{"$ref": "#/components/schemas/DtoStatsItemType"}]}, "itemCount": {"description": "The amount of item", "type": "integer"}, "itemCountDown": {"description": "The timer for the item", "type": "integer"}}}, "DashboardStatsResp": {"title": "DashboardStatsResp", "type": "object", "properties": {"stats": {"description": "The list of stats", "type": "array", "items": {"$ref": "#/components/schemas/DtoStatsItem"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}