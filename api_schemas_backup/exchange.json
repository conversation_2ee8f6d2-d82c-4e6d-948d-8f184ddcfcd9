{"openapi": "3.1.0", "info": {"title": "Blidz Exchange API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/exchange/virtual_items": {"post": {"description": "Exchange virtual items", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExchangeVirtualItemsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ExchangeVirtualItemsResponse"}}}}}}}}}}, "components": {"schemas": {"GameId": {"title": "GameId", "description": "An enumeration.", "enum": ["candy_jam", "snake"], "type": "string"}, "WalletType": {"title": "WalletType", "description": "Defines the wallet types we have. Used in the exchange system.", "enum": ["cash", "coin", "fincash", "fincoin", "fiat"], "type": "string"}, "VirtualType": {"title": "VirtualType", "description": "Virtual item type used in exchange system.", "enum": ["key", "mystery_box", "game_turn", "spin"], "type": "string"}, "ExchangeVirtualItemsRequest": {"title": "ExchangeVirtualItemsRequest", "type": "object", "properties": {"gameId": {"description": "When exchanging for game turns, the game id is required, i.e. 'snake' or 'candy_jam'.", "allOf": [{"$ref": "#/components/schemas/GameId"}]}, "fromType": {"description": "Which currency type to exchange from, one of 'cash', 'coin', 'fiat'", "default": "cash", "allOf": [{"$ref": "#/components/schemas/WalletType"}]}, "toType": {"description": "Exchange to one of 'mystery_box', 'key', 'spin', 'game_turn'", "allOf": [{"$ref": "#/components/schemas/VirtualType"}]}, "amount": {"description": "How many MBs, keys, spins or game turns to exchange.", "type": "integer"}}, "required": ["toType", "amount"]}, "ExchangeVirtualItemsResponse": {"title": "ExchangeVirtualItemsResponse", "type": "object", "properties": {"fromType": {"description": "Which currency type to exchange from", "default": "cash", "allOf": [{"$ref": "#/components/schemas/WalletType"}]}, "walletBalance": {"description": "The wallet balance (i.e. the from-type balance after exchange)", "type": "integer"}, "toType": {"description": "Exchange to one of 'mystery_box', 'key', 'spin', 'game_turn'", "allOf": [{"$ref": "#/components/schemas/VirtualType"}]}, "virtualItemBalance": {"description": "The balance of the given items after exchange.", "type": "integer"}, "gameId": {"description": "When exchanging game turns, the game id is mandatory.", "allOf": [{"$ref": "#/components/schemas/GameId"}]}}, "required": ["toType"]}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}