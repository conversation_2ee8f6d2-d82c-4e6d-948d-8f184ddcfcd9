{"openapi": "3.1.0", "info": {"title": "Blidz Payout Spin_Definitions API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/spin/v2.1/init_surplus": {"post": {"description": "Called when entering the spin wheel page, to initialize the numbers on the wheel.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DtoSpinWheelDataV2"}}}}}}}}}, "/spin/v2.1/confirm_reward": {"post": {"description": "Called after the 'spin' button is clicked, to give reward and update the numbers on the wheel", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DtoSpinWheelDataV2"}}}}}}}}}, "/spin/v2.1/spin_data": {"post": {"description": "Returns spin data for the currently logged-in user.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SpinDataRespV2"}}}}}}}}}, "/spin/v2/leaderboard": {"post": {"description": "Return top users who have gotten most spins", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SpinLeaderboardResp"}}}}}}}}}, "/spin/v3/init_surplus": {"post": {"description": "Called when entering the spin wheel page, to initialize the numbers on the wheel.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DtoSpinWheelDataV3"}}}}}}}}}, "/spin/v3/confirm_reward": {"post": {"description": "Called after the 'spin' button is clicked, to give reward and update the numbers on the wheel", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DtoSpinWheelDataV3"}}}}}}}}}, "/spin/v3/spin_data": {"post": {"description": "Returns spin data for the currently logged-in user.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SpinDataRespV3"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoSeasonProgressData": {"title": "DtoSeasonProgressData", "type": "object", "properties": {"nameOfSeason": {"description": "The name of season. It's based on month name for now", "type": "string"}, "seasonStartAt": {"description": "Start time of season", "type": "integer"}, "seasonEndAt": {"description": "End time of season", "type": "integer"}, "totalSeasonTarget": {"description": "Total target of season", "type": "integer"}, "totalSeasonFincoinTarget": {"description": "Total fincoin target of season", "type": "integer"}, "totalSeasonProgress": {"description": "Total progress of season", "type": "integer"}, "totalSeasonFincoinProgress": {"description": "Total fincoin progress of season", "type": "integer"}}}, "DtoMilestone": {"title": "DtoMilestone", "type": "object", "properties": {"percentage": {"type": "integer"}, "rewardAmount": {"type": "integer"}, "rewardFincoinAmount": {"type": "integer"}, "milestoneName": {"type": "string"}, "isCompleted": {"type": "boolean"}}, "required": ["percentage", "rewardAmount", "rewardFincoinAmount", "milestoneName", "isCompleted"]}, "DtoSpinDataV2": {"title": "DtoSpinDataV2", "type": "object", "properties": {"spinProgress": {"description": "The progress of current level", "type": "integer"}, "spinFincoinProgress": {"description": "The fincoin progress of current level", "type": "integer"}, "spinTarget": {"description": "The target of current level", "type": "integer"}, "spinFincoinTarget": {"description": "The fincoin target of current level", "type": "integer"}, "spinLevel": {"description": "The level of spin progress", "type": "integer"}, "spinRemaining": {"description": "The spins remaining for the user", "type": "integer"}, "nextFreeSpinTime": {"description": "The timestamp when the next free spin is given.", "type": "integer"}, "hasUsedSpin": {"description": "Whether user has used any spin", "type": "boolean"}, "seasonProgress": {"description": "The season data", "allOf": [{"$ref": "#/components/schemas/DtoSeasonProgressData"}]}, "spinProgressMilestones": {"description": "spin progress milestones", "type": "array", "items": {"$ref": "#/components/schemas/DtoMilestone"}}}}, "DtoTierPayoutLimit": {"title": "DtoTierPayoutLimit", "type": "object", "properties": {"moneyLimit": {"description": "How much money user can withdrew in current 'limit cycle'. 'Limit cycle' means:   for v2: non_member/yearly: calendar month, monthly/weekly: subscription period.   for v2.1, a cycle is one day", "type": "integer"}, "fincoinLimit": {"description": "How much fincoin user can withdrew in current 'limit cycle'. 'Limit cycle' means:   for v2: non_member/yearly: calendar month, monthly/weekly: subscription period.   for v2.1, a cycle is one day", "type": "integer"}, "countLimit": {"description": "How many withdrawals user has done in current 'limit cycle'. 'Limit cycle' means:   for v2: non_member/yearly: calendar month, monthly/weekly: subscription period.   for v2.1, a cycle is one day", "type": "integer"}}}, "DtoPayoutLimitData": {"title": "DtoPayoutLimitData", "type": "object", "properties": {"resetAt": {"description": "The reset time of limit. In milliseconds", "type": "integer"}, "currentCyclePayoutAmount": {"description": "How much money user has withdrew in current 'limit cycle'. 'Limit cycle' means: a cycle is one day", "type": "integer"}, "currentCyclePayoutLimitAmountAndCount": {"description": "How much money user can withdraw at most in current 'limit cycle'. 'Limit cycle' means: a cycle is one day. key-value is: tier -> limit, including a 'default' to represent non active member", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoTierPayoutLimit"}}, "skipUserLimitCheck": {"description": "Whether to skip the limit check for user for next payout", "type": "boolean"}}}, "DtoLastFailedPayout": {"title": "DtoLastFailedPayout", "type": "object", "properties": {"amount": {"description": "the amount", "type": "integer"}, "failedAt": {"description": "the timestamp when it failed", "type": "integer"}}}, "DtoPayoutTurnData": {"title": "DtoPayoutTurnData", "type": "object", "properties": {"payoutTurnAmount": {"description": "The amount of payout turn", "type": "integer"}, "maxPayoutTurnStack": {"description": "The stack of payout turn", "type": "integer"}, "monthlyPayoutCents": {"description": "The monthly payout in cents", "type": "integer"}, "monthlyPayoutCapCents": {"description": "The monthly payout cap in cents", "type": "integer"}, "basePayoutAmountCents": {"description": "The base payout amount in cents", "type": "integer"}}}, "DtoPayoutData": {"title": "DtoPayoutData", "type": "object", "properties": {"payoutBalance": {"description": "The current balance which user has", "type": "integer"}, "fincoinBalance": {"description": "The current fincoin balance which user has", "type": "integer"}, "payoutBalanceAvailable": {"description": "The available balance which user can payout", "type": "integer"}, "fincoinBalanceAvailable": {"description": "The available fincoin balance which user can payout", "type": "integer"}, "payoutBalanceCap": {"description": "The upper limit of payout balance can reach. Note for old users, this cap may be exceeded", "type": "integer"}, "payoutLimit": {"description": "The limit of payout", "allOf": [{"$ref": "#/components/schemas/DtoPayoutLimitData"}]}, "hasDoneNormalPayout": {"description": "Whether we have done normal payout to user", "type": "boolean"}, "specialPayoutsSent": {"description": "The list of special payouts we have sent to user", "type": "array", "items": {"type": "string"}}, "totalPaidOutAmount": {"description": "Total money we paid out to this user", "type": "integer"}, "lastFailedPayout": {"description": "Last failed payout before account reset", "allOf": [{"$ref": "#/components/schemas/DtoLastFailedPayout"}]}, "totalPayoutAttempt": {"description": "Total payout transactions recorded", "type": "integer"}, "payoutTurn": {"description": "The payout turn data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutTurnData"}]}, "payoutCooldownInterval": {"description": "The cooldown interval (milliseconds) for payout", "type": "integer"}}}, "DtoSpinWheelDataV2": {"title": "DtoSpinWheelDataV2", "type": "object", "properties": {"numbers": {"description": "The numbers on the wheel, including the outcome and surpluses", "type": "array", "items": {"type": "integer"}}, "fincoinNumbers": {"description": "The fincoin numbers on the wheel, including the outcome and surpluses", "type": "array", "items": {"type": "integer"}}, "outcome": {"description": "The outcome value", "type": "integer"}, "fincoinOutcome": {"description": "The fincoin outcome value", "type": "integer"}, "nextOutcome": {"description": "The next outcome value", "type": "integer"}, "nextFincoinOutcome": {"description": "The next fincoin outcome value", "type": "integer"}, "outcomeIndex": {"description": "The index (or segment) of the outcome in the numbers list", "type": "integer"}, "fincoinOutcomeIndex": {"description": "The index (or segment) of the fincoin outcome in the fincoin_numbers list", "type": "integer"}, "totalCash": {"description": "User total cash, including free and paid cash", "type": "integer"}, "spin": {"description": "The spin data", "allOf": [{"$ref": "#/components/schemas/DtoSpinDataV2"}]}, "payout": {"description": "The payout data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutData"}]}}}, "SpinDataRespV2": {"title": "SpinDataRespV2", "type": "object", "properties": {"spin": {"description": "The spin data", "allOf": [{"$ref": "#/components/schemas/DtoSpinDataV2"}]}, "payout": {"description": "The payout data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutData"}]}}}, "DtoSpinUser": {"title": "DtoSpinUser", "type": "object", "properties": {"userId": {"description": "User's id", "type": "string"}, "name": {"description": "User's full name if exist", "type": "string"}, "playerName": {"description": "User's player name", "type": "string"}, "avatar": {"description": "User's avatar if exist", "type": "string"}, "usedSpinCount": {"description": "User's used spin count", "type": "integer"}, "rankInLeaderboard": {"description": "User's current rank", "type": "integer"}, "prizePayoutAmountInLeaderboard": {"description": "Prize amount from position", "type": "integer"}}}, "DtoMonthLeaderboard": {"title": "DtoMonthLeaderboard", "type": "object", "properties": {"users": {"description": "List of users have highest amount of spin", "type": "array", "items": {"$ref": "#/components/schemas/DtoSpinUser"}}, "currentUser": {"description": "Ranking info of api request's user.", "allOf": [{"$ref": "#/components/schemas/DtoSpinUser"}]}}}, "SpinLeaderboardResp": {"title": "SpinLeaderboardResp", "type": "object", "properties": {"users": {"description": "List of users have highest amount of spin", "type": "array", "items": {"$ref": "#/components/schemas/DtoSpinUser"}}, "currentMonth": {"description": "Current month leaderboard", "allOf": [{"$ref": "#/components/schemas/DtoMonthLeaderboard"}]}, "lastMonth": {"description": "Current month leaderboard", "allOf": [{"$ref": "#/components/schemas/DtoMonthLeaderboard"}]}}}, "DtoSpinDataV3": {"title": "DtoSpinDataV3", "type": "object", "properties": {"spinRemaining": {"description": "The spins remaining for the user", "type": "integer"}, "nextFreeSpinTime": {"description": "The timestamp when the next free spin is given.", "type": "integer"}, "hasUsedSpin": {"description": "Whether user has used any spin", "type": "boolean"}}}, "DtoSpinWheelDataV3": {"title": "DtoSpinWheelDataV3", "type": "object", "properties": {"numbers": {"description": "The numbers on the wheel, including the outcome and surpluses", "type": "array", "items": {"type": "integer"}}, "fincoinNumbers": {"description": "The fincoin numbers on the wheel, including the outcome and surpluses", "type": "array", "items": {"type": "integer"}}, "outcome": {"description": "The outcome value", "type": "integer"}, "fincoinOutcome": {"description": "The fincoin outcome value", "type": "integer"}, "nextOutcome": {"description": "The next outcome value", "type": "integer"}, "nextFincoinOutcome": {"description": "The next fincoin outcome value", "type": "integer"}, "outcomeIndex": {"description": "The index (or segment) of the outcome in the numbers list", "type": "integer"}, "fincoinOutcomeIndex": {"description": "The index (or segment) of the fincoin outcome in the fincoin_numbers list", "type": "integer"}, "spin": {"description": "The spin data", "allOf": [{"$ref": "#/components/schemas/DtoSpinDataV3"}]}, "payout": {"description": "The payout data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutData"}]}}}, "SpinDataRespV3": {"title": "SpinDataRespV3", "type": "object", "properties": {"spin": {"description": "The spin data", "allOf": [{"$ref": "#/components/schemas/DtoSpinDataV3"}]}, "payout": {"description": "The payout data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutData"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}