{"openapi": "3.1.0", "info": {"title": "Blidz Fincoin_Wallet API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/fincoin_wallet/get_fincoin_wallet": {"post": {"description": "Get the fincoin wallet info", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetFincoinWalletResp"}}}}}}}}}, "/fincoin_wallet/get_fincoin_line_items": {"post": {"description": "Get the fincoin line items", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFincoinLineItemsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetFincoinLineItemsResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "GetFincoinWalletResp": {"title": "GetFincoinWalletResp", "type": "object", "properties": {"amount": {"description": "The amount in wallet", "type": "integer"}, "totalAddedAmount": {"description": "The total added amount", "type": "integer"}}}, "DtoFincoinLineItemAction": {"title": "DtoFincoinLineItemAction", "description": "An enumeration.", "enum": ["add", "spend", "remove", "expire"], "type": "string"}, "GetFincoinLineItemsReq": {"title": "GetFincoinLineItemsReq", "type": "object", "properties": {"page": {"description": "The current page number", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}, "actionFilter": {"description": "The action to filter on line items", "allOf": [{"$ref": "#/components/schemas/DtoFincoinLineItemAction"}]}}}, "DtoFincoinLineItem": {"title": "DtoFincoinLineItem", "type": "object", "properties": {"created": {"description": "The created time", "type": "integer"}, "action": {"description": "The action", "allOf": [{"$ref": "#/components/schemas/DtoFincoinLineItemAction"}]}, "reason": {"description": "The reason of this line item", "type": "string"}, "amount": {"description": "The amount of this line item", "type": "integer"}, "beforeBalance": {"description": "The wallet balance before this line item happens", "type": "integer"}, "afterBalance": {"description": "The wallet balance after this line item happens", "type": "integer"}}}, "GetFincoinLineItemsResp": {"title": "GetFincoinLineItemsResp", "type": "object", "properties": {"lineItems": {"description": "The line items", "type": "array", "items": {"$ref": "#/components/schemas/DtoFincoinLineItem"}}, "total": {"description": "The total count that fits the filter in request", "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}