{"openapi": "3.1.0", "info": {"title": "Blidz Ai API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/ai/chat": {"post": {"description": "Chat with agent", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/ai/list_chat": {"post": {"description": "List chat, order by the timestamp of last message in descending order", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListChatReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListChatResp"}}}}}}}}}, "/ai/get_chat": {"post": {"description": "Get chat by chat id", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetChatReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetChatResp"}}}}}}}}}, "/ai/get_prompts": {"post": {"description": "Get all prompts for all agents", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetPromptsResp"}}}}}}}}}}, "components": {"schemas": {"DtoClientAgentType": {"title": "DtoClientAgentType", "description": "An enumeration.", "enum": ["shop", "earn", "customer"], "type": "string"}, "ChatReq": {"title": "ChatReq", "type": "object", "properties": {"chatId": {"description": "The chat id", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "prompt": {"description": "The prompt", "type": "string"}, "agentType": {"description": "The agent type", "allOf": [{"$ref": "#/components/schemas/DtoClientAgentType"}]}}, "required": ["prompt", "agentType"]}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "ListChatReq": {"title": "ListChatReq", "type": "object", "properties": {"page": {"description": "The page number, starting from 1", "default": 1, "type": "integer"}, "pageSize": {"description": "The page size", "default": 20, "type": "integer"}}}, "DtoChatSummary": {"title": "DtoChatSummary", "type": "object", "properties": {"chatId": {"description": "The chat id", "type": "string"}, "title": {"description": "The title", "type": "string"}, "createdAt": {"description": "The created timestamp of this chat", "type": "integer"}, "updatedAt": {"description": "The timestamp of last message of this chat", "type": "integer"}}}, "ListChatResp": {"title": "ListChatResp", "type": "object", "properties": {"chatSummaries": {"description": "The chat summaries", "type": "array", "items": {"$ref": "#/components/schemas/DtoChatSummary"}}, "total": {"description": "The total number of chats", "type": "integer"}}}, "GetChatReq": {"title": "GetChatReq", "type": "object", "properties": {"chatId": {"description": "The chat id", "type": "string"}}, "required": ["chatId"]}, "DtoAIRole": {"title": "DtoAIRole", "description": "An enumeration.", "enum": ["assistant", "user"], "type": "string"}, "DtoChatHistory": {"title": "DtoChatHistory", "type": "object", "properties": {"role": {"description": "The role", "allOf": [{"$ref": "#/components/schemas/DtoAIRole"}]}, "mainContent": {"description": "The main content without thinking part", "type": "string"}, "createdAt": {"description": "The created timestamp of this message", "type": "integer"}}}, "GetChatResp": {"title": "GetChatResp", "type": "object", "properties": {"title": {"description": "The title", "type": "string"}, "chatId": {"description": "The chat id", "type": "string"}, "createdAt": {"description": "The created timestamp of this chat", "type": "integer"}, "updatedAt": {"description": "The timestamp of last message of this chat", "type": "integer"}, "chat": {"description": "The chat", "type": "array", "items": {"$ref": "#/components/schemas/DtoChatHistory"}}}}, "EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoPrompt": {"title": "DtoPrompt", "type": "object", "properties": {"title": {"description": "The title", "type": "string"}, "prompt": {"description": "The prompt", "type": "string"}}, "required": ["title", "prompt"]}, "DtoPromptsOneAgent": {"title": "DtoPromptsOneAgent", "type": "object", "properties": {"agentType": {"description": "The agent type", "allOf": [{"$ref": "#/components/schemas/DtoClientAgentType"}]}, "prompts": {"description": "The prompts", "type": "array", "items": {"$ref": "#/components/schemas/DtoPrompt"}}}}, "GetPromptsResp": {"title": "GetPromptsResp", "type": "object", "properties": {"allPrompts": {"description": "All prompts for all agents", "type": "array", "items": {"$ref": "#/components/schemas/DtoPromptsOneAgent"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}