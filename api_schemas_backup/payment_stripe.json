{"openapi": "3.1.0", "info": {"title": "Blidz Payment Stripe_Definitions API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/payment/stripe/create_payment_intent": {"post": {"description": "Create stripe payment intent", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentIntentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CreatePaymentIntentResponse"}}}}}}}}}, "/payment/stripe/create_subscription": {"post": {"description": "Create stripe subscription", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CreateSubscriptionResponse"}}}}}}}}}, "/payment/stripe/create_checkout_session": {"post": {"description": "Create stripe checkout session", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStripeCheckoutSessionReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CreateStripeCheckoutSessionResp"}}}}}}}}}, "/payment/stripe/confirm_order_status": {"post": {"description": "Confirm order status after stripe payment", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmOrderStatusRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ConfirmOrderStatusResponse"}}}}}}}}}, "/payment/stripe/retrieve_payment_methods": {"post": {"description": "Retrieve payment methods of stripe", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PaymentMethodsResponse"}}}}}}}}}, "/payment/stripe/set_default_payment_method": {"post": {"description": "Set default payment method", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetDefaultPaymentMethodRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SetDefaultPaymentMethodResponse"}}}}}}}}}, "/payment/stripe/create_setup_intent": {"post": {"description": "Create setup intent", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSetupIntentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CreateSetupIntentResponse"}}}}}}}}}, "/payment/stripe/authenticate_special_offer": {"post": {"description": "Authenticate membership special offer", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticateSpecialOfferRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/AuthenticateSpecialOfferResponse"}}}}}}}}}, "/payment/stripe/reserve_funds": {"post": {"description": "Payment hold", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveFundsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ReserveFundsResponse"}}}}}}}}}, "/payment/stripe/verify_setup_intent": {"post": {"description": "Verify setup intent", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifySetupIntentRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/VerifySetupIntentResponse"}}}}}}}}}, "/payment/stripe/get_backup_card_rewards": {"post": {"description": "Get backup card rewards", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetBackupCardRewardsResp"}}}}}}}}}, "/payment/stripe/get_payment_holds_info": {"post": {"description": "Get payment holds info", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPaymentHoldsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetPaymentHoldsResp"}}}}}}}}}, "/payment/stripe/preview_upgrading_subscription": {"post": {"description": "Preview upgrading subscription", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PreviewUpgradingSubscriptionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PreviewUpgradingSubscriptionResp"}}}}}}}}}, "/payment/stripe/upgrade_subscription": {"post": {"description": "Upgrade subscription", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpgradeSubscriptionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/UpgradeSubscriptionResponse"}}}}}}}}}, "/payment/stripe/subscription_config": {"post": {"description": "Get config of subscription", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SubscriptionConfigResponse"}}}}}}}}}}, "components": {"schemas": {"Options": {"title": "Options", "type": "object", "properties": {"skuId": {"description": "SKU ID", "type": "string"}, "attributes": {"description": "The attribute of sku. For example: {\"color\": \"red\"}", "type": "object"}, "priceLevel": {"description": "Price level, one of min, max, sale, target, solo, join. In Meituan model, when duodeal is team_buy, price_level must be none.", "type": "string"}}}, "Item": {"title": "<PERSON><PERSON>", "type": "object", "properties": {"type": {"description": "should be 'deal', because other types of items are all deprecated", "type": "string"}, "id": {"description": "should be deal id, because other types of items are all deprecated", "type": "string"}, "currency": {"description": "Currency of payment", "type": "string"}, "options": {"description": "Options", "allOf": [{"$ref": "#/components/schemas/Options"}]}, "shippingOptionCode": {"description": "The shipping option code. If omitted, use default blidz shipping", "type": "string"}, "buySource": {"description": "Whether user place orders in app or via an external link, one of internal, external", "type": "string"}, "duoDealEntryId": {"description": "The duo deal entry ID if user is joining a duo deal", "type": "string"}, "reqId": {"description": "Request Id", "type": "string"}, "shoppingCartItemId": {"description": "The ID of shopping cart item", "type": "string"}, "channel": {"description": "Original deal channel", "type": "string"}, "allowAutoCompleteByTicket": {"description": "Whether allow auto complete by ticket", "type": "boolean"}}}, "Address": {"title": "Address", "type": "object", "properties": {"email": {"description": "Email", "type": "string"}, "firstName": {"description": "First name", "type": "string"}, "lastName": {"description": "Last name", "type": "string"}, "street": {"description": "Street address", "type": "string"}, "city": {"description": "City", "type": "string"}, "zip": {"description": "Zip code", "type": "string"}, "state": {"description": "State", "type": "string"}, "country": {"description": "Country", "type": "string"}, "phoneNumber": {"description": "Phone number", "type": "string"}, "recipient": {"description": "Recipient", "type": "string"}, "line2": {"description": "Address line 2", "type": "string"}, "timeZone": {"description": "Timezone", "type": "string"}}}, "Addresses": {"title": "Addresses", "type": "object", "properties": {"shipping": {"description": "Shipping address", "allOf": [{"$ref": "#/components/schemas/Address"}]}, "billing": {"description": "Billing address", "allOf": [{"$ref": "#/components/schemas/Address"}]}}}, "UserPlatform": {"title": "UserPlatform", "description": "An enumeration.", "enum": ["android_web", "desktop_web", "ios_web", "android_app", "ios_app", "unknown"], "type": "string"}, "CreatePaymentIntentRequest": {"title": "CreatePaymentIntentRequest", "type": "object", "properties": {"currency": {"description": "Currency of payment.", "type": "string"}, "items": {"description": "Detail of payment.", "type": "array", "items": {"$ref": "#/components/schemas/Item"}}, "email": {"description": "User email if user not logged in.", "type": "string"}, "addresses": {"description": "The billing/shipping addresses.", "allOf": [{"$ref": "#/components/schemas/Addresses"}]}, "paymentMethodType": {"description": "Stored in the user.defaultPaymentMethod.", "type": "string"}, "paymentMethodId": {"description": "The stripe payment method id", "type": "string"}, "userPlatform": {"description": "The platform used.", "allOf": [{"$ref": "#/components/schemas/UserPlatform"}]}, "subscriptionId": {"description": "The stripe subscription ID", "type": "string"}, "channel": {"description": "Algo channel defined by client", "type": "string"}, "waitUserUploadVideo": {"description": "User is supposed to upload video for this order", "type": "boolean"}, "paymentHoldId": {"description": "The payment hold ID if this order is related to a payment hold", "type": "string"}}, "required": ["currency", "items"]}, "CreatePaymentIntentResponse": {"title": "CreatePaymentIntentResponse", "type": "object", "properties": {"csrfToken": {"description": "CSRF Token", "type": "string"}, "loginRequired": {"description": "Whether login is required or not.", "type": "boolean"}, "user": {"description": "The user model.", "type": "object"}, "email": {"description": "Only returned when login_required is True.", "type": "string"}, "status": {"description": "Response status.", "type": "integer"}, "type": {"description": "Payment intent type, e.g. success.", "type": "string"}, "orderId": {"description": "The order ID.", "type": "string"}, "reason": {"description": "Payment intent created.", "type": "string"}, "code": {"description": "Error code", "type": "string"}, "message": {"description": "Error message", "type": "string"}, "paymentIntentId": {"description": "Payment intent id.", "type": "string"}, "paymentMethodId": {"description": "The payment method id used for the payment.", "type": "string"}, "stripeError": {"description": "If there's an error with creating the Payment Intent.", "type": "object"}, "clientSecret": {"description": "The client secret for the payment intent. For new clients this is the encrypted data.", "anyOf": [{"type": "string"}, {"type": "object"}]}}}, "DtoRewardType": {"title": "DtoRewardType", "description": "An enumeration.", "enum": ["cash"], "type": "string"}, "CreateSubscriptionRequest": {"title": "CreateSubscriptionRequest", "type": "object", "properties": {"paymentMethodId": {"description": "The payment method id to use for the subscription.", "type": "string"}, "priceId": {"description": "The price id to subscribe to.", "type": "string"}, "rewards": {"description": "Rewards for authentication", "type": "array", "items": {"$ref": "#/components/schemas/DtoRewardType"}}, "occasion": {"description": "The occasion of subscription. It can be any string. But if it matches any value in PredefinedSubscriptionOccasion, then we may need to do some extra logic", "type": "string"}}}, "DtoPaymentMethod": {"title": "DtoPaymentMethod", "type": "object", "properties": {"paymentMethodId": {"description": "Payment method ID", "type": "string"}, "type": {"description": "The type of payment method, currently only 'card'", "type": "string"}, "brand": {"description": "The brand of card, for example: 'visa'", "type": "string"}, "expMonth": {"description": "The month of expiry", "type": "integer"}, "expYear": {"description": "The year of expiry", "type": "integer"}, "last4": {"description": "The last 4 digits of card number", "type": "string"}, "funding": {"description": "The funding of card: prepaid, debit, credit", "type": "string"}, "fingerprint": {"description": "The fingerprint of card", "type": "string"}, "email": {"description": "The email of the payment method", "type": "string"}, "cashtag": {"description": "The cashapp tag of the payment method", "type": "string"}, "buyerId": {"description": "The buyer id of the payment method", "type": "string"}}}, "CreateSubscriptionResponse": {"title": "CreateSubscriptionResponse", "type": "object", "properties": {"subscriptionId": {"description": "The subscription ID.", "type": "string"}, "clientSecret": {"description": "The Subscription's latest invoice's payment intent client secret. For new clients it's the encrypted data.", "anyOf": [{"type": "string"}, {"type": "object"}]}, "paymentIntentSecret": {"description": "Whether the client secret is from payment intent or not.", "default": false, "type": "boolean"}, "setupIntentSecret": {"description": "Whether the client secret is from setup intent or not.", "default": false, "type": "boolean"}, "paymentMethod": {"description": "The payment method for this subscription.", "allOf": [{"$ref": "#/components/schemas/DtoPaymentMethod"}]}}, "required": ["subscriptionId"]}, "PaymentMethodType": {"title": "PaymentMethodType", "description": "An enumeration.", "enum": ["acss_debit", "affirm", "afterpay_clearpay", "alipay", "alma", "amazon_pay", "au_becs_debit", "bacs_debit", "bancontact", "blik", "boleto", "card", "card_present", "cashapp", "crypto", "customer_balance", "eps", "fpx", "giropay", "grabpay", "ideal", "interac_present", "kakao_pay", "klarna", "konbini", "kr_card", "link", "mobilepay", "multibanco", "naver_pay", "oxxo", "p24", "payco", "paynow", "paypal", "pix", "promptpay", "revolut_pay", "samsung_pay", "sepa_debit", "sofort", "swish", "twint", "us_bank_account", "wechat_pay", "zip"], "type": "string"}, "CreateStripeCheckoutSessionReq": {"title": "CreateStripeCheckoutSessionReq", "type": "object", "properties": {"items": {"description": "Line items. If `items` is set but `join_blidz_plus` is not set, then this is a one-time payment. If `join_blidz_plus` is set, then this is a subscription. And if `items` is set too, the purchase can be done in one transaction If neither `items` nor `join_blidz_plus` is set, then this is to collect payment method", "type": "array", "items": {"$ref": "#/components/schemas/Item"}}, "joinBlidzPlus": {"description": "Whether user wants to join blidz+. If `items` is set but `join_blidz_plus` is not set, then this is a one-time payment. If `join_blidz_plus` is set, then this is a subscription. And if `items` is set too, the purchase can be done in one transaction If neither `items` nor `join_blidz_plus` is set, then this is to collect payment method", "type": "boolean"}, "currency": {"description": "Currency of payment. If either `items` or `join_blidz_plus` is set, then this field is required", "type": "string"}, "email": {"description": "User email if user not logged in.", "type": "string"}, "addresses": {"description": "The billing/shipping addresses.", "allOf": [{"$ref": "#/components/schemas/Addresses"}]}, "channel": {"description": "Algo channel defined by client", "type": "string"}, "waitUserUploadVideo": {"description": "User is supposed to upload video for this order", "type": "boolean"}, "paymentHoldId": {"description": "The payment hold ID if this order is related to a payment hold", "type": "string"}, "paymentMethodTypes": {"description": "Client can specify the payment method types to use", "type": "array", "items": {"$ref": "#/components/schemas/PaymentMethodType"}}}}, "CreateStripeCheckoutSessionResp": {"title": "CreateStripeCheckoutSessionResp", "type": "object", "properties": {"loginRequired": {"description": "Whether login is required or not.", "type": "boolean"}, "user": {"description": "The user model.", "type": "object"}, "email": {"description": "Only returned when login_required is True.", "type": "string"}, "sessionId": {"description": "The stripe checkout session ID", "type": "string"}, "setupIntentId": {"description": "The stripe setup intent ID", "type": "string"}, "checkoutSessionUrl": {"description": "The url of stripe checkout session", "type": "string"}, "successUrl": {"description": "The success url for stripe checkout session", "type": "string"}, "cancelUrl": {"description": "The cancel url for stripe checkout session", "type": "string"}}}, "ConfirmOrderStatusRequest": {"title": "ConfirmOrderStatusRequest", "type": "object", "properties": {"paymentIntentId": {"description": "The payment intent id.", "type": "string"}, "checkoutSessionId": {"description": "The stripe checkout session id", "type": "string"}}}, "ConfirmOrderStatusResponse": {"title": "ConfirmOrderStatusResponse", "type": "object", "properties": {"order": {"description": "The order", "type": "object"}}}, "EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "PaymentMethodsResponse": {"title": "PaymentMethodsResponse", "type": "object", "properties": {"paymentMethods": {"description": "List of payment methods for the customer.", "type": "array", "items": {"$ref": "#/components/schemas/DtoPaymentMethod"}}}}, "SetDefaultPaymentMethodRequest": {"title": "SetDefaultPaymentMethodRequest", "type": "object", "properties": {"paymentMethod": {"description": "The payment method id to set as the default.", "type": "string"}}, "required": ["paymentMethod"]}, "SetDefaultPaymentMethodResponse": {"title": "SetDefaultPaymentMethodResponse", "type": "object", "properties": {"paymentMethod": {"description": "The new default payment method.", "allOf": [{"$ref": "#/components/schemas/DtoPaymentMethod"}]}}}, "CreateSetupIntentRequest": {"title": "CreateSetupIntentRequest", "type": "object", "properties": {"email": {"description": "User email if user not logged in.", "type": "string"}}}, "CreateSetupIntentResponse": {"title": "CreateSetupIntentResponse", "type": "object", "properties": {"clientSecret": {"description": "The client secret for the setup intent. New clients get the encrypted data.", "anyOf": [{"type": "string"}, {"type": "object"}]}, "setupIntentId": {"description": "The setup intent id", "type": "string"}, "customerId": {"description": "The customer id just created", "type": "string"}, "ephemeralSecret": {"description": "The key used by PaymentSheet in our app", "type": "string"}}}, "AuthenticateSpecialOfferRequest": {"title": "AuthenticateSpecialOfferRequest", "type": "object", "properties": {"currency": {"description": "The currency in which to reserve the funds.", "type": "string"}, "paymentMethodId": {"description": "The payment method id for the user.", "type": "string"}, "priceId": {"description": "The price id to subscribe to.", "type": "string"}}, "required": ["currency"]}, "AuthenticateSpecialOfferResponse": {"title": "AuthenticateSpecialOfferResponse", "type": "object", "properties": {"paymentMethod": {"description": "The payment method if verification succeeds.", "allOf": [{"$ref": "#/components/schemas/DtoPaymentMethod"}]}}}, "PaymentHoldType": {"title": "PaymentHoldType", "description": "An enumeration.", "enum": ["hold_subscription", "verify_card"], "type": "string"}, "PaymentHoldSource": {"title": "PaymentHoldSource", "description": "An enumeration.", "enum": ["subscribe_payout", "subscribe_mixed_checkout", "subscribe_standalone", "order_wait_upload_video", "add_new_card"], "type": "string"}, "ReserveFundsRequest": {"title": "ReserveFundsRequest", "type": "object", "properties": {"currency": {"description": "The currency in which to reserve the funds.", "default": "USD", "type": "string"}, "addresses": {"description": "The billing/shipping addresses.", "allOf": [{"$ref": "#/components/schemas/Addresses"}]}, "paymentMethodType": {"description": "Stored in the user.defaultPaymentMethod.", "type": "string"}, "reason": {"description": "Reason to reserve fund to get amount.", "type": "string"}, "priceId": {"description": "The price ID to get the amount from.", "type": "string"}, "paymentMethodId": {"description": "The payment method id for the user.", "type": "string"}, "holdType": {"description": "The payment hold type", "allOf": [{"$ref": "#/components/schemas/PaymentHoldType"}]}, "source": {"description": "The source/scenario of the hold", "allOf": [{"$ref": "#/components/schemas/PaymentHoldSource"}]}, "savedCard": {"description": "Is the card previously saved", "type": "boolean"}}}, "ReserveFundsResponse": {"title": "ReserveFundsResponse", "type": "object", "properties": {"paymentIntentId": {"description": "The payment intent Id.", "type": "string"}, "paymentMethodId": {"description": "The payment method id if it exists.", "type": "string"}, "paymentHoldId": {"description": "The ID of payment hold document", "type": "string"}}}, "VerifySetupIntentRequest": {"title": "VerifySetupIntentRequest", "type": "object", "properties": {"setupIntentId": {"description": "The setup intent to confirm.", "type": "string"}}, "required": ["setupIntentId"]}, "VerifySetupIntentResponse": {"title": "VerifySetupIntentResponse", "type": "object", "properties": {"errorCode": {"description": "In some cases we need to tell client error happened without actually raise exception", "type": "string"}, "paymentMethod": {"description": "The payment method if verification succeeds.", "allOf": [{"$ref": "#/components/schemas/DtoPaymentMethod"}]}, "paymentMethodId": {"description": "The payment method id if verification succeeds.", "type": "string"}, "fundingType": {"description": "The funding type of card. Can be credit, debit, prepaid, or unknown", "type": "string"}, "brand": {"description": "The network name of the card. Eg: 'Visa', 'Master'..", "type": "string"}}}, "DtoBackupCardRewardType": {"title": "DtoBackupCardRewardType", "description": "An enumeration.", "enum": ["spin", "cash"], "type": "string"}, "DtoBackupCardRewardInfo": {"title": "DtoBackupCardRewardInfo", "type": "object", "properties": {"rewardType": {"description": "The reward type", "allOf": [{"$ref": "#/components/schemas/DtoBackupCardRewardType"}]}, "rewardAmount": {"description": "The reward amount", "type": "integer"}, "received": {"description": "Whether the reward has been received", "type": "boolean"}}}, "GetBackupCardRewardsResp": {"title": "GetBackupCardRewardsResp", "type": "object", "properties": {"creditCardReward": {"description": "The reward info for credit card", "allOf": [{"$ref": "#/components/schemas/DtoBackupCardRewardInfo"}]}, "generalReward": {"description": "The generate reward for any card", "allOf": [{"$ref": "#/components/schemas/DtoBackupCardRewardInfo"}]}}}, "DtoPaymentHoldStatus": {"title": "DtoPaymentHoldStatus", "description": "An enumeration.", "enum": ["holding", "released"], "type": "string"}, "GetPaymentHoldsReq": {"title": "GetPaymentHoldsReq", "type": "object", "properties": {"status": {"description": "Status of the hold", "allOf": [{"$ref": "#/components/schemas/DtoPaymentHoldStatus"}]}, "source": {"description": "The source/scenario of the hold", "allOf": [{"$ref": "#/components/schemas/PaymentHoldSource"}]}}, "required": ["source"]}, "GetPaymentHoldsResp": {"title": "GetPaymentHoldsResp", "type": "object", "properties": {"count": {"description": "Count of payment hold with the requested source ", "type": "integer"}}}, "PreviewUpgradingSubscriptionRequest": {"title": "PreviewUpgradingSubscriptionRequest", "type": "object", "properties": {"priceId": {"description": "The price id to subscribe to.", "type": "string"}}, "required": ["priceId"]}, "PreviewUpgradingSubscriptionResp": {"title": "PreviewUpgradingSubscriptionResp", "type": "object", "properties": {"currentPriceId": {"description": "The current price id", "type": "string"}, "newPriceId": {"description": "The new price id", "type": "string"}, "additionalPay": {"description": "The additional amount need to pay", "type": "integer"}}, "required": ["currentPriceId", "newPriceId"]}, "UpgradeSubscriptionRequest": {"title": "UpgradeSubscriptionRequest", "type": "object", "properties": {"priceId": {"description": "The price id to subscribe to.", "type": "string"}, "paymentMethodId": {"description": "The payment method id for the user.", "type": "string"}, "occasion": {"description": "The occasion of subscription. It can be any string. But if it matches any value in PredefinedSubscriptionOccasion, then we may need to do some extra logic", "type": "string"}}, "required": ["priceId"]}, "UpgradeSubscriptionResponse": {"title": "UpgradeSubscriptionResponse", "type": "object", "properties": {"subscriptionId": {"description": "The subscription ID.", "type": "string"}, "paymentMethod": {"description": "The payment method.", "allOf": [{"$ref": "#/components/schemas/DtoPaymentMethod"}]}}}, "_DtoCurrencyOptions": {"title": "_DtoCurrencyOptions", "type": "object", "properties": {"customUnitAmount": {"description": "When set, provides configuration for the amount to be adjusted by the customer during Checkout Sessions and Payment Links.", "type": "object"}, "taxBehavior": {"description": "Specifies whether the price is considered inclusive of taxes or exclusive of taxes. One of inclusive, exclusive, or unspecified. Once specified as either inclusive or exclusive, it cannot be changed.", "type": "string"}, "tiers": {"description": "Each element represents a pricing tier. This parameter requires billing_scheme to be set to tiered. See also the documentation for billing_scheme. This field is not included by default. To include it in the response, expand the <currency>.tiers field.", "type": "array", "items": {"type": "object"}}, "unitAmount": {"description": "The unit amount in cents to be charged, represented as a whole integer if possible. Only set if billing_scheme=per_unit.", "type": "integer"}, "unitAmountDecimal": {"description": "The unit amount in cents to be charged, represented as a decimal string with at most 12 decimal places. Only set if billing_scheme=per_unit.", "type": "string"}}}, "DtoProduct": {"title": "DtoProduct", "description": "Product object, see https://stripe.com/docs/api/products/object", "type": "object", "properties": {"id": {"description": "Unique identifier for the object.", "type": "string"}, "object": {"description": "String representing the object's type. Objects of the same type share the same value.", "default": "product", "type": "string"}, "active": {"description": "Whether the product is currently available for purchase.", "type": "boolean"}, "created": {"description": "Time at which the object was created. Measured in seconds since the Unix epoch.", "type": "integer"}, "defaultPrice": {"description": "The ID of the Price object that is the default price for this product.", "type": "string"}, "description": {"description": "The product's description, meant to be displayable to the customer. Use this field to optionally store a long form explanation of the product being sold for your own rendering purposes.", "type": "string"}, "images": {"description": "A list of up to 8 URLs of images for this product, meant to be displayable to the customer.", "type": "array", "items": {"type": "string"}}, "livemode": {"description": "Has the value true if the object exists in live mode or the value false if the object exists in test mode.", "type": "boolean"}, "metadata": {"description": "Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format.", "type": "object"}, "name": {"description": "The product's name, meant to be displayable to the customer.", "type": "string"}, "packageDimensions": {"description": "The dimensions of this product for shipping purposes.", "type": "object"}, "shippable": {"description": "Whether this product is shipped (i.e., physical goods).", "type": "boolean"}, "statementDescriptor": {"description": "Extra information about a product which will appear on your customer's credit card statement. In the case that multiple products are billed at once, the first statement descriptor will be used.", "type": "string"}, "taxCode": {"description": "A tax code ID.", "type": "string"}, "unitLabel": {"description": "A label that represents units of this product. When set, this will be included in customers' receipts, invoices, Checkout, and the customer portal.", "type": "string"}, "updated": {"description": "Time at which the object was last updated. Measured in seconds since the Unix epoch.", "type": "integer"}, "url": {"description": "A URL of a publicly-accessible webpage for this product.", "type": "string"}}, "required": ["id", "name"]}, "RecurringInterval": {"title": "RecurringInterval", "description": "An enumeration.", "enum": ["month", "year", "week", "day"], "type": "string"}, "_DtoRecurring": {"title": "_DtoRecurring", "type": "object", "properties": {"aggregateUsage": {"description": "Specifies a usage aggregation strategy for prices of usage_type=metered. Allowed values are sum for summing up all usage during a period, last_during_period for using the last usage record reported within a period, last_ever for using the last usage record ever (across period bounds) or max which uses the usage record with the maximum reported usage during a period. Defaults to sum.", "type": "string"}, "interval": {"description": "The frequency at which a subscription is billed. One of day, week, month or year.", "allOf": [{"$ref": "#/components/schemas/RecurringInterval"}]}, "intervalCount": {"description": "The number of intervals (specified in the interval attribute) between subscription billings. For example, interval=month and interval_count=3 bills every 3 months.", "exclusiveMinimum": 0, "type": "integer"}, "usageType": {"description": "Configures how the quantity per period should be determined. Can be either metered or licensed. licensed automatically bills the quantity set when adding it to a subscription. metered aggregates the total usage based on usage records. Defaults to licensed", "enum": ["metered", "licensed"], "type": "string"}}}, "DtoPrice": {"title": "DtoPrice", "description": "Price object, see https://stripe.com/docs/api/prices/object", "type": "object", "properties": {"id": {"description": "Unique identifier for the object.", "type": "string"}, "object": {"description": "String representing the object's type. Objects of the same type share the same value.", "default": "price", "type": "string"}, "active": {"description": "Whether the price can be used for new purchases.", "type": "boolean"}, "billingScheme": {"description": "Describes how to compute the price per period. Either per_unit or tiered. per_unit indicates that the fixed amount (specified in unit_amount or unit_amount_decimal) will be charged per unit in quantity (for prices with usage_type=licensed), or per unit of total usage (for prices with usage_type=metered). tiered indicates that the unit pricing will be computed using a tiering strategy as defined using the tiers and tiers_mode attributes.", "type": "string"}, "created": {"description": "Time at which the object was created. Measured in seconds since the Unix epoch.", "type": "integer"}, "currency": {"description": "Three-letter ISO currency code, in lowercase. Must be a supported currency.", "type": "string"}, "currencyOptions": {"description": "Prices defined in each available currency option. Each key must be a three-letter ISO currency code and a supported currency. For example, to get your price in eur, fetch the value of the eur key in currency_options. This field is not included by default. To include it in the response, expand the currency_options field.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/_DtoCurrencyOptions"}}, "customUnitAmount": {"description": "When set, provides configuration for the amount to be adjusted by the customer during Checkout Sessions and Payment Links.", "type": "object"}, "livemode": {"description": "Has the value true if the object exists in live mode or the value false if the object exists in test mode.", "type": "boolean"}, "lookupKey": {"description": "A lookup key used to retrieve prices dynamically from a static string. This may be up to 200 characters.", "type": "string"}, "metadata": {"description": "Set of key-value pairs that you can attach to an object. This can be useful for storing additional information about the object in a structured format.", "type": "object"}, "nickname": {"description": "A brief description of the price, hidden from customers.", "type": "string"}, "product": {"description": "The ID of the product this price is associated with.", "anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/DtoProduct"}]}, "recurring": {"description": "The recurring components of a price such as interval and usage_type.", "allOf": [{"$ref": "#/components/schemas/_DtoRecurring"}]}, "taxBehavior": {"description": "Specifies whether the price is considered inclusive of taxes or exclusive of taxes. One of inclusive, exclusive, or unspecified. Once specified as either inclusive or exclusive, it cannot be changed.", "type": "string"}, "tiers": {"description": "Each element represents a pricing tier. This parameter requires billing_scheme to be set to tiered. See also the documentation for billing_scheme. This field is not included by default. To include it in the response, expand the tiers field.", "type": "array", "items": {"type": "object"}}, "tiersMode": {"description": "Defines if the tiering price should be graduated or volume based. In volume-based tiering, the maximum quantity within a period determines the per unit price. In graduated tiering, pricing can change as the quantity grows.", "type": "string"}, "transformQuantity": {"description": "Apply a transformation to the reported usage or set quantity before computing the amount billed. Cannot be combined with tiers.", "type": "object"}, "type": {"description": "One of one_time or recurring depending on whether the price is for a one-time purchase or a recurring (subscription) purchase.", "type": "string"}, "unitAmount": {"description": "The unit amount in cents to be charged, represented as a whole integer if possible. Only set if billing_scheme=per_unit.", "type": "integer"}, "unitAmountDecimal": {"description": "The unit amount in cents to be charged, represented as a decimal string with at most 12 decimal places. Only set if billing_scheme=per_unit.", "type": "string"}}, "required": ["id", "product"]}, "SubscriptionConfigResponse": {"title": "SubscriptionConfigResponse", "type": "object", "properties": {"prices": {"description": "List of price objects.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DtoPrice"}}, "trialDays": {"description": "The trial period in days.", "exclusiveMinimum": 0, "type": "integer"}, "defaultPrice": {"description": "The default price from Stripe.", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}}, "required": ["trialDays", "defaultPrice"]}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}