{"openapi": "3.1.0", "info": {"title": "Blidz Marketplace API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/marketplace/webhook": {"post": {"description": "Process all webhooks from Marketplace.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketplaceReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MarketplaceResp"}}}}}}}}}, "/marketplace/ugd/bind_manual_order": {"post": {"description": "Bind manual order info to blidz order.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BindManualOrderReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/marketplace/ugd/set_forward_tracking": {"post": {"description": "", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetForwardTrackingReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/marketplace/ugd/order/info": {"post": {"description": "", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlidzUgdOrderInfoReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/BlidzUgdOrderInfoResp"}}}}}}}}}, "/marketplace/ugd/order/list": {"post": {"description": "", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlidzUgdOrderListReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/BlidzUgdOrderListResp"}}}}}}}}}}, "components": {"schemas": {"HookType": {"title": "HookType", "description": "An enumeration.", "enum": ["user", "order", "product", "payment"], "type": "string"}, "MarketplaceReq": {"title": "MarketplaceReq", "type": "object", "properties": {"hookType": {"description": "The hook type of marketplace.", "allOf": [{"$ref": "#/components/schemas/HookType"}]}, "action": {"description": "The action of this request tell <PERSON><PERSON><PERSON> to do.", "type": "string"}, "data": {"description": "Request data. It needs to be serialized by the worker.", "type": "object"}}, "required": ["hookType", "action", "data"]}, "MarketplaceResp": {"title": "MarketplaceResp", "type": "object", "properties": {"data": {"description": "Response data. It needs to be serialized by the marketplace.", "type": "object"}}, "required": ["data"]}, "BindManualOrderReq": {"title": "BindManualOrderReq", "type": "object", "properties": {"orderId": {"description": "Order id for binding manual order", "type": "string"}, "manualSource": {"description": "Source for manual order", "type": "string"}, "sourceOrderId": {"description": "Order id for source order", "type": "string"}, "sourceTrackingId": {"description": "Tracking id for source order", "type": "string"}}, "required": ["orderId", "manualSource", "sourceOrderId"]}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "SetForwardTrackingReq": {"title": "SetForwardTrackingReq", "type": "object", "properties": {"orderId": {"description": "Order id for manual order", "type": "string"}, "forwardTrackingId": {"description": "Forward tracking id for manual order", "type": "string"}}, "required": ["orderId", "forwardTrackingId"]}, "BlidzUgdOrderInfoReq": {"title": "BlidzUgdOrderInfoReq", "type": "object", "properties": {"orderId": {"description": "Order id for manual order", "type": "string"}}, "required": ["orderId"]}, "Attribute": {"title": "Attribute", "type": "object", "properties": {"name": {"description": "The name of the product attribute.", "type": "string"}, "value": {"description": "The value of the product attribute.", "type": "string"}}}, "Supplier": {"title": "Supplier", "type": "object", "properties": {"name": {"description": "The name of supplier.", "type": "string"}, "supplierStoreId": {"description": "The store Id of the supplier.", "type": "string"}, "supplierProductId": {"description": "The supplier product id for the product.", "type": "string"}}}, "OrderItem": {"title": "OrderItem", "type": "object", "properties": {"productId": {"description": "The Id of the product.", "type": "string"}, "productType": {"description": "The type of the product.", "type": "string"}, "status": {"description": "The status of the product.", "type": "string"}, "description": {"description": "The description of the product.", "type": "string"}, "name": {"description": "The name of the product.", "type": "string"}, "priceBeforeDiscount": {"description": "The original price of the product", "type": "integer"}, "currency": {"description": "The currency of the price.", "type": "string"}, "image": {"description": "The image of the product.", "type": "string"}, "dealId": {"description": "The deal id for the product.", "type": "string"}, "dealType": {"description": "The deal type for the product.", "type": "string"}, "attributeData": {"description": "The attribute of the bought item, eg. size, color", "type": "array", "items": {"$ref": "#/components/schemas/Attribute"}}, "supplier": {"description": "The supplier info of the product.", "allOf": [{"$ref": "#/components/schemas/Supplier"}]}, "productCost": {"type": "integer"}}}, "UserInfo": {"title": "UserInfo", "type": "object", "properties": {"userId": {"description": "The user id of the buyer.", "type": "string"}, "userEmail": {"description": "The user name of the buyer.", "type": "string"}}}, "ShippingAddress": {"title": "ShippingAddress", "type": "object", "properties": {"recipient": {"description": "The recipient of the shipping address", "type": "string"}, "firstName": {"description": "The first name of the shipping address", "type": "string"}, "lastName": {"description": "The last name of the shipping address", "type": "string"}, "phoneNumber": {"description": "The phone number of the shipping address", "type": "string"}, "country": {"description": "The country of the shipping address", "type": "string"}, "state": {"description": "The state of the shipping address", "type": "string"}, "city": {"description": "The city of the shipping address", "type": "string"}, "street": {"description": "The street of the shipping address", "type": "string"}, "line2": {"description": "The line2 of the shipping address", "type": "string"}, "zip": {"description": "The zip of the shipping address", "type": "string"}}}, "ZincData": {"title": "ZincData", "type": "object", "properties": {"zincRequestId": {"description": "The zinc request id for the zinc request, got from zinc api", "type": "string"}, "zincStatus": {"description": "The zinc status for the order, eg. ok, pending, failed and etc.", "type": "string"}, "zincMessage": {"description": "The zinc error message if provided.", "type": "string"}, "zincRetryCount": {"description": "The zinc script retry count.", "type": "integer"}}}, "ForwardTracking": {"title": "ForwardTracking", "type": "object", "properties": {"manualSource": {"description": "Source for manual order, like amazon", "type": "string"}, "sourceOrderId": {"description": "Order id for source order", "type": "string"}, "sourceOrderTime": {"description": "The time to manually create the order.", "type": "integer"}, "sourceTrackingId": {"description": "Tracking id for source order", "type": "string"}, "sourceTrackingTime": {"description": "The time for setting the source tracking.", "type": "integer"}, "forwardTrackingId": {"description": "Forward tracking id for manual order", "type": "string"}, "forwardTime": {"description": "The time for ugd forwarding the order.", "type": "integer"}}}, "OrderInfo": {"title": "OrderInfo", "type": "object", "properties": {"orderId": {"description": "Order id", "type": "string"}, "createdTime": {"description": "Created time for the order", "type": "integer"}, "transactionId": {"description": "Transcation id", "type": "string"}, "price": {"description": "The buyer paid amout for the order.", "type": "integer"}, "productCost": {"description": "The product cost for the order.", "type": "integer"}, "status": {"description": "Order status", "type": "string"}, "btStatus": {"description": "The btStatus for the order", "type": "string"}, "membershipStatus": {"description": "The membership status for the order", "type": "string"}, "duoDealScenario": {"description": "The duo deal scenario for the order", "type": "string"}, "notes": {"description": "The notes for the order", "type": "string"}, "orderItems": {"description": "The order items for the order", "type": "array", "items": {"$ref": "#/components/schemas/OrderItem"}}, "userInfo": {"description": "The user info for the order", "allOf": [{"$ref": "#/components/schemas/UserInfo"}]}, "shippingAddress": {"description": "Shipping info for the order", "allOf": [{"$ref": "#/components/schemas/ShippingAddress"}]}, "zinc": {"description": "Zinc status for the order", "allOf": [{"$ref": "#/components/schemas/ZincData"}]}, "forwardTracking": {"description": "Forward tracking data for the order", "allOf": [{"$ref": "#/components/schemas/ForwardTracking"}]}, "userUploadVideoStatus": {"description": "User upload video status for the order", "type": "string"}}}, "BlidzUgdOrderInfoResp": {"title": "BlidzUgdOrderInfoResp", "type": "object", "properties": {"orderInfo": {"description": "Order info struct", "allOf": [{"$ref": "#/components/schemas/OrderInfo"}]}}, "required": ["orderInfo"]}, "BlidzUgdOrderListReq": {"title": "BlidzUgdOrderListReq", "type": "object", "properties": {"orderId": {"description": "Blidz order id", "type": "string"}, "sourceOrderId": {"description": "Order id for source order", "type": "string"}, "orderStatus": {"description": "Search order by order_status", "type": "string"}, "zincStatus": {"description": "Search order by zinc_status", "type": "string"}, "page": {"description": "Paging for search order", "type": "integer"}, "pageSize": {"description": "Paging for search order", "type": "integer"}}}, "BlidzUgdOrderListResp": {"title": "BlidzUgdOrderListResp", "type": "object", "properties": {"orders": {"description": "Order list for ugd system", "type": "array", "items": {"$ref": "#/components/schemas/OrderInfo"}}, "count": {"description": "The matched order quantity", "type": "integer"}}, "required": ["orders"]}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}