{"openapi": "3.1.0", "info": {"title": "Blidz Product Product_Like_Definitions API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/product/{product_id}/like": {"post": {"description": "Like a product", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}, {"in": "path", "name": "product_id", "required": true, "schema": {"type": "string"}, "description": "The product ID."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductLikeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ProductLikeResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "If the product is not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "422": {"description": "If the request validation fails.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/product/{product_id}/list_likes": {"get": {"description": "List a product's likes", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"in": "path", "name": "product_id", "required": true, "schema": {"type": "string"}, "description": "The product ID."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListProductLikesResponse"}}}}}}, "404": {"description": "If the product is not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/product/own_likes": {"get": {"description": "List own liked products.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListOwnProductLikesResponse"}}}}}}}}}}, "components": {"schemas": {"ProductLikeRequest": {"title": "ProductLikeRequest", "type": "object", "properties": {"privacy": {"description": "The privacy of the like. Defaults to public.", "default": "public", "type": "string"}, "outsourced": {"description": "Whether the like is outsourced or not, e.g. from a supplier.", "default": false, "type": "boolean"}, "simulated": {"description": "Whether the like is simulated or not, e.g. our own simulation.", "default": false, "type": "boolean"}}}, "ProductLikeResponse": {"title": "ProductLikeResponse", "type": "object", "properties": {}}, "ListProductLikesRequest": {"title": "ListProductLikesRequest", "type": "object", "properties": {}}, "ListProductLikesResponse": {"title": "ListProductLikesResponse", "type": "object", "properties": {"count": {"description": "The sum of all likes for given product.", "type": "integer"}}}, "ListOwnProductLikesRequest": {"title": "ListOwnProductLikesRequest", "type": "object", "properties": {}}, "LockCost": {"title": "LockCost", "type": "object", "properties": {"coins": {"title": "Coins", "default": 0, "type": "integer"}}}, "PriceDetail": {"title": "PriceDetail", "type": "object", "properties": {"coins": {"title": "Coins", "type": "integer"}, "price": {"title": "Price", "type": "integer"}, "shipping": {"title": "Shipping", "type": "integer"}, "shippingAsCoins": {"title": "Shippingascoins", "type": "integer"}}, "required": ["coins", "price", "shipping", "shippingAsCoins"]}, "PriceProperty": {"title": "PriceProperty", "type": "object", "properties": {"lockCost": {"$ref": "#/components/schemas/LockCost"}, "restricted": {"title": "Restricted", "type": "boolean"}, "price": {"title": "Price", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceDetail"}}}}, "PriceLevelSet": {"title": "PriceLevelSet", "type": "object", "properties": {"solo": {"$ref": "#/components/schemas/PriceProperty"}, "target": {"$ref": "#/components/schemas/PriceProperty"}, "join": {"$ref": "#/components/schemas/PriceProperty"}, "retail": {"$ref": "#/components/schemas/PriceProperty"}, "sale": {"$ref": "#/components/schemas/PriceProperty"}, "min": {"$ref": "#/components/schemas/PriceProperty"}, "max": {"$ref": "#/components/schemas/PriceProperty"}}}, "DealAttributes": {"title": "DealAttributes", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "optionCount": {"title": "Optioncount", "type": "integer"}}}, "DealClientListData": {"title": "DealClientListData", "type": "object", "properties": {"_id": {"pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "state": {"type": "string"}, "priceLevels": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "array", "items": {}}}}, "lowestPriceInUsdCent": {"type": "integer"}, "reallocatedPrice": {"type": "integer"}, "price": {"type": "integer"}, "fincoinPrice": {"type": "integer"}, "strikethroughPrice": {"type": "integer"}, "_productId": {"type": "string"}, "productName": {"type": "string"}, "image": {"type": "string"}, "rating": {"type": "number"}, "ratingCount": {"type": "integer"}, "reviews": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "retailPrice": {"type": "object", "additionalProperties": {"type": "integer"}}, "duoDeal": {"default": true, "type": "boolean"}, "gamified": {"default": false, "type": "boolean"}, "dealRace": {"default": false, "type": "boolean"}, "quantity": {"default": {}, "type": "object"}, "minLockCost": {"default": 1, "type": "integer"}, "priceCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "coinCostCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "imageKitEnabled": {"default": true, "type": "boolean"}, "lastUpdated": {"type": "integer"}, "duoDealEntries": {"type": "array", "items": {"type": "object"}}, "timerStart": {"type": "integer"}, "timerEnd": {"type": "integer"}, "endTime": {"type": "integer"}, "recentRacers": {"type": "object"}, "amountOfRacers": {"type": "integer"}, "usersWon": {"type": "object"}, "maxRacer": {"type": "integer"}, "leaderboardActivated": {"type": "boolean"}, "productLikes": {"type": "integer"}, "productCost": {"type": "integer"}, "supplierName": {"type": "string"}, "supplierStoreId": {"type": "string"}, "fromProducer": {"type": "string"}, "reqId": {"type": "string"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/DealAttributes"}}, "soldCount": {"type": "integer"}, "data": {"description": "Client List Data as a string.", "type": "string"}, "branded": {"type": "boolean"}, "startTime": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}, "hasAdultContent": {"type": "boolean"}}, "required": ["_id", "state", "_productId", "productName", "type"]}, "ProductLikeData": {"title": "ProductLikeData", "type": "object", "properties": {"productId": {"description": "The product id.", "type": "string"}, "productName": {"description": "The product name.", "type": "string"}, "productImageUrl": {"description": "The product image url.", "type": "string"}, "productSupplier": {"description": "The product supplier name.", "type": "string"}, "deal": {"description": "Deal data for client", "allOf": [{"$ref": "#/components/schemas/DealClientListData"}]}}, "required": ["productId"]}, "ListOwnProductLikesResponse": {"title": "ListOwnProductLikesResponse", "type": "object", "properties": {"products": {"description": "List of liked products.", "type": "array", "items": {"$ref": "#/components/schemas/ProductLikeData"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}