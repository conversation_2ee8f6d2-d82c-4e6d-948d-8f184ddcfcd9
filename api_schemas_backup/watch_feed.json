{"openapi": "3.1.0", "info": {"title": "Blidz Watch_Feed API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/watch_feed/feed": {"post": {"description": "Get feed item list", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetWatchFeedReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetWatchFeedResp"}}}}}}}}}, "/watch_feed/like": {"post": {"description": "Like watch feed item", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LikeWatchFeedReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/LikeWatchFeedResp"}}}}}}}}}, "/watch_feed/item": {"post": {"description": "Get watch feed item details", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetWatchFeedItemDetailsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetWatchFeedItemDetailsResp"}}}}}}}}}, "/watch_feed/start_watch_session": {"post": {"description": "Start watch session", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartWatchSessionReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/StartWatchSessionResp"}}}}}}}}}, "/watch_feed/end_watch_session": {"post": {"description": "End watch session", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EndWatchSessionReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EndWatchSessionResp"}}}}}}}}}, "/watch_feed/start_watch": {"post": {"description": "Start watch video", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartWatchReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/StartWatchResp"}}}}}}}}}, "/watch_feed/get_reward": {"post": {"description": "Get reward when user has watched more than 3 seconds", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRewardReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetRewardResp"}}}}}}}}}}, "components": {"schemas": {"DtoWatchFeedTab": {"title": "DtoWatchFeedTab", "description": "An enumeration.", "enum": ["default", "product", "fun"], "type": "string"}, "GetWatchFeedReq": {"title": "GetWatchFeedReq", "type": "object", "properties": {"tab": {"description": "The tab name for the watch feed, eg. product, fun", "default": "default", "allOf": [{"$ref": "#/components/schemas/DtoWatchFeedTab"}]}, "count": {"description": "How many feed items should return in one call", "default": 10, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined by client", "type": "string"}}}, "DtoWatchFeedItemType": {"title": "DtoWatchFeedItemType", "description": "An enumeration.", "enum": ["video"], "type": "string"}, "DtoWatchFeedItemOwner": {"title": "DtoWatchFeedItemOwner", "type": "object", "properties": {"userId": {"description": "The user ID of the user who uploaded this video", "type": "string"}, "avatar": {"description": "The avatar URL of the user who uploaded this video", "type": "string"}, "fullname": {"description": "The fullname of user", "type": "string"}, "playerName": {"description": "The player name of user", "type": "string"}}}, "DtoPriceData": {"title": "DtoPriceData", "type": "object", "properties": {"USD": {"description": "The price of USD", "minimum": 0, "type": "integer"}, "AUD": {"description": "The price of AUD", "minimum": 0, "type": "integer"}, "CAD": {"description": "The price of CAD", "minimum": 0, "type": "integer"}}}, "DtoDealInfo": {"title": "DtoDealInfo", "type": "object", "properties": {"dealId": {"description": "The id of deal", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "productId": {"description": "The product id of deal", "type": "string"}, "name": {"description": "The name of deal", "type": "string"}, "image": {"description": "The image of deal", "type": "string"}, "btnPrice": {"description": "The BTN price of deal", "allOf": [{"$ref": "#/components/schemas/DtoPriceData"}]}, "retailPrice": {"description": "The retail price of deal", "allOf": [{"$ref": "#/components/schemas/DtoPriceData"}]}, "reqId": {"description": "Request id", "type": "string"}}}, "LockCost": {"title": "LockCost", "type": "object", "properties": {"coins": {"title": "Coins", "default": 0, "type": "integer"}}}, "PriceDetail": {"title": "PriceDetail", "type": "object", "properties": {"coins": {"title": "Coins", "type": "integer"}, "price": {"title": "Price", "type": "integer"}, "shipping": {"title": "Shipping", "type": "integer"}, "shippingAsCoins": {"title": "Shippingascoins", "type": "integer"}}, "required": ["coins", "price", "shipping", "shippingAsCoins"]}, "PriceProperty": {"title": "PriceProperty", "type": "object", "properties": {"lockCost": {"$ref": "#/components/schemas/LockCost"}, "restricted": {"title": "Restricted", "type": "boolean"}, "price": {"title": "Price", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceDetail"}}}}, "PriceLevelSet": {"title": "PriceLevelSet", "type": "object", "properties": {"solo": {"$ref": "#/components/schemas/PriceProperty"}, "target": {"$ref": "#/components/schemas/PriceProperty"}, "join": {"$ref": "#/components/schemas/PriceProperty"}, "retail": {"$ref": "#/components/schemas/PriceProperty"}, "sale": {"$ref": "#/components/schemas/PriceProperty"}, "min": {"$ref": "#/components/schemas/PriceProperty"}, "max": {"$ref": "#/components/schemas/PriceProperty"}}}, "DealAttributes": {"title": "DealAttributes", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "optionCount": {"title": "Optioncount", "type": "integer"}}}, "DealClientListData": {"title": "DealClientListData", "type": "object", "properties": {"_id": {"pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "state": {"type": "string"}, "priceLevels": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "array", "items": {}}}}, "lowestPriceInUsdCent": {"type": "integer"}, "reallocatedPrice": {"type": "integer"}, "price": {"type": "integer"}, "fincoinPrice": {"type": "integer"}, "strikethroughPrice": {"type": "integer"}, "_productId": {"type": "string"}, "productName": {"type": "string"}, "image": {"type": "string"}, "rating": {"type": "number"}, "ratingCount": {"type": "integer"}, "reviews": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "retailPrice": {"type": "object", "additionalProperties": {"type": "integer"}}, "duoDeal": {"default": true, "type": "boolean"}, "gamified": {"default": false, "type": "boolean"}, "dealRace": {"default": false, "type": "boolean"}, "quantity": {"default": {}, "type": "object"}, "minLockCost": {"default": 1, "type": "integer"}, "priceCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "coinCostCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "imageKitEnabled": {"default": true, "type": "boolean"}, "lastUpdated": {"type": "integer"}, "duoDealEntries": {"type": "array", "items": {"type": "object"}}, "timerStart": {"type": "integer"}, "timerEnd": {"type": "integer"}, "endTime": {"type": "integer"}, "recentRacers": {"type": "object"}, "amountOfRacers": {"type": "integer"}, "usersWon": {"type": "object"}, "maxRacer": {"type": "integer"}, "leaderboardActivated": {"type": "boolean"}, "productLikes": {"type": "integer"}, "productCost": {"type": "integer"}, "supplierName": {"type": "string"}, "supplierStoreId": {"type": "string"}, "fromProducer": {"type": "string"}, "reqId": {"type": "string"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/DealAttributes"}}, "soldCount": {"type": "integer"}, "data": {"description": "Client List Data as a string.", "type": "string"}, "branded": {"type": "boolean"}, "startTime": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}, "hasAdultContent": {"type": "boolean"}}, "required": ["_id", "state", "_productId", "productName", "type"]}, "DtoWatchFeedItem": {"title": "DtoWatchFeedItem", "type": "object", "properties": {"feedItemId": {"description": "The unique id of feed item", "type": "string"}, "feedItemType": {"description": "The type of feed", "allOf": [{"$ref": "#/components/schemas/DtoWatchFeedItemType"}]}, "videoUrl": {"description": "The video url if provided", "type": "string"}, "title": {"description": "The title of video if from youtube or product name if from CJ", "type": "string"}, "owner": {"description": "The user who uploaded the video", "allOf": [{"$ref": "#/components/schemas/DtoWatchFeedItemOwner"}]}, "isLiked": {"description": "The watch feed is liked or not", "type": "boolean"}, "likes": {"description": "The total like count for the item", "minimum": 0, "type": "integer"}, "dealInfo": {"description": "The infomation of deal realated the video if provided", "allOf": [{"$ref": "#/components/schemas/DtoDealInfo"}]}, "deal": {"description": "The deal realated the video if provided", "allOf": [{"$ref": "#/components/schemas/DealClientListData"}]}, "viewCount": {"description": "The view count of this video", "minimum": 0, "type": "integer"}, "videoSource": {"description": "Source of the video", "type": "string"}, "reqId": {"description": "Request id that used for algo.", "type": "string"}}}, "GetWatchFeedResp": {"title": "GetWatchFeedResp", "type": "object", "properties": {"feedItems": {"description": "The feed item list", "type": "array", "items": {"$ref": "#/components/schemas/DtoWatchFeedItem"}}}}, "DtoLikeAction": {"title": "DtoLikeAction", "description": "An enumeration.", "enum": ["like", "toggle"], "type": "string"}, "LikeWatchFeedReq": {"title": "LikeWatchFeedReq", "type": "object", "properties": {"feedItemId": {"description": "The feed item id user liked", "type": "string"}, "action": {"description": "The action of like, `like` or `toggle`", "default": "like", "allOf": [{"$ref": "#/components/schemas/DtoLikeAction"}]}}, "required": ["feedItemId"]}, "LikeWatchFeedResp": {"title": "LikeWatchFeedResp", "type": "object", "properties": {"status": {"description": "The feed is liked or not", "type": "boolean"}}}, "GetWatchFeedItemDetailsReq": {"title": "GetWatchFeedItemDetailsReq", "type": "object", "properties": {"feedItemId": {"description": "The feed item id for the details", "type": "string"}, "channel": {"description": "The algo channel defined by client", "type": "string"}, "reqId": {"description": "Req_id of the video if existed", "type": "string"}}, "required": ["feedItemId"]}, "GetWatchFeedItemDetailsResp": {"title": "GetWatchFeedItemDetailsResp", "type": "object", "properties": {"details": {"description": "The feed item details data", "allOf": [{"$ref": "#/components/schemas/DtoWatchFeedItem"}]}}}, "StartWatchSessionReq": {"title": "StartWatchSessionReq", "type": "object", "properties": {}}, "StartWatchSessionResp": {"title": "StartWatchSessionResp", "type": "object", "properties": {"totalVideoCountForSpin": {"description": "The total count of videos user has to watch to get spin reward", "minimum": 0, "type": "integer"}, "videoCountLeftForSpin": {"description": "The count of videos left user has to watch to get spin reward", "minimum": 0, "type": "integer"}}}, "EndWatchSessionReq": {"title": "EndWatchSessionReq", "type": "object", "properties": {}}, "EndWatchSessionResp": {"title": "EndWatchSessionResp", "type": "object", "properties": {}}, "StartWatchReq": {"title": "StartWatchReq", "type": "object", "properties": {"feedItemId": {"description": "The feed item id that user ready to watch", "type": "string"}}, "required": ["feedItemId"]}, "StartWatchResp": {"title": "StartWatchResp", "type": "object", "properties": {}}, "GetRewardReq": {"title": "GetRewardReq", "type": "object", "properties": {"feedItemId": {"description": "The feed item id that user is watching", "type": "string"}}, "required": ["feedItemId"]}, "GetRewardResp": {"title": "GetRewardResp", "type": "object", "properties": {"cashAmount": {"description": "The amount of cash user got in this time", "minimum": 0, "type": "integer"}, "spinAmount": {"description": "The amount of spin user got in this time", "minimum": 0, "type": "integer"}, "fincoinAmount": {"description": "The amount of fincoin user got in this time", "minimum": 0, "type": "integer"}, "totalFincoinAmount": {"description": "The total amount of fincoin user has already got in today", "minimum": 0, "type": "integer"}, "totalAmount": {"description": "The total amount of cash user has already got in today", "minimum": 0, "type": "integer"}, "totalVideoCountForSpin": {"description": "The total count of videos user has to watch to get spin reward", "minimum": 0, "type": "integer"}, "videoCountLeftForSpin": {"description": "The count of videos left user has to watch to get spin reward", "minimum": 0, "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}