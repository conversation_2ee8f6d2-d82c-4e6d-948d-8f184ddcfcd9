{"openapi": "3.1.0", "info": {"title": "Blidz PuSu", "version": "1.0"}, "servers": [], "paths": {}, "components": {"schemas": {"Root": {"PusuEvent": {"properties": {"type": {"$ref": "#/components/schemas/Root/PusuEventType", "description": "The type of event. It matches the name of event content class"}, "time": {"description": "The timestamp of event in milliseconds", "type": "integer"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "content": {"$ref": "#/components/schemas/Root/PusuEventContent", "description": "The content of event. Different types have different schemas"}}, "required": ["type", "time", "content"], "type": "object"}, "PusuEventContent": {"properties": {}, "type": "object"}, "PusuEventType": {"enum": ["update_spin", "update_fincoin", "update_user_key", "update_mystery_box", "update_game_turn", "update_duo_deal_auto_completion_ticket", "update_membership", "update_stripe"], "type": "string"}}, "GameId": {"enum": ["candy_jam", "snake"], "type": "string"}, "InvoiceStatus": {"enum": ["draft", "open", "paid", "uncollectible", "void"], "type": "string"}, "MembershipStatus": {"enum": ["non_member", "trial_member", "trial_member_no_renewal", "active_member", "active_member_no_renewal", "active_member_paused"], "type": "string"}, "PaymentIntentStatus": {"enum": ["canceled", "processing", "requires_action", "requires_capture", "requires_confirmation", "requires_payment_method", "succeeded", "payment_failed"], "type": "string"}, "Schedule": {"enum": ["daily", "weekly", "monthly", "yearly"], "type": "string"}, "SubscriptionPlatform": {"enum": ["stripe", "revenue_cat_apple_app_store", "revenue_cat_google_play_store"], "type": "string"}, "Tier": {"enum": ["blidz_plus", "blidz_plus_extra", "blidz_plus_ultra"], "type": "string"}, "UpdateDuoDealAutoCompletionTicket": {"properties": {"amount": {"type": "integer"}}, "required": ["amount"], "toChannel": "user_v2", "type": "object"}, "UpdateFincoin": {"properties": {"fincoinBalance": {"type": "integer"}, "totalAddedAmount": {"type": "integer"}}, "required": ["fincoinBalance", "totalAddedAmount"], "toChannel": "user_v2", "type": "object"}, "UpdateGameTurn": {"properties": {"gameId": {"$ref": "#/components/schemas/GameId"}, "turnsLeft": {"type": "integer"}, "turnResetTime": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "required": ["gameId", "turnsLeft"], "toChannel": "user_v2", "type": "object"}, "UpdateMembership": {"properties": {"subscriptionPlatform": {"anyOf": [{"$ref": "#/components/schemas/SubscriptionPlatform"}, {"type": "null"}], "default": null}, "tier": {"anyOf": [{"$ref": "#/components/schemas/Tier"}, {"type": "null"}], "default": null}, "trialUsed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null}, "trialCount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "status": {"anyOf": [{"$ref": "#/components/schemas/MembershipStatus"}, {"type": "null"}], "default": null}, "createdAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "price": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "startedAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "renewedAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "canceledAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "endedAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "paymentStatus": {"anyOf": [{"$ref": "#/components/schemas/_DtoPaymentStatus"}, {"type": "null"}], "default": null}, "periodStart": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "periodEnd": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "trialStart": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "trialEnd": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "memberOrderCounts": {"anyOf": [{"$ref": "#/components/schemas/_DtoMemberOrderCount"}, {"type": "null"}], "default": null}, "mixedCartOrder": {"anyOf": [{"$ref": "#/components/schemas/_DtoMixedCartOrder"}, {"type": "null"}], "default": null}, "memberSavingsAmount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "memberCashEarned": {"anyOf": [{"$ref": "#/components/schemas/_DtoCashEarned"}, {"type": "null"}], "default": null}, "memberCashStashed": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "specialOffer": {"anyOf": [{"$ref": "#/components/schemas/_DtoSpecialOffer"}, {"type": "null"}], "default": null}, "freeCashOnTrialGiven": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null}, "lookupKey": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "schedule": {"anyOf": [{"$ref": "#/components/schemas/Schedule"}, {"type": "null"}], "default": null}}, "toChannel": "user_v2", "type": "object"}, "UpdateMysteryBox": {"properties": {"available": {"anyOf": [{"items": {"$ref": "#/components/schemas/_MysteryBoxData"}, "type": "array"}, {"type": "null"}], "default": null}, "active": {"anyOf": [{"$ref": "#/components/schemas/_ActiveMysteryBoxData"}, {"type": "null"}], "default": null}, "finishedCount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "nextFreeMinigameTime": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "toChannel": "user_v2", "type": "object"}, "UpdateSpin": {"properties": {"spinRemaining": {"type": "integer"}, "nextFreeSpinTime": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "required": ["spinRemaining"], "toChannel": "user_v2", "type": "object"}, "UpdateUserKey": {"properties": {"keyCount": {"type": "integer"}, "nextFreeKey": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "required": ["keyCount"], "toChannel": "user_v2", "type": "object"}, "_ActiveMysteryBoxData": {"properties": {"given": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "expires": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "started": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "inviteLink": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "inviter": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "fromInviteLink": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "rewardType": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "freeCoins": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "freeCash": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "type": "object"}, "_DtoCashEarned": {"properties": {"free": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "paid": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "type": "object"}, "_DtoMemberOrderCount": {"properties": {"freeProduct": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "paidProduct": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "subscription": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "type": "object"}, "_DtoMixedCartOrder": {"properties": {"orderId": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "finalCheckoutPrice": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "reqId": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "algoChannel": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}}, "type": "object"}, "_DtoPaymentStatus": {"properties": {"attemptedAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "amountDue": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "attemptCount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "nextPaymentAttempt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "invoiceId": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "invoiceStatus": {"anyOf": [{"$ref": "#/components/schemas/InvoiceStatus"}, {"type": "null"}], "default": null}, "paymentIntentStatus": {"anyOf": [{"$ref": "#/components/schemas/PaymentIntentStatus"}, {"type": "null"}], "default": null}, "lastPaymentError": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null}, "lastFailedInvoiceUrl": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}}, "type": "object"}, "_DtoSpecialOffer": {"properties": {"firstMonthFree": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null}, "authenticationCount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}}, "type": "object"}, "_MysteryBoxData": {"properties": {"given": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "expires": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "started": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null}, "inviteLink": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "inviter": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}, "fromInviteLink": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null}}, "type": "object"}}}}