{"openapi": "3.1.0", "info": {"title": "Blidz Product_Review API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/product_review/reviews_stats": {"post": {"description": "Get statistics of reviews", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetReviewsStatsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetReviewsStatsResp"}}}}}}}}}, "/product_review/reviews": {"post": {"description": "Get reviews", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetReviewsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetReviewsResp"}}}}}}}}}, "/product_review/write_review": {"post": {"description": "Write new review", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WriteReviewReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/WriteReviewResp"}}}}}}}}}}, "components": {"schemas": {"GetReviewsStatsReq": {"title": "GetReviewsStatsReq", "type": "object", "properties": {"productId": {"description": "The product Id", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "reviewCount": {"description": "How many reviews to fetch along with the statistics data", "default": 2, "minimum": 0, "type": "integer"}, "fetchRatingCount": {"description": "Fetch the count of reviews of each rating", "default": false, "type": "boolean"}, "fetchRecentImage": {"description": "Fetch the recent image list along with the statistics data", "default": false, "type": "boolean"}}, "required": ["productId"]}, "ReviewFilter": {"title": "ReviewFilter", "description": "An enumeration.", "enum": ["myReview", "imageReview", "positive", "negative"], "type": "string"}, "FilteredCount": {"title": "FilteredCount", "type": "object", "properties": {"filter": {"description": "The type of filter", "allOf": [{"$ref": "#/components/schemas/ReviewFilter"}]}, "count": {"description": "The count of reviews under this filter", "minimum": 0, "type": "integer"}}}, "Review": {"title": "Review", "type": "object", "properties": {"reviewId": {"description": "The review ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "reviewTime": {"description": "The timestamp when the review is created", "exclusiveMinimum": 0, "type": "integer"}, "reviewerId": {"description": "The user ID of the reviewer", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "reviewerName": {"description": "The name of the reviewer", "type": "string"}, "rating": {"description": "The rating number", "minimum": 1, "maximum": 5, "type": "integer"}, "text": {"description": "The text comment of this review", "type": "string"}, "images": {"description": "The image URLs of this review", "type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 2083, "format": "uri"}}, "countryCode": {"description": "The country code", "pattern": "[A-Z]{2}", "type": "string"}}}, "GetReviewsStatsResp": {"title": "GetReviewsStatsResp", "type": "object", "properties": {"rating": {"description": "The final rating of the product", "minimum": 0, "type": "number"}, "totalCount": {"description": "Total count of reviews", "minimum": 0, "type": "integer"}, "star5": {"description": "The count of reviews with 5 stars", "minimum": 0, "type": "integer"}, "star4": {"description": "The count of reviews with 4 stars", "minimum": 0, "type": "integer"}, "star3": {"description": "The count of reviews with 3 stars", "minimum": 0, "type": "integer"}, "star2": {"description": "The count of reviews with 2 stars", "minimum": 0, "type": "integer"}, "star1": {"description": "The count of reviews with 1 stars", "minimum": 0, "type": "integer"}, "filterCounts": {"description": "Count of reviews under each filter", "type": "array", "items": {"$ref": "#/components/schemas/FilteredCount"}}, "recentImages": {"description": "The URLs of images", "type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 2083, "format": "uri"}}, "reviews": {"description": "The reviews", "type": "array", "items": {"$ref": "#/components/schemas/Review"}}}}, "GetReviewsReq": {"title": "GetReviewsReq", "type": "object", "properties": {"productId": {"description": "The product Id", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "lastReviewId": {"description": "If this is specified, will fetch reviews after this one. Otherwise will fetch from the first one. Sort order is always the latest on top. It's used in pagination", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "size": {"description": "The count of reviews per page", "default": 20, "exclusiveMinimum": 0, "type": "integer"}, "filter": {"description": "Set filter of the request. Returns all reviews if not specified", "allOf": [{"$ref": "#/components/schemas/ReviewFilter"}]}}, "required": ["productId"]}, "GetReviewsResp": {"title": "GetReviewsResp", "type": "object", "properties": {"reviews": {"description": "The list of reviews", "type": "array", "items": {"$ref": "#/components/schemas/Review"}}}}, "WriteReviewReq": {"title": "WriteReviewReq", "type": "object", "properties": {"orderId": {"description": "The deal Id", "type": "string"}, "reviewRating": {"description": "The rating number", "minimum": 1, "maximum": 5, "type": "integer"}, "text": {"description": "The text comment of this review", "type": "string"}, "images": {"description": "The image URLs of this review", "type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 2083, "format": "uri"}}}, "required": ["orderId", "reviewRating"]}, "WriteReviewResp": {"title": "WriteReviewResp", "type": "object", "properties": {"review": {"description": "The newly created review", "allOf": [{"$ref": "#/components/schemas/Review"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}