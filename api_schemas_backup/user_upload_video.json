{"openapi": "3.1.0", "info": {"title": "Blidz User_Upload_Video API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/user_upload_video/save_video": {"post": {"description": "Save the video", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveVideoReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SaveVideoResp"}}}}}}}}}, "/user_upload_video/get_video": {"post": {"description": "Get the my video by order Id", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVideoReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetVideoResp"}}}}}}}}}, "/user_upload_video/get_videos": {"post": {"description": "Get list of videos", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVideosReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetVideosResp"}}}}}}}}}}, "components": {"schemas": {"SaveVideoReq": {"title": "SaveVideoReq", "type": "object", "properties": {"orderId": {"description": "The order ID if the video is related to order", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "productId": {"description": "The product ID if the video is related to product", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "url": {"description": "The URL of video. Needs to be in our google bucket 'blidz-user-upload'", "type": "string"}, "title": {"description": "The title of video", "type": "string"}, "description": {"description": "The description of video", "type": "string"}}, "required": ["url"]}, "DtoReviewStatus": {"title": "DtoReviewStatus", "description": "An enumeration.", "enum": ["pending", "approved", "denied"], "type": "string"}, "SaveVideoResp": {"title": "SaveVideoResp", "type": "object", "properties": {"videoId": {"description": "The ID of video", "type": "string"}, "url": {"description": "The URL of video. This URL is not necessarily the same one in request", "type": "string"}, "reviewStatus": {"description": "The review status of video", "allOf": [{"$ref": "#/components/schemas/DtoReviewStatus"}]}}}, "GetVideoReq": {"title": "GetVideoReq", "type": "object", "properties": {"orderId": {"description": "The order ID related to this video", "type": "string"}}, "required": ["orderId"]}, "GetVideoResp": {"title": "GetVideoResp", "type": "object", "properties": {"videoId": {"description": "The ID of video", "type": "string"}, "title": {"description": "The title of video", "type": "string"}, "description": {"description": "The description of video", "type": "string"}, "url": {"description": "The URL of video. This URL is not necessarily the same one in request", "type": "string"}, "reviewStatus": {"description": "The review status of video", "allOf": [{"$ref": "#/components/schemas/DtoReviewStatus"}]}, "denyReason": {"description": "The reason of denial if the video is denied", "type": "string"}}}, "GetVideosReq": {"title": "GetVideosReq", "type": "object", "properties": {"page": {"description": "The page number. Starting from 1", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}, "orderId": {"description": "If this is not None, then only show video related to this order", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "reviewStatus": {"description": "If this is not None, then only show this review status", "allOf": [{"$ref": "#/components/schemas/DtoReviewStatus"}]}}}, "DtoVideo": {"title": "DtoVideo", "type": "object", "properties": {"videoId": {"description": "The ID of video", "type": "string"}, "title": {"description": "The title of video", "type": "string"}, "description": {"description": "The description of video", "type": "string"}, "url": {"description": "The URL of video", "type": "string"}, "reviewStatus": {"description": "The review status of video", "allOf": [{"$ref": "#/components/schemas/DtoReviewStatus"}]}, "denyReason": {"description": "The reason of denial if the video is denied", "type": "string"}}}, "GetVideosResp": {"title": "GetVideosResp", "type": "object", "properties": {"videos": {"description": "The video list", "type": "array", "items": {"$ref": "#/components/schemas/DtoVideo"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}