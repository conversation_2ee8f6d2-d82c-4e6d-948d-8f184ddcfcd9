{"openapi": "3.1.0", "info": {"title": "Blidz Fiat_Wallet API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/fiat_wallet/get_fiat_wallet": {"post": {"description": "Get the fiat wallet info", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetFiatWalletResp"}}}}}}}}}, "/fiat_wallet/get_fiat_line_items": {"post": {"description": "Get the fiat line items", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFiatLineItemsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetFiatLineItemsResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "GetFiatWalletResp": {"title": "GetFiatWalletResp", "type": "object", "properties": {"currency": {"description": "The currency of fiat wallet", "type": "string"}, "amount": {"description": "The amount of fiat wallet", "type": "integer"}}}, "DtoFiatLineItemAction": {"title": "DtoFiatLineItemAction", "description": "An enumeration.", "enum": ["add", "deposit", "spend", "withdraw", "remove"], "type": "string"}, "GetFiatLineItemsReq": {"title": "GetFiatLineItemsReq", "type": "object", "properties": {"page": {"description": "The current page number. Start with 1", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}, "actionFilter": {"description": "Which action to show. Show all if not specified", "allOf": [{"$ref": "#/components/schemas/DtoFiatLineItemAction"}]}}}, "DtoFiatLineItem": {"title": "DtoFiatLineItem", "type": "object", "properties": {"created": {"description": "The created time", "type": "integer"}, "action": {"description": "The action of fiat item", "allOf": [{"$ref": "#/components/schemas/DtoFiatLineItemAction"}]}, "reason": {"description": "Why the line item happened", "type": "string"}, "currency": {"description": "The currency of line item", "type": "string"}, "amount": {"description": "The amount", "type": "integer"}, "beforeBalance": {"description": "The fiat wallet balance before this item happened", "type": "integer"}, "afterBalance": {"description": "The fiat wallet balance after this item happened", "type": "integer"}}}, "GetFiatLineItemsResp": {"title": "GetFiatLineItemsResp", "type": "object", "properties": {"fiatLineItems": {"description": "The list of line items", "type": "array", "items": {"$ref": "#/components/schemas/DtoFiatLineItem"}}, "total": {"description": "Total count that fit the filter in the request", "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}