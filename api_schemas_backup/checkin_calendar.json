{"openapi": "3.1.0", "info": {"title": "Blidz Checkin_Calendar API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/checkin_calendar/calendar_status": {"post": {"description": "Get the current status of user's check-in calendar", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetCalendarStatusResponse"}}}}}}}}}, "/checkin_calendar/claim_calendar_reward": {"post": {"description": "Perform daily check-in and claim rewards", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimCalendarRewardResponse"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoCheckInStatus": {"title": "DtoCheckInStatus", "description": "An enumeration.", "enum": ["available", "claimed", "locked", "missed"], "type": "string"}, "DtoDayStatus": {"title": "DtoDayStatus", "type": "object", "properties": {"status": {"description": "Current status of this day", "allOf": [{"$ref": "#/components/schemas/DtoCheckInStatus"}]}, "dayOfWeek": {"description": "Day of week (0 = Monday, 6 = Sunday)", "type": "integer"}, "isToday": {"description": "Whether this is today", "type": "boolean"}}}, "DtoCalendarRewardType": {"title": "DtoCalendarRewardType", "description": "An enumeration.", "enum": ["fincoin", "spin", "mystery_box", "key"], "type": "string"}, "DtoRewardItem": {"title": "DtoRewardItem", "type": "object", "properties": {"rewardType": {"description": "Type of reward (points, spin, etc)", "allOf": [{"$ref": "#/components/schemas/DtoCalendarRewardType"}]}, "rewardAmount": {"description": "Amount of the reward", "type": "integer"}}, "required": ["rewardType", "rewardAmount"]}, "DtoRewardSchema": {"title": "DtoRewardSchema", "type": "object", "properties": {"rewards": {"description": "List of rewards included in this scheme", "type": "array", "items": {"$ref": "#/components/schemas/DtoRewardItem"}}, "displayText": {"description": "User-friendly text to display for this reward scheme", "type": "string"}}}, "GetCalendarStatusResponse": {"title": "GetCalendarStatusResponse", "type": "object", "properties": {"currentStreak": {"description": "User's current check-in streak", "type": "integer"}, "calendarDays": {"description": "Status of each day in the current week", "type": "array", "items": {"$ref": "#/components/schemas/DtoDayStatus"}}, "todayReward": {"description": "Reward for today's check-in", "allOf": [{"$ref": "#/components/schemas/DtoRewardSchema"}]}, "nextCheckInTime": {"description": "When user can check in next if already claimed today", "type": "integer"}, "nextReward": {"description": "Reward for the next check-in", "allOf": [{"$ref": "#/components/schemas/DtoRewardSchema"}]}}}, "ClaimCalendarRewardResponse": {"title": "ClaimCalendarRewardResponse", "type": "object", "properties": {"newStreak": {"description": "Updated streak count after this check-in", "type": "integer"}, "rewardsClaimed": {"description": "Rewards earned from this check-in", "allOf": [{"$ref": "#/components/schemas/DtoRewardSchema"}]}, "nextCheckInTime": {"description": "When the next check-in will be available", "type": "integer"}, "nextReward": {"description": "Reward for the next check-in", "allOf": [{"$ref": "#/components/schemas/DtoRewardSchema"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}