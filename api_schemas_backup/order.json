{"openapi": "3.1.0", "info": {"title": "Blidz Order API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/order/order_video_upload_status": {"post": {"description": "Get the overall status of orders which are waiting for video upload", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/OrderVideoUploadStatusResp"}}}}}}}}}, "/order/change_to_next_status": {"post": {"description": "Manually change order to next status. Used for test purpose only. Not available on production", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeToNextStatusReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ChangeToNextStatusResp"}}}}}}}}}, "/order/fill_addresses": {"post": {"description": "Fill the shipping and billing addresses for the order", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FillAddressesReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/FillAddressesResp"}}}}}}}}}, "/order/require_fill_addresses": {"post": {"description": "Check if the user needs to fill the shipping and billing addresses for the order", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequireFillAddressesReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/RequireFillAddressesResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoOrderVideoUploadStatus": {"title": "DtoOrderVideoUploadStatus", "description": "An enumeration.", "enum": ["no_order", "orders_waiting_video", "all_orders_have_video"], "type": "string"}, "OrderVideoUploadStatusResp": {"title": "OrderVideoUploadStatusResp", "type": "object", "properties": {"status": {"description": "The overall status of orders which are waiting for video upload", "allOf": [{"$ref": "#/components/schemas/DtoOrderVideoUploadStatus"}]}}}, "ChangeToNextStatusReq": {"title": "ChangeToNextStatusReq", "type": "object", "properties": {"orderId": {"description": "The order ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}}, "required": ["orderId"]}, "ChangeToNextStatusResp": {"title": "ChangeToNextStatusResp", "type": "object", "properties": {"order": {"description": "The order data", "type": "object"}}}, "Address": {"title": "Address", "type": "object", "properties": {"email": {"description": "Email", "type": "string"}, "firstName": {"description": "First name", "type": "string"}, "lastName": {"description": "Last name", "type": "string"}, "street": {"description": "Street address", "type": "string"}, "city": {"description": "City", "type": "string"}, "zip": {"description": "Zip code", "type": "string"}, "state": {"description": "State", "type": "string"}, "country": {"description": "Country", "type": "string"}, "phoneNumber": {"description": "Phone number", "type": "string"}, "recipient": {"description": "Recipient", "type": "string"}, "line2": {"description": "Address line 2", "type": "string"}, "timeZone": {"description": "Timezone", "type": "string"}}}, "Addresses": {"title": "Addresses", "type": "object", "properties": {"shipping": {"description": "Shipping address", "allOf": [{"$ref": "#/components/schemas/Address"}]}, "billing": {"description": "Billing address", "allOf": [{"$ref": "#/components/schemas/Address"}]}}}, "FillAddressesReq": {"title": "FillAddressesReq", "type": "object", "properties": {"orderId": {"description": "The order ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "addresses": {"description": "The billing/shipping addresses.", "allOf": [{"$ref": "#/components/schemas/Addresses"}]}}, "required": ["addresses"]}, "FillAddressesResp": {"title": "FillAddressesResp", "type": "object", "properties": {"orderShippingAddressFilled": {"description": "Whether the order shipping address is filled", "type": "boolean"}, "userDefaultAddressFilled": {"description": "Whether the user default address is filled", "type": "boolean"}, "editedOrderIds": {"description": "The order IDs that are edited", "type": "array", "items": {"type": "string"}}}, "required": ["orderShippingAddressFilled", "userDefaultAddressFilled", "editedOrderIds"]}, "RequireFillAddressesReq": {"title": "RequireFillAddressesReq", "type": "object", "properties": {"completedDuoDealOnly": {"description": "Whether to only check completed duo deals", "type": "boolean"}}, "required": ["completedDuoDealOnly"]}, "RequireFillAddressesResp": {"title": "RequireFillAddressesResp", "type": "object", "properties": {"orderIds": {"description": "The order IDs", "type": "array", "items": {"type": "string"}}}, "required": ["orderIds"]}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}