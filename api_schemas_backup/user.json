{"openapi": "3.1.0", "info": {"title": "Blidz User API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/user/firebase_login": {"post": {"description": "Login with Firebase", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FirebaseLoginReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/user/my_deals_v2": {"post": {"description": "Get my deals. Returns the list of 'simple deal' model", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyDealsV2Req"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MyDealsV2Resp"}}}}}}}}}, "/user/profile_deals_v2": {"post": {"description": "Get deals of specific user. Returns the list of 'simple deal' model", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileDealsV2Req"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/UserProfileDealsV2Resp"}}}}}}}}}, "/user/give_app_rating_follow_up": {"post": {"description": "Do actions after user give app rating eg: give cash reward", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/user/give_notification_perm_follow_up": {"post": {"description": "Do actions after user give notification permission eg: give spin reward", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/user/check_email": {"post": {"description": "Check email valid to create new user", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/user/share_after_payout_followup": {"post": {"description": "The followup actions for sharing after payout", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ShareAfterPayoutFollowupResp"}}}}}}}}}, "/user/my_search": {"post": {"description": "Get user search keywords", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MySearchKeywordsResp"}}}}}}}}}, "/user/background_images": {"post": {"description": "Get user background images from cdn", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/BackgroundImagesResp"}}}}}}}}}, "/user/update_analytics_info": {"post": {"description": "Update info for analytic service", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserAnalyticInfoReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}}, "components": {"schemas": {"FirebaseLoginReq": {"title": "FirebaseLoginReq", "type": "object", "properties": {"idToken": {"description": "The Firebase ID token to login with.", "type": "string"}, "email": {"description": "The email of the user.", "type": "string"}}, "required": ["idToken"]}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "DealType": {"title": "DealType", "description": "An enumeration.", "enum": ["duoDeal", "treasureHunt"], "type": "string"}, "MyDealsV2Req": {"title": "MyDealsV2Req", "type": "object", "properties": {"dealType": {"description": "The deal type to return", "allOf": [{"$ref": "#/components/schemas/DealType"}]}, "channel": {"description": "Algo channel defined by client", "type": "string"}, "page": {"description": "The current page. Start from 1", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}}}, "LockCost": {"title": "LockCost", "type": "object", "properties": {"coins": {"title": "Coins", "default": 0, "type": "integer"}}}, "PriceDetail": {"title": "PriceDetail", "type": "object", "properties": {"coins": {"title": "Coins", "type": "integer"}, "price": {"title": "Price", "type": "integer"}, "shipping": {"title": "Shipping", "type": "integer"}, "shippingAsCoins": {"title": "Shippingascoins", "type": "integer"}}, "required": ["coins", "price", "shipping", "shippingAsCoins"]}, "PriceProperty": {"title": "PriceProperty", "type": "object", "properties": {"lockCost": {"$ref": "#/components/schemas/LockCost"}, "restricted": {"title": "Restricted", "type": "boolean"}, "price": {"title": "Price", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceDetail"}}}}, "PriceLevelSet": {"title": "PriceLevelSet", "type": "object", "properties": {"solo": {"$ref": "#/components/schemas/PriceProperty"}, "target": {"$ref": "#/components/schemas/PriceProperty"}, "join": {"$ref": "#/components/schemas/PriceProperty"}, "retail": {"$ref": "#/components/schemas/PriceProperty"}, "sale": {"$ref": "#/components/schemas/PriceProperty"}, "min": {"$ref": "#/components/schemas/PriceProperty"}, "max": {"$ref": "#/components/schemas/PriceProperty"}}}, "DealAttributes": {"title": "DealAttributes", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "optionCount": {"title": "Optioncount", "type": "integer"}}}, "DealClientListData": {"title": "DealClientListData", "type": "object", "properties": {"_id": {"pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "state": {"type": "string"}, "priceLevels": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "array", "items": {}}}}, "lowestPriceInUsdCent": {"type": "integer"}, "reallocatedPrice": {"type": "integer"}, "price": {"type": "integer"}, "fincoinPrice": {"type": "integer"}, "strikethroughPrice": {"type": "integer"}, "_productId": {"type": "string"}, "productName": {"type": "string"}, "image": {"type": "string"}, "rating": {"type": "number"}, "ratingCount": {"type": "integer"}, "reviews": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "retailPrice": {"type": "object", "additionalProperties": {"type": "integer"}}, "duoDeal": {"default": true, "type": "boolean"}, "gamified": {"default": false, "type": "boolean"}, "dealRace": {"default": false, "type": "boolean"}, "quantity": {"default": {}, "type": "object"}, "minLockCost": {"default": 1, "type": "integer"}, "priceCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "coinCostCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "imageKitEnabled": {"default": true, "type": "boolean"}, "lastUpdated": {"type": "integer"}, "duoDealEntries": {"type": "array", "items": {"type": "object"}}, "timerStart": {"type": "integer"}, "timerEnd": {"type": "integer"}, "endTime": {"type": "integer"}, "recentRacers": {"type": "object"}, "amountOfRacers": {"type": "integer"}, "usersWon": {"type": "object"}, "maxRacer": {"type": "integer"}, "leaderboardActivated": {"type": "boolean"}, "productLikes": {"type": "integer"}, "productCost": {"type": "integer"}, "supplierName": {"type": "string"}, "supplierStoreId": {"type": "string"}, "fromProducer": {"type": "string"}, "reqId": {"type": "string"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/DealAttributes"}}, "soldCount": {"type": "integer"}, "data": {"description": "Client List Data as a string.", "type": "string"}, "branded": {"type": "boolean"}, "startTime": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}, "hasAdultContent": {"type": "boolean"}}, "required": ["_id", "state", "_productId", "productName", "type"]}, "MyDealsV2Resp": {"title": "MyDealsV2Resp", "type": "object", "properties": {"deals": {"description": "List of deal client data", "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "UserProfileDealsV2Req": {"title": "UserProfileDealsV2Req", "type": "object", "properties": {"userId": {"description": "List user_ids", "type": "string"}, "dealType": {"description": "The deal type to return", "allOf": [{"$ref": "#/components/schemas/DealType"}]}, "channel": {"description": "Algo channel defined by client", "type": "string"}, "page": {"description": "The current page. Start from 1", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}}}, "UserProfileDealsV2Resp": {"title": "UserProfileDealsV2Resp", "type": "object", "properties": {"deals": {"description": "List of deal client data", "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "ShareAfterPayoutRewardType": {"title": "ShareAfterPayoutRewardType", "description": "An enumeration.", "enum": ["spin"], "type": "string"}, "DtoShareAfterPayoutReward": {"title": "DtoShareAfterPayoutReward", "type": "object", "properties": {"rewardType": {"description": "The reward type", "allOf": [{"$ref": "#/components/schemas/ShareAfterPayoutRewardType"}]}, "rewardAmount": {"description": "the amount", "type": "integer"}}}, "ShareAfterPayoutFollowupResp": {"title": "ShareAfterPayoutFollowupResp", "type": "object", "properties": {"rewards": {"description": "The rewards", "type": "array", "items": {"$ref": "#/components/schemas/DtoShareAfterPayoutReward"}}}}, "DtoHotKeywords": {"title": "DtoHotKeywords", "type": "object", "properties": {"keywords": {"description": "global hot keywords", "type": "array", "items": {"type": "string"}}, "hyperlinkKeywords": {"description": "Predefined link to some specific page", "type": "array", "items": {"type": "object"}}, "timestamp": {"description": "timestamp", "type": "integer"}}}, "MySearchKeywordsResp": {"title": "MySearchKeywordsResp", "type": "object", "properties": {"history": {"description": "The keywords history of the user", "type": "array", "items": {}}, "hotKeywords": {"description": "The hot keywords and hyperlink keywords", "allOf": [{"$ref": "#/components/schemas/DtoHotKeywords"}]}, "recommendedKeywords": {"description": "The keywords recommended for the user", "type": "array", "items": {"type": "string"}}}}, "BackgroundImageCategory": {"title": "BackgroundImageCategory", "type": "object", "properties": {"name": {"description": "name of the category", "type": "string"}, "images": {"description": "List of background images", "type": "array", "items": {"type": "string"}}}}, "BackgroundImagesResp": {"title": "BackgroundImagesResp", "type": "object", "properties": {"categories": {"description": "List of user's background category", "type": "array", "items": {"$ref": "#/components/schemas/BackgroundImageCategory"}}, "prefix": {"description": "Prefix of imgs", "type": "string"}}}, "UpdateUserAnalyticInfoReq": {"title": "UpdateUserAnalyticInfoReq", "type": "object", "properties": {"appsflyerId": {"description": "Appsflyer's unique id for device", "type": "string"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}