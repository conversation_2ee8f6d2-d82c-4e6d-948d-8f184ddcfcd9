from copy import deepcopy
from datetime import datetime, timezone
from time import sleep, time
from typing import Any, Dict, List, Optional, cast

import stripe
from bson.objectid import ObjectId
from newrelic.agent import add_custom_attribute, notice_error
from pymongo import DESCENDING
from pytasched import Task
from sherlock import Lock
from stripe import InvalidRequestError, RateLimitError

from commonlib import live_config
from commonlib.amplitude import AnalyticsManager
from commonlib.appsflyer.utils import get_subscribe_event
from commonlib.basic.maybe import maybe
from commonlib.basic.stripe_util import stripe_object_to_dict
from commonlib.basic.structured_log import (
    LogContentType,
    get_step_logger,
    get_structured_logger,
)
from commonlib.branch import generate_email_branch_link
from commonlib.clevertap.clevertap import Event
from commonlib.coindata import COIN_TYPE_FREE
from commonlib.db import get_db
from commonlib.duo_deal_auto_completion import (
    add_ticket,
    allow_non_ticket_auto_completion_feature,
    get_to_active_member_ticket_reward,
    get_to_trial_member_ticket_reward,
    spend_ticket,
)
from commonlib.duodeal import complete_duo_deal_entry
from commonlib.emails import queue_email
from commonlib.exceptions import BlidzException, ModelNotFoundError
from commonlib.facebook_analytics import FacebookAnalytics, FacebookEventType
from commonlib.formatting import format_price
from commonlib.models.address import ShippingAddress
from commonlib.models.cashback import CashBack
from commonlib.models.deal import Deal
from commonlib.models.duodeal_scenario import DuoDealScenario, DuoDealScenarioMask
from commonlib.models.duodealentry import DuoDealEntry
from commonlib.models.membership import Membership, MembershipStatus
from commonlib.models.order import Order
from commonlib.models.payment_hold import PaymentHold
from commonlib.models.payments import Transaction, TransactionStripe
from commonlib.models.payout.payout_transaction import PayoutTransaction
from commonlib.models.payout.spin_wheel import (
    PredefinedSpinLineItemReason,
    SpinProgress,
)
from commonlib.models.product import Product
from commonlib.models.shopping_cart import ShoppingCartItem
from commonlib.models.stripe import EventTypes, HandleStatus, StripeEvent
from commonlib.models.user import User
from commonlib.models.user_stats import UserStats
from commonlib.notifications.notification_manager import notification_manager
from commonlib.notifications.push_notification import PushNotification
from commonlib.notifications.utils import PushMessage
from commonlib.payment import send_clevertap_charged_event
from commonlib.paypal_payout import TransactionStatus
from commonlib.pricing.price_level import PriceLevel
from commonlib.publisher import publish_to_user_channel
from commonlib.pubsub.utils import (
    send_duodeal_algo_event,
    send_membership_invoice_algo_event,
)
from commonlib.pusu_models.update_stripe import UpdateStripe
from commonlib.season_system.season_progress import fill_up_season_progress
from commonlib.settings import (
    BRANCHIO_BASE_URL,
    COMPLETE_DUO_DEAL_GIFTS,
    DEFAULT_CURRENCY,
    DESKTOP_URL,
    DUO_DEAL_GIFT_CASHBACK,
    DUO_DEAL_GIFT_POINTS,
    DUO_DEAL_GIFT_SPIN,
    MEMBERSHIP_ITEM_ID,
    REASON_CHECKOUT_SUMMARY_POINT_REWARD,
)
from commonlib.settings_analytics_events import (
    EVENT_DUO_DEAL_COMPLETE_FOR_HOST,
    EVENT_DUO_DEAL_FAIL_FOR_HOST,
    EVENT_ORDER_SHIPPED,
    EVENT_STRIPE_INVOICE_CREATED,
    EVENT_STRIPE_INVOICE_DELETED,
    EVENT_STRIPE_INVOICE_FINALIZATION_FAILED,
    EVENT_STRIPE_INVOICE_FINALIZED,
    EVENT_STRIPE_INVOICE_MARKED_UNCOLLECTIBLE,
    EVENT_STRIPE_INVOICE_PAID,
    EVENT_STRIPE_INVOICE_PAYMENT_FAILED,
    EVENT_STRIPE_INVOICE_PAYMENT_SUCCEEDED,
    EVENT_STRIPE_PAYMENT_METHOD_ATTACHED,
    EVENT_STRIPE_PAYMENT_METHOD_DETACHED,
    EVENT_SUBSCRIPTION_CANCELED,
    AnalyticsEvent,
)
from commonlib.settings_mission import MissionAction
from commonlib.social_circles.graph.definitions import SocialCirclesGraph
from commonlib.social_circles.models.nodes import User as ArangoUser
from commonlib.social_circles.models.relationships import (
    DuoDealRelationship,
    SocialFeedRelationship,
)
from commonlib.social_circles.utils import add_arangodb_relation, add_arangodb_story
from commonlib.spin_wheel.user_spin_action import add_spin
from commonlib.stripe.definitions import (
    AuthenticationOccasion,
    InvoiceStatus,
    PaymentIntentStatus,
    PredefinedSubscriptionOccasion,
    SubscriptionStatus,
)
from commonlib.stripe.dispute import (
    preprocess_stripe_dispute,
    process_dispute_closed,
    process_dispute_updated,
)
from commonlib.stripe.helpers import (
    INVOICE_PAID_OCCASION,
    INVOICE_UPDATED_OCCASION,
    INVOICE_VOIDED_OCCASION,
    PAYMENT_ENTRY,
    PAYMENT_TRANSACTION_ID_IN_METADATA,
    SUBSCRIPTION_ID_IN_METADATA,
    SUBSCRIPTION_OCCASION,
    TRANSACTION_ID_IN_METADATA,
    USER_ID_IN_METADATA,
    cancel_payment_intent,
    cancel_subscription,
    cancel_subscription_immediately,
    check_should_cancel_subscription,
    create_payment_intent,
    determine_payment_intent_descriptor,
    determine_payment_intent_descriptor_for_BLIDZV,
    get_current_subscription_item,
    get_latest_charge_from_payment_intent,
    get_payment_brand,
    get_payment_details_from_payment_intent,
    get_price_from_subscription,
    get_subscription_latest_invoice_id,
    get_subscriptions_from_customer,
    get_transaction_ids_and_user_id,
    is_invoice_paid_in_time_duration,
    list_all_open_invoices,
    list_stripe_payment_intents,
    mark_open_invoices_uncollectible,
    modify_payment_intent_metadata,
    modify_payment_intent_shipping,
    modify_subscription,
    on_charge_refunded,
    pause_subscription_payment_collection,
    refund_stripe_payment_intent,
    retrieve_charge,
    retrieve_customer,
    retrieve_invoices,
    retrieve_stripe_payment_intent,
    retrieve_subscription,
    set_mixed_cart_order,
    set_subscription_to_user,
    void_invoice,
    void_open_invoices,
)
from commonlib.stripe.payment_method import sync_stripe_payment_methods
from commonlib.subscription_adapter.create_subscription_adapter import (
    create_subscription_adapter_by_membership,
)
from commonlib.subscription_adapter.error import SubscriptionNotFound
from commonlib.utils.request_id import create_request_id
from commonlib.utils.time import format_js_time, get_js_time, iso_8601_from_time
from commonlib.utils.time_constants import DAY, JS_DAY, MINUTE
from commonlib.utils.user import (
    add_stashed_cash_to_member,
    push_user_app_rating,
    stash_free_cash_from_member,
)
from commonlib.utils.utils import choose_value_for_env
from worker.app import app, get_logger
from worker.tasks_mission_system import mission_action_performed
from worker.tasks_payment import release_or_refund_payment_hold
from worker.tasks_reward_system import schedule_social_reward_worker
from worker.tasks_social import trigger_add_duo_deal_completetion_post
from worker.tasks_users import give_authentication_rewards, schedule_clevertap_event


def notify_customer_tracking_number(tracking_number: str, product_name: str, order: Order):
    logger = get_logger(__name__)

    user = order.load_user()
    order_id = order.get_id_assert()
    assert order.transactionId is not None

    transaction = Transaction()
    transaction.load_by_id(order.transactionId)

    shipping_address = ShippingAddress(order.shippingAddress)

    data = {
        "inAppUrl": f"/order/{order_id}",
        "campaign": Event.Names.TRACKING_INFO,
        "channel": "EmailOutreach",
        "feature": "orderDetail",
        "$og_title": product_name,
        "$og_image_url": order.get_image_url(),
    }
    order_url = generate_email_branch_link(data)
    # email
    assert order.price is not None
    email_data = {
        "orderId": order_id,
        "orderDate": format_js_time(order.created or 0),
        "product": product_name,
        "trackingCodes": tracking_number,
        "price": format_price(order.price, order.currency or DEFAULT_CURRENCY),
        "currency": order.currency,
        "paymentMethod": transaction.get_human_payment_method(),
        "shipping": format_price(order.get_shipping_price(), order.currency or DEFAULT_CURRENCY),
        "imageUrl": order.get_image_url(),
        "shippingAddress": shipping_address.to_string(),
        "orderUrl": order_url,
    }
    email_data.update(**shipping_address.get_data_separated())

    logger.info(f"Sending shipped email for order {order_id}")
    queue_email("tracking-info", email_data, order.db, user=user)

    # notification
    logger.info(f"Sending shipped notification for order {order_id}")
    notification_manager.send_notification(
        user=user,
        notification=PushNotification(
            **PushMessage.ORDER_SHIPPED,
            format_data={"productName": product_name},
            custom_params={
                "params": {
                    "orderId": order_id,
                }
            },
        ),
    )

    for item in order.orderItems:
        try:
            AnalyticsManager.send_event(
                user_id=user.get_id_assert(),
                analytics_event=EVENT_ORDER_SHIPPED,
                event_properties={
                    "supplier": item["product"]["supplier"]["name"],
                    "deal id": item["dealId"],
                    "deal type": item["dealType"],
                    "tracking": tracking_number,
                },
                data_for_general_properties={
                    "product": Product(item["product"]),
                    "order": order,
                    "order_item": item,
                },
            )
        except Exception as e:
            print(
                "Cannot send {event} to user {user}".format(
                    event=EVENT_ORDER_SHIPPED, user=user.get_id()
                )
            )
            print(e)


@app.task(ignore_result=True)
def process_order_email_receipt(order_id: str, transaction_id: str, user_id: str):
    try:
        user = User()
        user.load_by_id(user_id)
        order = Order()
        order.load_by_id(order_id)
        transaction = Transaction()
        transaction.load_by_id(transaction_id)
        order.process_email_receipt(transaction=transaction, db=get_db(), user=user)
    except Exception as err:
        raise err


@app.task(ignore_result=True)
def process_cashback(cashback_id: str, order_id: str, duo_deal_entry_id: str, user_id: Optional[str] = None):
    logger = get_structured_logger("process_cashback")
    log_data: LogContentType = {
        "order_id": order_id,
        "duo_deal_entry_id": duo_deal_entry_id,
    }

    if not user_id:
        return

    try:
        user = User()
        user.load_by_id(user_id)
        is_active_member = user.get_or_create_membership().is_active_member()
    except ModelNotFoundError:
        log_data["user_not_found"] = user_id
        is_active_member = False

    log_data["can_give_cashback"] = is_active_member

    if is_active_member:
        CashBack.update_one(
            {"_id": ObjectId(cashback_id)},
            {
                "$set": {
                    "order_id": order_id,
                    "duo_deal_entry_id": duo_deal_entry_id,
                    "claimed": True,
                }
            },
        )

        task = Task(
            "worker.tasks_users:give_cash_back.delay",
            kwargs={
                "order_id": order_id,
                "duo_deal_entry_id": duo_deal_entry_id,
            },
        )
        task.add()
        log_data["claimed"] = True
    logger.info(log_data)


@app.task(ignore_result=True)
def process_checkout_summary_point_rewards(user_id: str, order_id: str, duo_deal_entry_id: str, final_price: int):
    """
    Gives the point rewards for users in the checkout summary.

    :param user_id: The user id.
    :param order_id: The order id.
    :param duo_deal_entry_id: The duo deal entry id.
    :param final_price: The final price of the checkout.
    """
    from commonlib.utils.checkout import calculate_points_reward

    logger = get_structured_logger("process_checkout_summary_point_rewards")
    log_data: LogContentType = {
        "user_id": user_id,
        "order_id": order_id,
        "duo_deal_entry_id": duo_deal_entry_id,
        "final_price": final_price,
    }
    try:
        user = User()
        user.load_by_id(user_id)
    except ModelNotFoundError:
        user = None
        msg = f"Could not find user {user_id}"
        log_data["error"] = msg
        print(msg)

    if user and user.get_or_create_membership().is_active_member():
        points_to_give = calculate_points_reward(final_price)
        user.add_coins(
            count=points_to_give,
            reason=REASON_CHECKOUT_SUMMARY_POINT_REWARD,
            human_reason="Checkout summary point reward",
            coin_type=COIN_TYPE_FREE,
        )
        log_data["added_coins"] = points_to_give

    logger.info(log_data)


@app.task(ignore_result=True)
def update_user_order_queued(
    user_id: str, order_id: str, notification_data: dict, try_count: int
):
    order = Order()
    order.load_by_id(order_id)
    if order.status == order.STATUS_NEW:
        user = User()
        user.load_by_id(user_id)

        notification_manager.send_notification(
            user=user,
            notification=PushNotification(
                **PushMessage.ORDER_QUEUED,
                format_data=notification_data["data"],
                custom_params={
                    "params": notification_data["params"],
                },
            ),
        )

        if try_count == 1:
            delay_time = choose_value_for_env(DAY, 5 * MINUTE)
            task = Task(
                "worker.tasks_orders:update_user_order_queued.delay",
                kwargs={
                    "user_id": user_id,
                    "order_id": order_id,
                    "notification_data": notification_data,
                    "try_count": try_count + 1,
                },
                when=time() + float(delay_time),
            )
            task.add()


@app.task(ignore_result=True)
def auto_update_notification_after_status_changed(
    user_id: str, order_id: str, notification_data: dict, latest_status: str
):
    order = Order()
    order.load_by_id(order_id)
    if order.status == latest_status:
        message = None
        if order.status == order.STATUS_PROCESSED:
            message = PushMessage.ORDER_AFTER_PROCESSED
        if order.status == order.STATUS_SHIPPED:
            message = PushMessage.ORDER_AFTER_SHIPPED

        user = User()
        user.load_by_id(user_id)

        if message:
            notification_manager.send_notification(
                user=user,
                notification=PushNotification(
                    **message,
                    format_data=notification_data["data"],
                    custom_params={
                        "params": notification_data["params"],
                    },
                ),
            )


@app.task(ignore_result=True)
def on_duodeal_completed_for_started_user(
    order_id: str,
    completed_user_name: str,
    completed_user_id: Optional[str] = None,
):
    started_user_order = Order()
    started_user_order.load_by_id(order_id)

    deal_order_item = None
    deal_id = None
    for item in started_user_order.orderItems:
        deal_id = item.get("dealId")
        if deal_id:
            deal_order_item = item
            break

    if not deal_order_item:
        return
    assert deal_id is not None

    deal = Deal()
    deal.load_by_id(deal_id)
    user_id = started_user_order.userId
    assert user_id is not None
    started_user = User()
    started_user.load_by_id(user_id)

    transaction_id = deal_order_item.get("transactionId", "Unknown")

    duo_deal_entry = DuoDealEntry()
    if deal_order_item.get("duoDealEntryId"):
        duo_deal_entry_id = deal_order_item["duoDealEntryId"]
        duo_deal_entry.load_by_id(duo_deal_entry_id)

    # give some gifts

    if duo_deal_entry:
        _give_checkout_gifts(
            started_user,
            started_user_order,
            duo_deal_entry,
            deal_id,
            transaction_id,
            duo_deal_entry.gifts,
            deal_order_item.get("cashbackId"),
        )

        # Add the relation for social circles
        # startedUser -- buy_together_nonpaying --> completedUser
        if started_user_order.product and completed_user_id:
            started_user_id = duo_deal_entry.get_started_user_id()
            # For some reason the duo_deal_entry.completedUser is not set here,
            # so use the ID passed to this function.
            relationship_type = DuoDealRelationship.Types(started_user_order.product[0]["duoDealScenario"])
            if started_user_id:
                add_arangodb_relation(
                    started_user_id,
                    completed_user_id,
                    DuoDealRelationship,
                    relationship_type,
                    [duo_deal_entry.get_id_assert()],
                )
                # Add an is_friend relationship if it's JEP or JEN.
                if relationship_type in {
                    DuoDealRelationship.Types.JOIN_EXTERNAL_NONPAYING.value,
                    DuoDealRelationship.Types.JOIN_EXTERNAL_PAYING.value,
                }:
                    add_arangodb_relation(
                        completed_user_id,
                        started_user_id,
                        SocialFeedRelationship,
                        SocialFeedRelationship.Types.IS_FRIEND,
                        {},
                    )
                    add_arangodb_relation(
                        started_user_id,
                        completed_user_id,
                        SocialFeedRelationship,
                        SocialFeedRelationship.Types.IS_FRIEND,
                        {},
                    )
    # mission

    mission_action_performed.delay(user_id, MissionAction.DUODEAL_COMPLETED_FOR_HOST.value, deal_id)

    # notification and email

    notification_manager.send_notification(
        user=started_user,
        notification=PushNotification(
            **PushMessage.DUO_DEAL_COMPLETE,
            format_data={
                "joined_user_name": completed_user_name,
            },
            custom_params={
                "params": {
                    "orderId": started_user_order.get_id(),
                }
            },
        ),
    )
    started_user_order_transaction = started_user_order.get_transaction()
    started_user_shipping_address = ShippingAddress(started_user_order.shippingAddress)

    order_url = generate_email_branch_link(
        {
            "inAppUrl": f"/order/{order_id}",
            "campaign": Event.Names.DUO_DEAL_COMPLETE,
            "channel": "EmailOutreach",
            "feature": "orderDetail",
        },
        deal=deal,
    )
    assert started_user_order.price is not None
    email_data = {
        "dealId": deal_id,
        "duoDealEntryId": duo_deal_entry.get_id(),
        "inviteeName": completed_user_name,
        "orderId": started_user_order.get_id(),
        "currency": started_user_order.currency,
        "price": format_price(started_user_order.price, started_user_order.currency or DEFAULT_CURRENCY),
        "shippingAddress": started_user_shipping_address.to_string(),
        "product": deal.product["name"] if deal.product else None,
        "shipping": format_price(
            started_user_order.get_shipping_price(), started_user_order.currency or DEFAULT_CURRENCY
        ),
        "orderDate": format_js_time(started_user_order.created or 0),
        "paymentMethod": started_user_order_transaction.get_human_payment_method(),
        "imageUrl": deal.get_public_product_image(),
        "orderUrl": order_url,
    }
    email_data.update(**started_user_shipping_address.get_data_separated())
    if duo_deal_entry.isOnboardingDuoDeal and not started_user_order.shippingAddress:
        queue_email("onboarding-duo-deal-complete-require-fill-address", email_data, user=started_user)
    else:
        queue_email("duo-deal-complete", email_data, user=started_user)

    # record in mongo
    started_user.record_successful_duo_deal_count()
    stats = UserStats.record(started_user.get_id_assert(), UserStats.COMPLETED_DUO_DEAL_COUNT)
    if stats and stats.completed_duo_deal_count == 1:
        push_user_app_rating(started_user)

    # give referral reward of first completes a duo deal AS A HOST for the first time ever
    schedule_social_reward_worker(
        "give_first_host_duo_deal_be_completed_reward",
        {
            "user_id": started_user.get_id_assert(),
        },
    )
    # send a post to social feed
    trigger_add_duo_deal_completetion_post.delay(user_id, duo_deal_entry.get_id_assert())

    # Send to analytics
    try:
        AnalyticsManager.send_event(
            user_id=user_id or "",
            analytics_event=EVENT_DUO_DEAL_COMPLETE_FOR_HOST,
            event_properties={"source": "purchase"},
            data_for_general_properties={
                "deal": deal,
                "order": started_user_order,
                "order_item": deal_order_item,
                "product": Product(deal_order_item["product"]),
                "duo_deal_entry": duo_deal_entry,
            },
        )
    except Exception as e:
        print(f"Cannot send {EVENT_DUO_DEAL_COMPLETE_FOR_HOST} to user {user_id}")
        print(e)

    send_duodeal_algo_event(
        event_type="completion_host", user=started_user, order=started_user_order
    )

    if duo_deal_entry.isOnboardingDuoDeal:
        try:
            schedule_clevertap_event.delay(
                [user_id],
                Event.Names.NOTIFICATION_ONBOARDING_DEAL_COMPLETE,
                data={
                    "deal_id": deal_id,
                    "duo_deal_entry_id": duo_deal_entry.get_id_assert(),
                    "product_name": deal.get_product().name,
                    "product_image_url": deal.get_public_product_image(),
                },
            )
        except Exception as e:
            print(f"Cannot send {Event.Names.NOTIFICATION_ONBOARDING_DEAL_COMPLETE} to user {user_id}")
            print(e)


@app.task(ignore_result=True)
def on_duodeal_completed_for_completed_user(order_id: str, started_user_name: str):
    completed_user_order = Order()
    completed_user_order.load_by_id(order_id)

    deal_order_item = None
    deal_id = None
    for item in completed_user_order.orderItems:
        deal_id = item.get("dealId")
        if deal_id:
            deal_order_item = item
            break

    if not deal_order_item:
        return
    assert deal_id is not None

    deal = Deal()
    deal.load_by_id(deal_id)
    user_id = completed_user_order.userId
    assert user_id is not None
    completed_user = User()
    completed_user.load_by_id(user_id)

    transaction_id = deal_order_item.get("transactionId", "Unknown")

    duo_deal_entry = DuoDealEntry()
    if deal_order_item.get("duoDealEntryId"):
        duo_deal_entry_id = deal_order_item["duoDealEntryId"]
        duo_deal_entry.load_by_id(duo_deal_entry_id)

    # Add the relation for social circles
    # completedUser -- join_internal_paying/join_internal_nonpaying/join_external_* --> startedUser
    try:
        if completed_user_order.product:
            started_user_id = duo_deal_entry.get_started_user_id()
            relationship_type = DuoDealRelationship.Types(completed_user_order.product[0]["duoDealScenario"])
            if started_user_id:
                add_arangodb_relation(
                    user_id,
                    started_user_id,
                    DuoDealRelationship,
                    relationship_type,
                    [duo_deal_entry.get_id_assert()],
                )
                # Add an is_friend relationship if it's JEP or JEN.
                if relationship_type in {
                    DuoDealRelationship.Types.JOIN_EXTERNAL_NONPAYING.value,
                    DuoDealRelationship.Types.JOIN_EXTERNAL_PAYING.value,
                }:
                    add_arangodb_relation(
                        user_id,
                        started_user_id,
                        SocialFeedRelationship,
                        SocialFeedRelationship.Types.IS_FRIEND,
                        {},
                    )
                    add_arangodb_relation(
                        started_user_id,
                        user_id,
                        SocialFeedRelationship,
                        SocialFeedRelationship.Types.IS_FRIEND,
                        {},
                    )
    except Exception:
        notice_error()

    try:
        # Give the checkout gifts here.
        _give_checkout_gifts(
            completed_user,
            completed_user_order,
            duo_deal_entry,
            deal_id,
            transaction_id,
            COMPLETE_DUO_DEAL_GIFTS,
            deal_order_item.get("cashbackId"),
        )
    except Exception:
        notice_error()

    try:
        notification_manager.send_notification(
            user=completed_user,
            notification=PushNotification(
                **PushMessage.DUO_DEAL_COMPLETE_FOR_COMPLETED_USER,
                format_data={
                    "started_user_name": started_user_name,
                },
                custom_params={
                    "params": {
                        "dealId": duo_deal_entry.dealId,
                        "duoDealEntryId": duo_deal_entry.get_id(),
                    }
                },
            ),
        )
    except Exception:
        notice_error()

    try:
        completed_user.record_successful_duo_deal_count()
    except Exception:
        notice_error()

    try:
        stats = UserStats.record(completed_user.get_id_assert(), UserStats.COMPLETED_DUO_DEAL_COUNT)
        if stats and stats.completed_duo_deal_count == 1:
            push_user_app_rating(completed_user)
    except Exception:
        notice_error()

    if duo_deal_entry.isOnboardingDuoDeal:
        try:
            schedule_clevertap_event.delay(
                [user_id],
                Event.Names.NOTIFICATION_ONBOARDING_DEAL_COMPLETE,
                data={
                    "deal_id": deal_id,
                    "duo_deal_entry_id": duo_deal_entry.get_id_assert(),
                    "product_name": deal.get_product().name,
                    "product_image_url": deal.get_public_product_image(),
                },
            )
        except Exception:
            notice_error()


@app.task(ignore_result=True)
def on_duodeal_failed_for_started_user(order_id: str):
    started_user_order = Order()
    started_user_order.load_by_id(order_id)

    deal_order_item: Optional[Dict[str, Any]] = None
    deal_id = None
    for item in started_user_order.orderItems:
        if item.get("dealId"):
            deal_order_item = item
            deal_id = item["dealId"]
            break

    if not deal_order_item or not deal_id:
        return

    user_id = started_user_order.userId
    if not user_id:
        return

    deal = Deal()
    deal.load_by_id(deal_id)
    started_user = User()
    started_user.load_by_id(user_id)

    duo_deal_entry = DuoDealEntry()
    if deal_order_item.get("duoDealEntryId"):
        duo_deal_entry_id = deal_order_item["duoDealEntryId"]
        duo_deal_entry.load_by_id(duo_deal_entry_id)

    try:
        UserStats.record(started_user.get_id_assert(), UserStats.FAILED_DUO_DEAL_COUNT)
    except Exception:
        notice_error()

    try:
        notification_manager.send_notification(
            user=started_user,
            notification=PushNotification(
                **PushMessage.DUO_DEAL_REFUNDED,
                custom_params={
                    "params": {
                        "orderId": started_user_order.get_id(),
                    }
                },
            ),
        )
    except Exception:
        notice_error()

    try:
        AnalyticsManager.send_event(
            user_id=user_id,
            analytics_event=EVENT_DUO_DEAL_FAIL_FOR_HOST,
            event_properties={"source": "purchase"},
            data_for_general_properties={
                "deal": deal,
                "order": started_user_order,
                "order_item": deal_order_item,
                "product": Product(deal_order_item["product"]),
                "duo_deal_entry": duo_deal_entry,
            },
        )
    except Exception:
        notice_error()

    if duo_deal_entry.isOnboardingDuoDeal:
        try:
            schedule_clevertap_event.delay(
                [user_id],
                Event.Names.NOTIFICATION_ONBOARDING_DEAL_FAIL,
                data={
                    "deal_id": deal_id,
                    "duo_deal_entry_id": duo_deal_entry.get_id_assert(),
                    "product_name": deal.get_product().name,
                    "product_image_url": deal.get_public_product_image(),
                },
            )
        except Exception:
            notice_error()

    send_duodeal_algo_event(
        event_type="failed_host", user=started_user, order=started_user_order
    )


@app.task(ignore_result=True)
def on_duodeal_accessed_user(duo_deal_entry_id: str, accessed_user_id: str):
    """
    Runs when a user accesses a duo deal.

    :param duo_deal_entry_id: The duo deal entry id.
    :param accessed_user_id: The accessing user's ID.
    """

    duo_deal_entry = DuoDealEntry()
    duo_deal_entry.load_by_id(duo_deal_entry_id)

    # startedUser -- share_duo_invite --> accessedUser
    if duo_deal_entry.startedUser and duo_deal_entry.startedUser.id:
        add_arangodb_relation(
            duo_deal_entry.startedUser.id,
            accessed_user_id,
            DuoDealRelationship,
            DuoDealRelationship.Types.SHARE_DUO_INVITE,
            [duo_deal_entry_id],
        )
        # accessedUser -- access_duo_invite --> startedUser
        add_arangodb_relation(
            accessed_user_id,
            duo_deal_entry.startedUser.id,
            DuoDealRelationship,
            DuoDealRelationship.Types.ACCESS_DUO_INVITE,
            [duo_deal_entry_id],
        )


@app.task(ignore_result=True)
def on_duodeal_created(duo_deal_entry_id: str):
    """
    Task that is run when a duo deal is created.

    :param duo_deal_entry_id: The duo deal entry id.
    """
    from worker.tasks_users import schedule_clevertap_event

    duo_deal_entry = DuoDealEntry()
    duo_deal_entry.load_by_id(duo_deal_entry_id)

    user_id = duo_deal_entry.get_started_user_id()
    if not user_id:
        return

    # The user creates a "story" when the duo deal is started.
    add_arangodb_story(user_id, duo_deal_entry)
    # When creating a new story, remove all the has_viewed relations to the user.
    user = ArangoUser.find_one({"_key": user_id})
    relations = DuoDealRelationship.find(
        {"_to": user.id_, "type": DuoDealRelationship.Types.HAS_VIEWED.value}
    )
    DuoDealRelationship.delete_many(list(relations))

    # mission

    mission_action_performed.delay(user_id, MissionAction.DUODEAL_STARTED.value, duo_deal_entry.dealId)

    # spin reward
    if duo_deal_entry.gifts and DUO_DEAL_GIFT_SPIN in duo_deal_entry.gifts:
        add_spin(user_id, reason=PredefinedSpinLineItemReason.CREATE_DUO_DEAL_REWARD.value)

    # Send a CleverTap event to each of the user's friends
    result = SocialCirclesGraph.get_user_friends(user_id)
    if result is None:
        return
    friends = list(result)
    if friends:
        host_user = User()
        host_user.load_by_id(user_id)
        # Send the notification if the user hasn't disabled it.
        send_notification = host_user.settings.get("teamBuyFriendNotifications", True)
        if send_notification:
            # Collect the event data to be sent.
            data = {
                "deal_id": duo_deal_entry.dealId,
                "duo_deal_entry_id": duo_deal_entry_id,
                "host_user_id": host_user.get_id(),
                "host_email": host_user.get_email(),
                "host_full_name": host_user.get_name(),
                "host_team_buy_invite_url": duo_deal_entry.inviteUrl,
                "host_team_buy_price": "",
                "product_image_url": "",
                "product_name": "",
            }
            # Get the deal and the price data for it.
            deal = duo_deal_entry.get_deal()
            if deal:
                price_data = deal.get_price_data(
                    PriceLevel.TEAM_BUY, host_user.get_currency(), host_user
                )
                # CleverTap want's this in dollars.
                data["host_team_buy_price"] = price_data["price"] / 100

            # Get the product for the deal, and some product data.
            product = duo_deal_entry.get_product()
            if product:
                data["product_image_url"] = product.get_default_image_url()
                data["product_name"] = product.name

            # Send the event to each of the friends.
            schedule_clevertap_event.delay(
                friends,
                Event.Names.NOTIFICATION_FRIEND_TEAM_BUY,
                data=data,
            )

    if duo_deal_entry.isOnboardingDuoDeal:
        try:
            product = duo_deal_entry.get_product()
            schedule_clevertap_event.delay(
                [user_id],
                Event.Names.NOTIFICATION_ONBOARDING_DEAL_START,
                data={
                    "deal_id": duo_deal_entry.dealId,
                    "duo_deal_entry_id": duo_deal_entry.get_id_assert(),
                    "product_name": product.name if product else "",
                    "product_image_url": product.get_default_image_url() if product else "",
                },
            )
        except Exception as e:
            print(f"Cannot send {Event.Names.NOTIFICATION_ONBOARDING_DEAL_START} to user {user_id}")
            print(e)


def _remove_shopping_cart_item_on_payment_complete(user_id: str, order: Order):
    order_item = order.get_item_by_type(Order.TYPE_DEAL)
    if not order_item:
        return
    deal_id = order_item.get("dealId")
    duo_deal_scenario_str = order_item.get("duoDealScenario")
    if not deal_id or not duo_deal_scenario_str:
        return
    duo_deal_scenario = DuoDealScenario(duo_deal_scenario_str)
    if duo_deal_scenario in [DuoDealScenario.INVALID, DuoDealScenario.NOT_DUO_DEAL]:
        return
    pl = duo_deal_scenario.to_price_level()
    if not pl:
        return
    ShoppingCartItem.remove_item(user_id, deal_id, pl)


@app.task(ignore_result=True)
def on_payment_complete(user_id: str, order_id: str, transaction_id: str):
    """
    Task that runs when the payment is completed.

    :param user_id: The user_id who completed the payment.
    :param order_id: The created order id.
    :param transaction_id: The transaction id for the order.
    """

    remove_abandon_checkout_email(user_id)

    order = Order()
    order.load_by_id(order_id)
    _remove_shopping_cart_item_on_payment_complete(user_id, order)
    user = User()
    user.load_by_id(user_id)
    send_duodeal_algo_event(event_type="purchase", user=user, order=order)


@app.task(ignore_result=True)
def cancel_order_and_refund_to_paypal(
    paypal_order_id: str,
    transaction_id: str,
    reason: str,
    paypal_settings: Dict[str, Any],
):
    from commonlib.paypal import refund_captured_payment
    order = Order()
    try:
        order.load_by_transaction_id(transaction_id)
        with order.lock_and_reload():
            order.refund_in_blidz_cash(
                human_reason=f"cancel order and refund: {reason}",
                refund_real_money_to_blidz_cash=False,
                take_process_fee=False,
            )
            order.save(lock=False)
    except ModelNotFoundError:
        pass

    # Only If the order is successfully refunded, then refund the money to paypal

    order_id = order.get_id()
    assert order_id is not None
    refund_captured_payment(blidz_order_id=order_id, paypal_order_id=paypal_order_id, settings=paypal_settings)

    with Transaction.lock_and_load(transaction_id) as trans:
        trans.update_status(Transaction.STATUS_REFUNDED)
        trans.save(lock=False)


@app.task(ignore_result=True)
def handle_abandon_checkout(user_id: str, deal_id: str, checkout_price: float):
    """
    Handles the abandon checkout logic.

    When a user starts the checkout session, we schedule the abandon checkout
    email 2h later. If at any time before this the user makes a purchase
    for the product, we remove the abandon checkout email from the queue.

    :param user_id: The logged-in user's ID
    :param deal_id: The deal ID from checkout
    :param checkout_price: The price in checkout, NOTE: This is in dollars, NOT cents.
    """

    config = live_config.get("email_config")
    delay_in_seconds = config.get("abandon_checkout", {}).get("send_timeout")

    # Make sure we have all the necessary data.
    if user_id and deal_id:
        # Schedule the abandon checkout email to be sent in X seconds,
        # but only if the task doesn't exist yet.
        task_data = get_abandon_checkout_task(user_id)
        if task_data is None:
            task = Task(
                "worker.tasks_orders:queue_abandon_checkout_email.delay",
                kwargs={
                    "user_id": user_id,
                    "deal_id": deal_id,
                    "price": checkout_price,
                },
                seconds=choose_value_for_env(
                    delay_in_seconds, 10 * MINUTE
                ),  # Send this in X seconds.
            )
            task.add()


@app.task(ignore_result=True)
def queue_abandon_checkout_email(user_id: str, deal_id: str, price: float):
    """
    Queues the abandon checkout email to be sent.

    :param user_id: The user id to send the email to.
    :param deal_id: The deal id we are sending the email for.
    :param price: The price in checkout, NOTE: This is in dollars, NOT cents.
    """

    user = User()
    user.load_by_id(user_id)

    deal = Deal()
    deal.load_by_id(deal_id)

    product = deal.get_product()
    image_url = (
        product.get_default_image_url() or ""
    )  # Don't send null values.

    # Create the branch links.
    product_checkout_url = generate_email_branch_link(
        {
            "inAppUrl": "/checkout/duoDeal",
            "params": {
                "dealId": deal_id,
                "buyType": "teamBuy",
                "buySource": "internal",
            },
            "campaign": Event.Names.ABANDON_CHECKOUT,
            "channel": "EmailOutreach",
            "feature": "checkout",
        },
        product=product,
    )
    product_url = generate_email_branch_link(
        {
            "inAppUrl": f"/deal/{deal_id}",
            "campaign": Event.Names.ABANDON_CHECKOUT,
            "channel": "EmailOutreach",
            "feature": "dealDetails",
        },
        product=product,
    )
    data = {
        "dealId": deal_id,
        "product": product.name,
        "imageUrl": image_url,
        "price": price,
        "productCheckoutUrl": product_checkout_url,
        "productUrl": product_url,
    }

    # Queue the email for sending, if there is no order for the user/deal.
    if not _has_order(user_id, deal_id):
        queue_email("abandon-checkout", data, user=user)


@app.task(ignore_result=True)
def remove_abandon_checkout_email(user_id: str):
    """
    Removes the scheduled abandon checkout email from the task queue.

    :param user_id: The kwargs.user_id used for the task.
    """

    _db = get_db()

    _db.get_collection("tasks").delete_one(
        {
            "task": "worker.tasks_orders:queue_abandon_checkout_email.delay",
            "kwargs.user_id": user_id,
        }
    )


def get_abandon_checkout_task(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Returns the abandon checkout task if found, None otherwise.

    :param user_id: The user id for the task.
    :return: The task dictionary, or None if not found.
    """

    _db = get_db()

    return _db.get_collection("tasks").find_one(
        {
            "task": "worker.tasks_orders:queue_abandon_checkout_email.delay",
            "kwargs.user_id": user_id,
        }
    )


def _has_order(user_id: str, deal_id: str) -> bool:
    """
    Checks if the given user has an order for the given deal.

    :param user_id: The user id.
    :param deal_id: The deal id.
    :return: True if the user/deal has an order, False otherwise.
    """

    _db = get_db()

    result = _db.get_collection("orders").find_one(
        {"userId": user_id, "orderItems.dealId": deal_id}
    )

    return result is not None


def _update_transactions(
    transaction_stripes: List[Optional[TransactionStripe]],
    payment_intent: Optional[stripe.PaymentIntent],
    invoice: Optional[stripe.Invoice],
):
    """
    Update the status the transaction and transaction_stripes

    Assume all item in the transaction_stripes list are related to the same payment_intent or the same invoice.
    It happens at the "one-time and subscription combined" checkout
    """

    if not payment_intent and not invoice:
        raise BlidzException(
            message="Either payment intent or invoice must exists",
            status=400,
            code="neither_payment_intent_nor_invoice_exists"
        )
    elif invoice and not payment_intent and invoice.payment_intent:
        if isinstance(invoice.payment_intent, str):
            payment_intent = retrieve_stripe_payment_intent(invoice.payment_intent)
        else:
            payment_intent = invoice.payment_intent

    for transaction_stripe in transaction_stripes:
        if not transaction_stripe:
            continue

        # Handle Transaction
        transaction_id = transaction_stripe.transactionId
        if not transaction_id:
            return

        override_existing_invoice = False
        override_existing_payment_intent = False
        new_transaction_status = None

        if invoice:
            if invoice.status == InvoiceStatus.PAID.value:
                new_transaction_status = Transaction.STATUS_PAID
                override_existing_invoice = True
            elif not transaction_stripe.invoice:
                override_existing_invoice = True
            elif transaction_stripe.invoice.status != InvoiceStatus.PAID.value:
                override_existing_invoice = True

        if payment_intent:
            final_statuses = [
                PaymentIntentStatus.SUCCEEDED.value,
                PaymentIntentStatus.CANCELED.value,
                PaymentIntentStatus.PAYMENT_FAILED.value,
            ]

            # In some situation, payment_intent may not exist
            # For example, "one-time and subscription combined" payment with total amout as 0
            if payment_intent.status in final_statuses:
                override_existing_payment_intent = True
            elif (
                transaction_stripe.paymentIntent
                and transaction_stripe.paymentIntent.status not in final_statuses
            ):
                override_existing_payment_intent = True

            if payment_intent.status == PaymentIntentStatus.SUCCEEDED.value:
                new_transaction_status = Transaction.STATUS_PAID
            elif payment_intent.status == PaymentIntentStatus.PAYMENT_FAILED.value:
                new_transaction_status = Transaction.STATUS_FAILED
            elif payment_intent.status == PaymentIntentStatus.CANCELED.value:
                new_transaction_status = Transaction.STATUS_CANCELED

        if override_existing_invoice or override_existing_payment_intent:
            with transaction_stripe.lock_and_reload():
                if override_existing_invoice and invoice:
                    transaction_stripe.invoice = invoice
                if override_existing_payment_intent and payment_intent:
                    transaction_stripe.paymentIntent = payment_intent
                    if payment_intent.payment_method:
                        if isinstance(payment_intent.payment_method, str):
                            transaction_stripe.paymentMethod = payment_intent.payment_method
                        else:
                            transaction_stripe.paymentMethod = payment_intent.payment_method.id
                transaction_stripe.save(lock=False)

        if not new_transaction_status:
            return

        with Transaction.lock_and_load(transaction_id) as transaction:
            if new_transaction_status == transaction.status:
                return

            transaction.status = new_transaction_status
            if payment_intent:
                transaction.paymentDetails = get_payment_details_from_payment_intent(payment_intent)

            transaction.save(lock=False)


def _release_payment_hold_for_canceled_payment_intent(user_id: str, payment_intent: stripe.PaymentIntent):
    # Check if this payment intent is a hold for the user.
    # This means that the payment intent is canceled by Stripe automatically, or someone manually canceled.
    payment_hold: Optional[PaymentHold] = PaymentHold.find_one(
        {"user_id": user_id, "payment_intent_data.id": payment_intent.id}
    )
    if payment_hold:
        # Set the released_at timestamp.
        payment_hold.set_released_at(payment_intent)
        return True

    return False


def _send_charge_failed_events(charge: stripe.Charge):
    from worker.tasks_users import schedule_clevertap_event

    # Get the user id from the charge object metadata.
    metadata = cast(Dict[str, str], charge.metadata)
    if not metadata:
        return

    print(f"Charges event: Charge '{charge.id}' failed!")
    user_id = metadata.get(USER_ID_IN_METADATA)
    transaction_id = metadata.get(TRANSACTION_ID_IN_METADATA)

    # Only send event to CT if we have a user who we can connect the event to.
    if not user_id:
        return

    # Collect necessary data to send to CleverTap
    product_image_url = ""
    product_name = ""
    product_url = (
        BRANCHIO_BASE_URL  # Default to the app link, so we just open the app.
    )

    item = None
    product = None
    # Load the transaction.
    if transaction_id:
        transaction = Transaction()
        try:
            transaction.load_by_id(transaction_id)
            if transaction.purchasedItems:
                item = transaction.purchasedItems[0]
                product_name = item["name"]
                if item["type"] == Order.TYPE_DEAL:
                    if "product" in item:
                        product = Product(item["product"])
                    else:
                        product = Product()
                        product.load_by_id(item["productId"])

                    product_image_url = product.get_default_image_url() or ""
                    if item["dealId"]:
                        product_url = f"{DESKTOP_URL}deal/{item['dealId']}"
        except ModelNotFoundError:
            print(
                f"Charges event: No transaction found with id {transaction_id}"
            )
    deal_id = None
    if item:
        deal_id = item.get("dealId")

    product_checkout_url = generate_email_branch_link(
        {
            "inAppUrl": "/checkout/duoDeal",
            "params": {
                "dealId": deal_id,
                "buyType": "teamBuy",
                "buySource": "internal",
            },
            "campaign": Event.Names.EMAIL_PAYMENT_ERROR,
            "channel": "EmailOutreach",
            "feature": "checkout",
        },
        product=product,
    )
    schedule_clevertap_event.delay(
        [user_id],
        Event.Names.EMAIL_PAYMENT_ERROR,
        data={
            "deal_id": deal_id,
            "error_message": "Charge failed",
            "failure_code": charge.failure_code,
            "failure_message": charge.failure_message,
            "product_image_url": product_image_url,
            "product_name": product_name,
            "product_url": product_url,
            "product_checkout_url": product_checkout_url,
        },
    )
    print(
        f"Charges event: CleverTap event {Event.Names.EMAIL_PAYMENT_ERROR.value} scheduled."
    )


def _check_default_payment_method(user_id: str, payment_intent: stripe.PaymentIntent):
    last_charge = get_latest_charge_from_payment_intent(payment_intent)

    if not last_charge:
        return

    payment_method_id = last_charge.payment_method
    assert payment_method_id is not None
    payment_method_card = None
    if last_charge.payment_method_details:
        payment_method_card = last_charge.payment_method_details.card

    user_default_method_changed = False
    should_change_default = False
    with User.lock_and_load(user_id) as user:
        # Get the current default payment method id
        current_default_pm = user.stripe.get("defaultPaymentMethod")
        current_funding = None
        if current_default_pm:
            # Get the current default payment method funding.
            current_funding = user.stripe.get("paymentMethods", {}).get(current_default_pm, {}).get("funding")

        card_details = payment_method_card
        if card_details:
            # If the new card is credit and the previous card is not credit, update the default.
            # Otherwise, don't update the default.
            if card_details.funding == "credit" and current_funding != "credit":
                should_change_default = True

        if should_change_default and current_default_pm != payment_method_id:
            user.stripe["defaultPaymentMethod"] = payment_method_id
            user.save(lock=False)
            user_default_method_changed = True

    customer_id = user.stripe["id"]
    if user_default_method_changed:
        stripe.Customer.modify(customer_id, invoice_settings={"default_payment_method": payment_method_id})

        publish_to_user_channel(
            user_id=user.get_id_assert(),
            content=UpdateStripe.from_stripe_data(user.stripe),
        )
        # TODO: remove this after the migration
        user.push_to_clients(stripe=True)

    # If the user is a member, let's update the subscription payment method as well.
    subscription_id = user.stripe.get("subscription_id")
    if subscription_id:
        # Get the current subscription and check if the default payment method is different.
        subscription = retrieve_subscription(subscription_id)
        if subscription:
            if user_default_method_changed and subscription.default_payment_method != payment_method_id:
                modify_subscription(
                    subscription_id,
                    default_payment_method=payment_method_id
                )


def _handle_stripe_payment_intent(payment_intent: stripe.PaymentIntent, event_id: str, event_type: EventTypes):
    """
    This task is called whenever the stripe payment is successful in the webhook.

    It's called before creating the order for the payment.

    :param user_id: The logged-in user's ID.
    :param payment_intent_id: The initiated payment intent ID.
    """

    logger = get_structured_logger("tasks_orders")

    log_data: LogContentType = {
        "action": "handle_stripe_payment_intent",
        "payment_intent_id": payment_intent.id,
        "event_id": event_id,
        "event": event_type.value,
    }

    payment_intent_id = payment_intent.id

    one_time_txn_id, _, user_id = get_transaction_ids_and_user_id(
        payment_intent=payment_intent
    )

    log_data["user_id"] = user_id
    log_data["one_time_transaction_id"] = one_time_txn_id

    if (
        event_type == EventTypes.PAYMENT_INTENT_PAYMENT_FAILED
        and user_id
        and payment_intent.invoice
    ):
        max_attempts = 8
        # reset invoice billing cycle if there are 8 failed attempt
        result = StripeEvent.find(
            {
                "type": EventTypes.PAYMENT_INTENT_PAYMENT_FAILED,
                "data.object.invoice": payment_intent.invoice,
            },
            limit=max_attempts,
        )
        if len(list(result)) >= max_attempts:
            try:
                _reset_billing_cycle_for_invoice_with_multiple_attempt(user_id, log_data)
            except Exception:
                logger.info(log_data)
                raise

    # The payment intent is canceled.
    if (
        event_type == EventTypes.PAYMENT_INTENT_CANCELED
        and user_id
        and not payment_intent.invoice
    ):
        _release_payment_hold_for_canceled_payment_intent(user_id, payment_intent)

    if not one_time_txn_id and not user_id:
        logger.info(log_data)
        return

    assert payment_intent.metadata is not None

    add_custom_attribute("stripe_event_id", event_id)
    add_custom_attribute("payment_intent_id", payment_intent.id)
    add_custom_attribute("one_time_txn_id", one_time_txn_id)
    add_custom_attribute("user_id", user_id)

    # update transaction status
    try:
        if one_time_txn_id:
            transaction_stripe = TransactionStripe()
            transaction_stripe.load_by_transaction_id(one_time_txn_id)
            _update_transactions([transaction_stripe], payment_intent, None)
    except Exception as ex:
        log_data["error"] = str(ex)
        log_data["payment_intent_id"] = payment_intent_id

    if event_type == EventTypes.PAYMENT_INTENT_PAYMENT_FAILED:
        latest_charge = get_latest_charge_from_payment_intent(payment_intent)
        if latest_charge:
            try:
                _send_charge_failed_events(latest_charge)
            except Exception:
                notice_error()
            payment_intent.latest_charge = latest_charge  # so that later we don't need to fetch the charge again

    if payment_intent.status != PaymentIntentStatus.SUCCEEDED.value:
        logger.info(log_data)
        return

    # set mixed cart order to membership
    if payment_intent.metadata.get(SUBSCRIPTION_ID_IN_METADATA) and user_id and one_time_txn_id:
        set_mixed_cart_order(user_id, one_time_txn_id)

    if not user_id:
        logger.info(log_data)
        return

    if not payment_intent.invoice and one_time_txn_id:  # if there is invoice, it will be handled in the INVOICE_PAID event
        transaction = Transaction()
        transaction.load_by_id(one_time_txn_id)
        send_clevertap_charged_event(user_id, transaction, None, source="order")

    try:
        _check_default_payment_method(user_id, payment_intent)
    except Exception as ex:
        log_data["error"] = str(ex)
        logger.info(log_data)

    logger.info(log_data)


def _auto_complete_onboarding_duo_deal_entry(user_id: str):
    is_onboarding = True
    duo_deal_entry = DuoDealEntry.find_one(
        {
            "startedUser.id": user_id,
            "closed": False,
            "isOnboardingDuoDeal": is_onboarding,
        },
        projection=["_id"]
    )
    if not duo_deal_entry:
        return

    if not allow_non_ticket_auto_completion_feature(user_id):
        print("auto completion is disabled")
        return

    complete_duo_deal_entry(
        duo_deal_entry_id=duo_deal_entry.get_id_assert(),
        simulated=True,
        is_onboarding=is_onboarding,
    )


def _handle_stripe_subscriptions(
    subscription: stripe.Subscription, event_id: str, event_type: EventTypes
):
    """
    Handles the subscriptions, e.g. status changes, invoice statuses etc.

    :param event_id: The stripe event id.
    """
    logger = get_structured_logger("tasks_orders")
    log_data: LogContentType = {
        "action": "handle_stripe_subscriptions",
        "event_id": event_id,
        "event": event_type,
    }

    customer = subscription.customer
    if isinstance(customer, stripe.Customer):
        customer = customer.id
    if not customer:
        log_data["error"] = "no customer in stripe subscription"
        logger.info(log_data)
        return

    user = User.find_one({"stripe.id": customer})
    if not user:
        log_data["error"] = "no user for this subscription"
        logger.info(log_data)
        return

    user_id = user.get_id_assert()

    _send_subscription_event_to_amplitude(
        event_type=event_type,
        subscription=subscription,
        user_id=user_id,
        customer_id=customer,
    )

    with Lock(f"stripe_subscription_webhook_{user_id}"):
        # retrieve the latest subscription for the customer
        # We have the logic to manipulate the subscriptions in a short period of time,
        # for example for active_member_paused users, we create new subscription and then cancel old one
        # Considering there might be some delay in webhook or network or other parts, it's safer to get
        # the latest subscription directly.
        # And it's possible that latest_subscription is None, it means all the subscritpions are canceled.
        # Then we just use the subscription from the event
        latest_subscription, other_subscriptions = get_subscriptions_from_customer(customer)
        if latest_subscription:
            subscription = latest_subscription

        membership = None

        add_custom_attribute("event_type", event_type)
        add_custom_attribute("user_id", user_id)

        log_data["user_id"] = user_id

        membership = Membership.by_user_id(user.get_id_assert())
        prev_membership_status = maybe(membership).status or MembershipStatus.NON_MEMBER
        print(f"{event_id}: Processing subscription id {subscription.id}, {event_type=}, {user_id=}")

        try:

            membership = set_subscription_to_user(user, subscription, log_data)
            if other_subscriptions:
                for sub in other_subscriptions:
                    # if the subscription is not incomplete, it means it's in the middle of payment
                    if sub.status != SubscriptionStatus.INCOMPLETE.value:
                        cancel_subscription_immediately(sub.id, "canceled_because_of_duplicate_subscription")
                        void_open_invoices(sub.id, "subscription_canceled_because_of_duplicate_subscription")

            if not membership:
                # if it's None, it means we don't react on this status.
                # for example, if the payment fails, the subscription will still be created, with status "incomplete",
                # we don't react to incomplete subscription
                log_data["error"] = "no membership"
                logger.info(log_data)
                return

            membership_status = membership.status
            log_data["status"] = membership_status

            if prev_membership_status == membership_status:
                # membership status not changed, just return
                # note: even the status is not changed, but the new content of subscription is already updated
                log_data["error"] = "no membership status change"
                logger.info(log_data)
                return

            if (
                prev_membership_status in [MembershipStatus.NON_MEMBER, MembershipStatus.ACTIVE_MEMBER_PAUSED] and
                membership_status == MembershipStatus.TRIAL_MEMBER
            ):
                schedule_social_reward_worker(
                    "trigger_trial_member_reward",
                    {
                        "invitee_id": user_id,
                    },
                )
                _auto_complete_onboarding_duo_deal_entry(user_id)
                try:
                    add_ticket(
                        user=user,
                        reason="to trial member",
                        amount=get_to_trial_member_ticket_reward(),
                    )
                except BlidzException:
                    pass

            if (
                prev_membership_status not in [
                    MembershipStatus.ACTIVE_MEMBER,
                    MembershipStatus.ACTIVE_MEMBER_NO_RENEWAL
                ] and
                membership_status in [
                    MembershipStatus.ACTIVE_MEMBER,
                    MembershipStatus.ACTIVE_MEMBER_NO_RENEWAL
                ]
            ):
                try:
                    add_ticket(
                        user=user,
                        reason="to active member",
                        amount=get_to_active_member_ticket_reward(),
                    )
                except BlidzException:
                    pass

            if membership_status in [MembershipStatus.ACTIVE_MEMBER, MembershipStatus.TRIAL_MEMBER]:
                # If the user has already been a member before, and they have cash stashed away from before,
                # we want to add it back when they subscribe again.
                if (
                    membership.trial_used and
                    (membership.trial_count or 0) > 1 and
                    (membership.member_cash_stashed or 0) > 0
                ):
                    add_stashed_cash_to_member(user)

            # Sent when a customer's subscription ends.
            if membership_status == MembershipStatus.NON_MEMBER:
                cancel_reason = ""
                if (d := subscription.cancellation_details) and (c := d.comment):
                    cancel_reason = c

                # Subscription may be deleted in these situations:
                # 1. Failed to pay successfully in during the creation of subscription
                # 2. It's canceled in stripe dashboard
                # 3. A new subscription is created and old one is canceled.
                #    We want to check user data in case we handle the wrong subscription
                # 4. The subscription is canceled at the end of the current period.

                # Whether we should stash the user's cash or not.
                stash_the_cash = True
                if (
                    cancel_reason in [
                        "auth_offer_active_member_paused",
                        "set_subscription_to_user_cancel_original_subscription"
                    ] or
                    cancel_reason.startswith("new_subscription_on_")
                ):
                    # In the case of an active_member_paused takes the auth offer, we should NOT stash the cash,
                    # since we create a new subscription for the user immediately.
                    stash_the_cash = False

                if stash_the_cash:
                    # Remove the free cash from the user when the subscription is actually canceled.
                    stash_free_cash_from_member(user)

                # void all invoices so we don't charge them again
                void_open_invoices(subscription.id, "subscription_canceled")

                _refund_hold(user_id)

            membership.trigger_status_change_event(prev_membership_status, membership_status)
            membership.notify_client()

        except Exception as ex:
            print(f"{ex=}")
            log_data["error"] = str(ex)
            # Record exception to New Relic
            notice_error()

    log_data["membership_status"] = membership.status if membership else None
    logger.info(log_data)


def _send_subscription_event_to_amplitude(
    event_type: EventTypes,
    subscription: stripe.Subscription,
    user_id: str,
    customer_id: str,
):
    if event_type == EventTypes.CUSTOMER_SUBSCRIPTION_DELETED:
        current_item_price = get_current_subscription_item(subscription).price
        assert current_item_price is not None
        amplitude_event_data: Dict[str, Any] = {
            "comment": maybe(subscription.cancellation_details).comment,
            "reason": maybe(subscription.cancellation_details).reason,
            "subscription_id": subscription.id,
            "current_membership_tier": current_item_price.lookup_key,
        }
        historical_membership_revenue = list(StripeEvent.aggregate(
            [
                {
                    "$match": {
                        "type": EventTypes.INVOICE_PAID,
                        "data.object.customer": customer_id,
                        "data.object.subscription": {"$exists": True},
                    }
                },
                {
                    "$group": {
                        "_id": "historical_membership_revenue",
                        "total": {"$sum": "$data.object.amount_paid"},
                    }
                },
            ],
        ))[0]
        amplitude_event_data["historical_membership_revenue"] = historical_membership_revenue.get("total")
        AnalyticsManager.send_event(
            user_id=user_id,
            analytics_event=EVENT_SUBSCRIPTION_CANCELED,
            event_properties=amplitude_event_data,
        )


def _find_mixed_cart_order_from_subscription(subscription: stripe.Subscription):
    if not subscription.metadata:
        return None

    user_id = subscription.metadata.get(USER_ID_IN_METADATA)
    if not user_id:
        return None

    return _find_mixed_cart_order_from_user_id(user_id)


def _find_mixed_cart_order_from_user_id(user_id: str) -> Optional[Order]:
    membership = Membership.find_one({"user_id": user_id}, sort=[("_id", DESCENDING)])
    if not membership:
        return None

    if not membership.mixed_cart_order or not membership.mixed_cart_order.order_id:
        return None

    order = Order()
    try:
        order.load_by_id(membership.mixed_cart_order.order_id)
    except Exception:
        return None

    return order


def _auto_complete_duo_deal_entry_for_subscription(user: User, subscription: stripe.Subscription):
    if not allow_non_ticket_auto_completion_feature(user):
        print("auto completion is disabled")
        return

    order = _find_mixed_cart_order_from_subscription(subscription)
    if not order:
        print("No mixed cart order")
        return

    if order.status != Order.STATUS_NEW:
        print("order is not in 'new' status")
        return

    duo_deal_entry_data = (
        DuoDealEntry()
        .get_collection()
        .find_one({"startedUser.orderId": order.get_id()})
    )
    if not duo_deal_entry_data:
        print("duo deal entry is not found")
        return

    duo_deal_entry = DuoDealEntry(duo_deal_entry_data)
    print("duo deal entry id", duo_deal_entry.get_id())
    if duo_deal_entry.closed:
        print("duo deal entry is closed:")
        return

    duo_deal_entry_id = duo_deal_entry.get_id()
    assert duo_deal_entry_id is not None
    print("begin to close duo deal entry", duo_deal_entry_id)
    complete_duo_deal_entry(duo_deal_entry_id=duo_deal_entry_id, simulated=True)


def auto_complete_duo_deal_entry_using_ticket(user: User, order: Order):
    user_id = user.get_id_assert()
    order_id = order.get_id_assert()

    print(f"Trying to auto complete order {order_id} for user {user_id}")

    if order.status != Order.STATUS_NEW:
        print("order is not in 'new' status")
        return

    order_item = order.get_item_by_type(Order.TYPE_DEAL)
    if not order_item:
        print("order is not a deal")
        return

    duo_deal_scenario_str: Optional[DuoDealScenario] = order_item.get("duoDealScenario")
    duo_deal_scenario = DuoDealScenario.INVALID
    if duo_deal_scenario_str:
        duo_deal_scenario = DuoDealScenario(duo_deal_scenario_str)

    if not duo_deal_scenario or not duo_deal_scenario.match(DuoDealScenarioMask.TEAM_BUY):
        print("order is not a team buy")
        return

    if not order_item.get("allowAutoCompleteByTicket"):
        print("auto completion with ticket is disabled")
        return

    duo_deal_entry = DuoDealEntry.find_one({"startedUser.orderId": order_id})
    if not duo_deal_entry:
        print("duo deal entry is not found")
        return

    print("duo deal entry id", duo_deal_entry.get_id())
    if duo_deal_entry.closed:
        print("duo deal entry is closed:")
        return

    membership = Membership.by_user_id(user_id)
    try:
        spend_ticket(user, reason="auto_complete_for_subscription", membership=membership)
    except Exception as e:
        print(f"Cannot spend ticket: {e}")
        return

    try:
        duo_deal_entry_id = duo_deal_entry.get_id()
        assert duo_deal_entry_id is not None
        print("begin to close duo deal entry", duo_deal_entry_id)
        complete_duo_deal_entry(
            duo_deal_entry_id=duo_deal_entry_id,
            simulated=True,
            is_used_auto_completion_ticket=True)
    except Exception:
        notice_error()
        tier = None
        if membership:
            tier = membership.tier
        try:
            add_ticket(user, reason="auto_complete_for_subscription_failed", current_tier=tier, membership=membership)
        except Exception:
            pass


def check_and_pause_unchargeable_subscription(user: User, latest_invoice: Optional[str] = None) -> bool:
    customer_id = user.stripe.get("id")
    subscription_id = user.stripe.get("subscription_id")
    if not customer_id or not subscription_id:
        return False

    invoices = retrieve_invoices(
        customer_id=customer_id,
        subscription_id=subscription_id,
        starting_after=latest_invoice,
        limit=2,
    )
    if not invoices or len(invoices) < 2:
        return False

    for inv in invoices:
        if inv.status != InvoiceStatus.UNCOLLECTIBLE.value:
            return False

    print(f"Pausing subscription {subscription_id}")
    pause_subscription_payment_collection(subscription_id)

    return True


def _send_invoice_event_to_amplitude(
    invoice_event: AnalyticsEvent,
    invoice: stripe.Invoice,
    membership: Optional[Membership],
    user: User,
    attempt_source: str
):
    user_id = user.get_id_assert()

    price = get_price_from_subscription(invoice.subscription)
    # [DEVELOP-2379]
    invoice_line_count = None
    if invoice.lines:
        invoice_line_count = len(invoice.lines.data)
    interval = None
    if price and price.recurring and price.recurring.interval:
        interval = price.recurring.interval
    trial_count = 0
    if not membership:
        membership = user.get_or_create_membership()
        trial_count = membership.trial_count or 0
    AnalyticsManager.send_event(
        user_id=user_id or "",
        analytics_event=invoice_event,
        event_properties={
            "id": invoice.id,
            "billing_reason": invoice.billing_reason,
            "currency": invoice.currency,
            "hosted_invoice_url": invoice.hosted_invoice_url,
            "invoice_pdf": invoice.invoice_pdf,
            "attempt_count": invoice.attempt_count,
            "amount_paid": invoice.amount_paid,
            "amount_due": invoice.amount_due,
            "paid": invoice.paid,
            "period_start": iso_8601_from_time(invoice.period_start),
            "period_end": iso_8601_from_time(invoice.period_end),
            "status": invoice.status,
            "total": invoice.total,
            "attempted": invoice.attempted,
            "lines_total_count": invoice_line_count,
            "count_of_trial_used": trial_count,
            "auto_advance": invoice.auto_advance,
            "paid_out_of_band": invoice.paid_out_of_band,
            "attempt_source": attempt_source,
            "interval": interval,
        },
    )


def _check_is_upgrade_membership(invoice: stripe.Invoice):
    if not (lines := invoice.lines):
        return False

    if not (data := lines.data):
        return False

    if len(data) < 2:
        return False

    if not data[0].proration or data[1].proration:
        return False

    return True


def _handle_stripe_invoice_event(
    invoice: stripe.Invoice, event_id: str, event_type: EventTypes
):
    """
    Handles the invoice statuses

    Notes, for invoice containing a subscription:
    1. For analytics reason, we need to send invoice event as a suppliment of subscription event to amplitude
    2. We hold user fund when authenticate special offer, and capture this hold if renewal fails

    :param event_id: The stripe event id.
    """
    logger = get_structured_logger("tasks_orders")

    # Used for amplitude analytics
    invoice_event = None
    membership = None
    attempt_source = "stripe_automatic"

    if (metadata := invoice.metadata) and (occasion := metadata.get(INVOICE_PAID_OCCASION)):
        attempt_source = occasion

    user = User.find_one({"stripe.id": invoice.customer})
    if not user:
        raise Exception(f"User not found for stripe customer {invoice.customer}")

    user_id = user.get_id_assert()

    # if not user.active and event_type == EventTypes.INVOICE_CREATED:
    #     send_slack_message(
    #         f"Hey {Slack.mention('backend')}\n"
    #         f"There is a banned user: {user_id}\n"
    #         f"Got a stripe invoice create event: {event_id}\n"
    #         f"For more information, "
    #         f"please check <https://merchant-blidz.com/customer/customer_user/detail/{user_id}|here>",
    #         channel="#engineering",
    #         icon_emoji=":warning:",
    #         username="Unexpected invoice created",
    #         escape=False,
    #     )

    add_custom_attribute("user_id", user_id)
    add_custom_attribute("event_type", event_type)

    log_data: LogContentType = {
        "action": "handle_stripe_invoice_event",
        "event_id": event_id,
        "user_id": user_id,
        "status": invoice.status,
        "event": event_type.value,
    }

    one_time_txn_id, subscription_txn_id, _ = get_transaction_ids_and_user_id(
        invoice=invoice
    )
    one_time_txn_stripe = None
    subscription_txn_stripe = None

    try:
        # update transactions
        if one_time_txn_id:
            one_time_txn_stripe = TransactionStripe()
            one_time_txn_stripe.load_by_transaction_id(one_time_txn_id)
        if subscription_txn_id:
            subscription_txn_stripe = TransactionStripe()
            subscription_txn_stripe.load_by_transaction_id(subscription_txn_id)
        _update_transactions(
            [one_time_txn_stripe, subscription_txn_stripe], None, invoice
        )

        # handle the analytics events

        if event_type == EventTypes.INVOICE_CREATED:
            invoice_event = EVENT_STRIPE_INVOICE_CREATED
            print(f"Invoice '{invoice.id}' created for user {user_id}...")

            default_dm = user.stripe.get("defaultPaymentMethod")
            if default_dm:
                brand = get_payment_brand(user, default_dm)
                amount = invoice.amount_due or 0
                if invoice.billing_reason == "subscription_create":
                    descriptor = determine_payment_intent_descriptor(amount, brand, initiated_by_user=True)
                else:
                    descriptor = determine_payment_intent_descriptor(amount, brand, initiated_by_user=False)

                if descriptor == "BLIDZV":
                    if invoice.subscription:
                        # if the invoice is for a subscription
                        # and current descriptor is BLIDZV
                        # we need to check if we should change the descriptor to BLIDZV_NEW
                        if isinstance(invoice.subscription, stripe.Subscription):
                            subscription_id = invoice.subscription.id
                        else:
                            subscription_id = invoice.subscription
                        descriptor = determine_payment_intent_descriptor_for_BLIDZV(subscription_id)

                if descriptor != invoice.statement_descriptor:
                    assert invoice.id is not None
                    stripe.Invoice.modify(id=invoice.id, statement_descriptor=descriptor)

        elif event_type == EventTypes.INVOICE_FINALIZED:
            print(f"Invoice '{invoice.id}' finalized for user {user_id}...")
            if (
                invoice.billing_reason == "subscription_cycle" and
                invoice.status in [InvoiceStatus.OPEN.value, InvoiceStatus.PAID.value] and
                invoice.attempted and
                invoice.attempt_count
            ):
                mark_open_invoices_uncollectible(user, starting_after=invoice.id)
                # Check if the subscription should be canceled.
                should_cancel_subscription = check_should_cancel_subscription(user)
                if should_cancel_subscription:
                    log_data["message"] = (
                        f"Canceling subscription, reason: charge failed consecutive months. {invoice.subscription=}"
                    )
                    # Cancel the subscription immediately.
                    cancel_subscription(user, reason="fail_charge_invoice_2_month")

            invoice_event = EVENT_STRIPE_INVOICE_FINALIZED

        elif event_type == EventTypes.INVOICE_DELETED:
            print(f"Invoice '{invoice.id}' deleted for user {user_id}...")
            invoice_event = EVENT_STRIPE_INVOICE_DELETED

        elif event_type == EventTypes.INVOICE_PAID:
            print(f"Invoice '{invoice.id}' paid for user {user_id}...")
            assert invoice.id is not None
            invoice_event = EVENT_STRIPE_INVOICE_PAID
            if invoice.paid_out_of_band:
                _handle_stripe_success_invoice(
                    invoice=invoice,
                    user=user,
                    subscription_txn_id=subscription_txn_id
                )
            if invoice.billing_reason in ["subscription_cycle"]:
                send_stripe_membership_invoice_pubsub_event.delay(event_id)
                send_membership_cycle_invoice_paid_reward.delay(user_id, invoice.id)

            if (
                invoice.billing_reason in ["subscription_cycle", "subscription_update", "subscription_create"]
                and ((invoice.amount_paid or 0) > 0 or invoice.paid_out_of_band)
            ):
                # Trigger the referral reward if the user becomes a paying member.
                schedule_social_reward_worker(
                    "trigger_active_member_reward",
                    {
                        "invitee_id": user_id,
                    },
                )

            if _check_is_upgrade_membership(invoice):
                invoice = stripe.Invoice.modify(invoice.id, metadata={INVOICE_PAID_OCCASION: "upgrade_membership"})

                attempt_source = "upgrade_membership"

        # The invoice couldn't be finalized.
        # 1. Inspect the Invoice's last_finalization_error to determine the cause of the error.
        # 2. If you're using Stripe Tax, check the Invoice object's automatic_tax field.
        # 3. If automatic_tax[status]=requires_location_inputs, the invoice can't be finalized and payments can't
        #    be collected. Notify your customer and collect the required customer location.
        # 4. If automatic_tax[status]=failed, retry the request later.
        elif event_type == EventTypes.INVOICE_FINALIZATION_FAILED:
            print(f"Invoice '{invoice.id}' finalization failed for user {user_id}...")
            invoice_event = EVENT_STRIPE_INVOICE_FINALIZATION_FAILED

        elif event_type == EventTypes.INVOICE_MARKED_UNCOLLECTIBLE:
            print(f"Invoice '{invoice.id} marked as uncollectible for user {user_id}...")
            invoice_event = EVENT_STRIPE_INVOICE_MARKED_UNCOLLECTIBLE

        # This event happens after the invoice has been paid successfully
        # The invoice.paid is not a guarantee that the invoice has been paid (it may still fail)
        elif event_type == EventTypes.INVOICE_PAYMENT_SUCCEEDED:
            print(f"Invoice '{invoice.id}' payment succeeded for user {user_id}...")
            invoice_event = EVENT_STRIPE_INVOICE_PAYMENT_SUCCEEDED
            _handle_stripe_success_invoice(
                invoice=invoice,
                user=user,
                subscription_txn_id=subscription_txn_id
            )
        elif event_type == EventTypes.INVOICE_PAYMENT_FAILED:
            # Invoice payment failed, let's capture the hold, if it's set.
            print(f"Invoice '{invoice.id}' payment failed for user {user_id}...")
            invoice_event = EVENT_STRIPE_INVOICE_PAYMENT_FAILED

        # Record the invoice event to Amplitude.
        if invoice_event:
            _send_invoice_event_to_amplitude(invoice_event, invoice, membership, user, attempt_source)

    except Exception as ex:
        print(f"{ex=}")
        log_data["error"] = str(ex)
        # Record exception to New Relic
        add_custom_attribute("invoice_event", invoice_event)
        notice_error()

    logger.info(log_data)


def _handle_stripe_success_invoice(invoice: stripe.Invoice, user: User, subscription_txn_id: Optional[str]):
    from worker.tasks_clicks import send_appsflyer_event
    subscription = None
    membership = None
    if invoice.subscription:
        if isinstance(invoice.subscription, stripe.Subscription):
            subscription_id = invoice.subscription.id
        else:
            subscription_id = invoice.subscription
        subscription = stripe.Subscription.retrieve(subscription_id)
        membership = user.get_or_create_membership()

    lookup_key = None
    if subscription:
        lookup_key = subscription["items"].data[0].price.lookup_key

    payment_intent_of_invoice = None

    # This is when the subscription is created, and the payment succeeds.
    if subscription and invoice.billing_reason == "subscription_create":
        # Get the initial transaction ID, when the subscription is created,
        # and mark it as paid if it's not already paid.
        transaction = None
        if subscription_txn_id:
            transaction = Transaction()
            try:
                transaction.load_by_id(subscription_txn_id)
            except ModelNotFoundError:
                print(
                    f"Could not find transaction by the id {subscription_txn_id}!"
                )

        if transaction and transaction.status != Transaction.STATUS_PAID:
            with transaction.lock_and_reload():
                transaction.update_status(Transaction.STATUS_PAID)
                transaction.purchasedItems = [
                    {
                        "id": MEMBERSHIP_ITEM_ID,
                        "invoiceId": invoice.id,
                        "subscriptionId": subscription.id,
                        "price": invoice.amount_paid,
                        "currency": invoice.currency.upper()
                        if invoice.currency
                        else None,
                        "name": "Membership Subscription",
                    }
                ]
                transaction.save(lock=False)

        if subscription.metadata.get(SUBSCRIPTION_OCCASION) == PredefinedSubscriptionOccasion.AFTER_PAYOUT.value:
            user_id = user.get_id_assert()
            give_authentication_rewards.delay(user_id, lookup_key, "after_payout_subscription")
            _refund_hold(user_id)

    # This is when the subscription is renewed, or updated.
    elif subscription and invoice.billing_reason in [
        "subscription_cycle",
        "subscription_update",
    ]:
        print(f"Renewing subscription {subscription.id} for user {user.get_id()}...")

        # Create a new transaction for the paid subscription renewal.
        transaction_data = {
            "userId": user.get_id(),
            "status": Transaction.STATUS_PAID,
            "type": Transaction.TYPE_SUBSCRIPTION,
            "paymentMethod": Transaction.PAYMENT_METHOD_STRIPE,
            "currency": invoice.currency.upper() if invoice.currency else None,
            "purchasedItems": [
                {
                    "id": MEMBERSHIP_ITEM_ID,
                    "invoiceId": invoice.id,
                    "subscriptionId": subscription.id,
                    "price": invoice.amount_paid,
                    "currency": invoice.currency.upper() if invoice.currency else None,
                    "name": "Membership Subscription",
                }
            ],
        }

        transaction = Transaction(transaction_data)
        transaction.save()

        transaction_stripe = TransactionStripe(
            {"transactionId": transaction.get_id(), "invoice": invoice}
        )
        transaction_stripe.save()

        # update payment intent metadata, so that we can track the payment intent by its metadata
        payment_intent_of_invoice = retrieve_stripe_payment_intent(invoice=invoice)
        if payment_intent_of_invoice:
            new_metadata_items: Dict[str, str] = {
                TRANSACTION_ID_IN_METADATA: transaction.get_id_assert(),
                USER_ID_IN_METADATA: user.get_id_assert(),
                PAYMENT_ENTRY: "stripe subscription cycle",
                SUBSCRIPTION_ID_IN_METADATA: subscription.id,
            }
            mt: Optional[Dict[str, str]] = payment_intent_of_invoice.metadata
            if mt:
                mt.update(new_metadata_items)
            else:
                mt = new_metadata_items
            modify_payment_intent_metadata(payment_intent_of_invoice.id, mt)

        if membership:
            # [DEVELOP-3303]: If this is an active_member_paused paying an invoice,
            # and the invoice paid_at time is > 24h since the invoice was finalized.
            if (
                invoice.billing_reason == "subscription_cycle" or
                (
                    (inv_meta := invoice.metadata) and
                    inv_meta.get(INVOICE_UPDATED_OCCASION) == "reset_billing_cycle_for_invoice_with_multiple_attempt"
                )
            ):
                # 24h on production, 10min on staging.
                time_duration = choose_value_for_env(24 * 3600, 10 * 60)
                if is_invoice_paid_in_time_duration(invoice, time_duration):
                    # Handle active member paused users and update their billing cycle manually
                    _update_subscription_billing_cycle_for_active_member_paused(subscription, membership)

            membership.renew()
            # Update the order counts for the subscription.
            membership.update_order_counts(subscription=1)
            # Record the order count in the user stats.
            UserStats.record(user.get_id_assert(), UserStats.SUBSCRIPTION_COUNT)
            if invoice.billing_reason == "subscription_cycle":
                # Only send the Subscribe event to Facebook on the cycle reason.
                try:
                    fb_analytics = FacebookAnalytics.init()
                    fb_event_subscribe = fb_analytics.construct_event(user, FacebookEventType.SUBSCRIBE, invoice)
                    fb_event_add_to_wishlist = fb_analytics.construct_event(
                        user,
                        FacebookEventType.ADD_TO_WISHLIST,
                        invoice,
                    )
                    fb_analytics.send_event([fb_event_subscribe, fb_event_add_to_wishlist])
                except Exception as ex:
                    print(
                        f"Could not send Facebook event for user "
                        f"{user.get_id()}, Subscription: {subscription.id}, reason: {ex=}"
                    )

                try:
                    event = get_subscribe_event(user.get_id_assert(), invoice)
                    data = cast(Dict[str, Any], event)
                    send_appsflyer_event.delay(data)
                except Exception as ex:
                    print(
                        f"Could not schedule task appsflyer subscribe event of user "
                        f"{user.get_id()}, Subscription: {subscription.id}, reason: {ex=}"
                    )

                # let next spin fill spin_progress
                SpinProgress.set_next_spin_fill_progress(
                    user.get_id_assert(),
                    f"invoice paid with billing reason: {invoice.billing_reason}"
                )

        # When the invoice is paid, set the paid with real money if it's not yet set.
        if not user.has_paid_real_money:
            with user.lock_and_reload():
                user.has_paid_real_money = True
                user.save(lock=False)

        _auto_complete_duo_deal_entry_for_subscription(user, subscription)

        is_auth = False
        invoice_paid_occasion = invoice.billing_reason
        if (mt := invoice.metadata) and (occasion := mt.get(INVOICE_PAID_OCCASION)):
            invoice_paid_occasion = occasion
            try:
                AuthenticationOccasion(occasion)
                is_auth = True
            except ValueError:
                pass

        user_id = user.get_id_assert()
        if is_auth:
            give_authentication_rewards.delay(user_id, lookup_key, "authenticate")

        if is_auth or invoice.billing_reason == "subscription_cycle":
            _refund_hold(user_id)

        send_clevertap_charged_event(user_id, None, invoice.amount_paid, invoice_paid_occasion)

    if membership:
        if not payment_intent_of_invoice:
            payment_intent_of_invoice = retrieve_stripe_payment_intent(invoice_id=invoice.id)
        membership.update_payment_status(invoice, payment_intent_of_invoice)

    if invoice.amount_paid:
        # Record the amount paid in the money spent attribute.
        UserStats.record(user.get_id_assert(), UserStats.MONEY_SPENT_WITHOUT_REFUND_TO_BANK, invoice.amount_paid)
        UserStats.record(user.get_id_assert(), UserStats.NON_REFUNDED_MEMBERSHIP_REVENUE, invoice.amount_paid)


def _handle_stripe_charges_events(
    charge: stripe.Charge,
    event_id: str,
    event_type: EventTypes
):
    """
    Handles charges.* events coming from Stripe.
    """
    logger = get_structured_logger("tasks_orders")
    log_data: LogContentType = {
        "action": "handle_stripe_charges_events",
        "charge_id": charge.id,
        "event_id": event_id,
    }

    print(
        f"Charges event: Handling Stripe charge event {event_id} of type {event_type}"
    )

    try:
        # Handle refunds
        if event_type == EventTypes.CHARGE_REFUNDED:
            result = on_charge_refunded(charge)
            if result:
                print(f"Charges event: {charge.id} refunded successfully.")
            else:
                print(
                    f"Charges event: Could not refund charge {charge.id} because transaction data wasn't found."
                )

    except Exception as ex:
        print(f"{ex=}")
        log_data["error"] = str(ex)

    logger.info(log_data)


def _handle_stripe_dispute_event(dispute: stripe.Dispute, event_id: str, event_type: EventTypes):
    if event_type == EventTypes.CHARGE_DISPUTE_CREATED:
        try:
            preprocess_stripe_dispute(dispute)
        except Exception as ex:
            print(f"Could not preprocess dispute {dispute.id}! {ex=}")
            # Record exception to New Relic
            notice_error()

        payment_intent_id: Optional[str] = None
        if isinstance(dispute.payment_intent, str):
            payment_intent_id = dispute.payment_intent
        elif dispute.payment_intent:
            payment_intent_id = dispute.payment_intent.id
        ban_user_and_refund_orders(
            human_reason="dispute created",
            payment_intent_id=payment_intent_id,
            mastercard_only=True,
            stripe_event_id=event_id,
            stripe_event_type=event_type,
        )
    elif event_type == EventTypes.CHARGE_DISPUTE_CLOSED:
        try:
            process_dispute_closed(dispute)
        except Exception as ex:
            print(f"Could not process dispute closed event {dispute.id}: {ex=}")
            notice_error()
    elif event_type == EventTypes.CHARGE_DISPUTE_UPDATED:
        try:
            process_dispute_updated(dispute)
        except Exception as ex:
            print(f"Could not process dispute updated event {dispute.id}: {ex=}")
            notice_error()


def _handle_stripe_mandate_event(
    mandate: stripe.Mandate,
    previous_attributes: Optional[Dict[str, Any]],
    event_id: str,
    event_type: EventTypes
):
    """
    Handles mandate.* events coming from Stripe.

    If the mandate status is 'inactive', detach the payment method from the user.

    :param mandate: The mandate object from Stripe.
    :param previous_attributes: The previous attributes of the mandate.
    :param event_id: The stripe event id.
    :param event_type: The event type.
    """
    logger = get_structured_logger("tasks_orders")
    log_data: LogContentType = {
        "action": "handle_stripe_mandate_event",
        "event_id": event_id,
        "event_type": event_type.value,
        "mandate_id": mandate.id
    }

    print(f"Mandate event: Handling Stripe mandate event {event_id} of type {event_type}")

    try:
        # Check if the mandate status is 'inactive'
        if mandate.status == "inactive":
            # Get the payment method ID
            payment_method_id = None
            if isinstance(mandate.payment_method, str):
                payment_method_id = mandate.payment_method
            elif hasattr(mandate.payment_method, 'id'):
                payment_method_id = mandate.payment_method.id

            if not payment_method_id:
                log_data["error"] = "No payment method ID in mandate"
                logger.info(log_data)
                return

            # Retrieve the payment method to get the customer ID
            try:
                from commonlib.stripe.helpers import retrieve_payment_method
                payment_method = retrieve_payment_method(payment_method_id)
                if not payment_method:
                    log_data["error"] = "Could not retrieve payment method"
                    logger.info(log_data)
                    return

                # Get the customer ID from the payment method
                customer_id = None
                if payment_method.customer:
                    if isinstance(payment_method.customer, str):
                        customer_id = payment_method.customer
                    elif hasattr(payment_method.customer, 'id'):
                        customer_id = payment_method.customer.id

                if not customer_id:
                    log_data["error"] = "No customer ID in payment method"
                    logger.info(log_data)
                    return
            except Exception as ex:
                log_data["error"] = f"Error retrieving payment method: {str(ex)}"
                print(f"Mandate event: Error retrieving payment method {payment_method_id}: {ex}")
                add_custom_attribute("payment_method_id", str(payment_method_id))
                notice_error()
                logger.info(log_data)
                return

            user = User.find_one({"stripe.id": customer_id})
            if not user:
                log_data["error"] = "User not found"
                logger.info(log_data)
                return

            user_id = user.get_id_assert()
            log_data["user_id"] = user_id
            log_data["payment_method_id"] = str(payment_method_id)

            # Detach the payment method
            from commonlib.stripe.helpers import delete_stripe_payment_method
            try:
                delete_stripe_payment_method(str(payment_method_id), user)
                log_data["result"] = "Payment method detached successfully"
                print(
                    f"Mandate event: Payment method {payment_method_id} detached successfully for user {user_id}")
            except Exception as ex:
                log_data["error"] = f"Failed to detach payment method: {str(ex)}"
                print(f"Mandate event: Failed to detach payment method {payment_method_id}: {ex}")
                add_custom_attribute("payment_method_id", str(payment_method_id))
                add_custom_attribute("user_id", user_id)
                notice_error()
    except Exception as ex:
        log_data["error"] = str(ex)
        print(f"Mandate event: Error handling mandate event: {ex}")
        add_custom_attribute("event_id", event_id)
        add_custom_attribute("event_type", event_type.value)
        notice_error()

    logger.info(log_data)


def _handle_stripe_payment_method_event(
    payment_method: stripe.PaymentMethod,
    previous_attributes: Optional[Dict[str, Any]],
    event_id: str,
    event_type: EventTypes
):
    logger = get_structured_logger("tasks_orders")
    log_data: LogContentType = {
        "action": "handle_stripe_payment_method",
        "event_id": event_id,
        "event_type": event_type.value
    }
    analytics_event = None
    analytics_event_data: Dict[str, Any] = {}
    if event_type == EventTypes.PAYMENT_METHOD_ATTACHED:
        analytics_event = EVENT_STRIPE_PAYMENT_METHOD_ATTACHED
    elif event_type == EventTypes.PAYMENT_METHOD_DETACHED:
        analytics_event = EVENT_STRIPE_PAYMENT_METHOD_DETACHED

    print(f"Handling Stripe payment method event {event_id} of type {event_type}")

    customer = payment_method.customer
    customer_id = None
    if customer:
        if isinstance(customer, stripe.Customer):
            customer_id = customer.id
        else:
            customer_id = customer
    if not customer_id:
        if previous_attributes:
            customer_id = previous_attributes.get("customer")
    if not customer_id:
        print("There is no customer in this event")
        log_data["error"] = "No customer in the event"
        logger.info(log_data)
        return

    log_data["customer_id"] = customer_id
    user = User.find_one(
        {"stripe.id": customer_id}
    )
    if not user:
        print("User is not found")
        log_data["error"] = "User not found"
        logger.info(log_data)
        return

    log_data["user_id"] = user.get_id_assert()

    sync_stripe_payment_methods(user, log_data)
    publish_to_user_channel(
        user_id=user.get_id_assert(),
        content=UpdateStripe.from_stripe_data(user.stripe),
    )
    # TODO: remove this after the migration
    user.push_to_clients(stripe=True)

    # Send the analytics for the payment method event.
    if card := payment_method.card:
        analytics_event_data["country"] = card.country
        if card.wallet:
            analytics_event_data["wallet_type"] = card.wallet.type
        analytics_event_data.update(
            {
                "brand": card.brand,
                "exp_month": card.exp_month,
                "exp_year": card.exp_year,
                "last4": card.last4,
                "funding": card.funding,
                "fingerprint": card.fingerprint,
            }
        )
        if card.checks:
            checks = stripe_object_to_dict(card.checks)
            analytics_event_data.update(**checks)
    try:
        if analytics_event:
            AnalyticsManager.send_event(
                user_id=user.get_id() or "",
                analytics_event=analytics_event,
                event_properties=analytics_event_data,
            )
    except Exception as ex:
        log_data["analytics_error"] = f"Could not send analytics event {analytics_event=}: {ex=}"

    logger.info(log_data)


def handle_stripe_radar_event(
    fraud_warning: stripe.radar.EarlyFraudWarning,
    event_id: Optional[str],
    event_type: Optional[EventTypes] = None
):
    logger = get_structured_logger("stripe_early_fraud_warning")

    payment_intent_id = cast(Optional[str], fraud_warning.payment_intent)
    charge_id = cast(Optional[str], fraud_warning.charge)

    log_data: LogContentType = {
        "action": "handle_stripe_radar_event",
        "event_id": event_id,
        "event_type": event_type,
        "payment_intent_id": payment_intent_id,
        "charge_id": charge_id,
    }

    if not payment_intent_id:
        # based on the stripe_events collection, we should always have payment intent id
        log_data["result"] = "No payment intent id"
        logger.info(log_data)
        return

    payment_intent = retrieve_stripe_payment_intent(payment_intent_id)
    if not payment_intent:
        log_data["result"] = "payment intent not found"
        logger.info(log_data)
        return

    if payment_intent.status != PaymentIntentStatus.SUCCEEDED.value:
        log_data["result"] = "payment intent not succeeded"
        logger.info(log_data)
        return

    customer = payment_intent.customer
    if not customer:
        log_data["result"] = "No customer id"
        logger.info(log_data)
        return
    elif isinstance(customer, stripe.Customer):
        customer_id = customer.id
    else:
        customer_id = customer

    user = User.find_one({"stripe.id": customer_id})
    if not user:
        log_data["result"] = "User not found"
        logger.info(log_data)
        return

    payment_method_id = None
    if isinstance(payment_intent.payment_method, str):
        payment_method_id = payment_intent.payment_method
    elif payment_intent.payment_method:
        payment_method_id = payment_intent.payment_method.id

    brand = get_payment_brand(user, payment_method_id) if payment_method_id else None
    is_mastercard = brand == "mastercard"
    is_visa = brand == "visa"
    log_data["brand"] = brand
    log_data["is_mastercard"] = is_mastercard
    log_data["is_visa"] = is_visa

    human_reason = "early fraud warning"
    if is_visa:
        # refund all visa transactions in the last 7 days
        log_data["result"] = f"refunding all visa transactions in the last 7 days"
        legacy_payment_intents = list_stripe_payment_intents(customer_id, get_js_time() - 7 * JS_DAY)
        should_refund_payment_intent_ids = set()
        should_refund_payment_intent_ids.add(payment_intent_id) if payment_intent_id else None
        for pi in legacy_payment_intents:
            if pi.status != PaymentIntentStatus.SUCCEEDED.value:
                continue
            payment_method_id = None
            if isinstance(pi.payment_method, str):
                payment_method_id = pi.payment_method
            elif pi.payment_method:
                payment_method_id = pi.payment_method.id
            if not payment_method_id:
                continue
            brand = get_payment_brand(user, payment_method_id)
            if brand != "visa":
                continue
            should_refund_payment_intent_ids.add(pi.id)

        for pi_id in should_refund_payment_intent_ids:
            try:
                refund_stripe_payment_intent(pi_id, human_reason=human_reason)
                log_data["result"] += f"\nrefund {pi_id} succeeded"
            except Exception as e:
                if "has been charged back" in str(e):
                    log_data["result"] += f"\n{pi_id} has been charged back"
                elif "has already been refunded" in str(e):
                    log_data["result"] += f"\n{pi_id} has already been refunded"
                else:
                    log_data["result"] += f"\nrefund {pi_id} got {str(e)}"
        logger.info(log_data)
    else:
        if not is_mastercard:
            do_nothing_if_payout_in = 30 * JS_DAY

            payout = PayoutTransaction.find_one(
                {
                    "user_id": user.get_id_assert(),
                    "payout_time": {"$gt": get_js_time() - do_nothing_if_payout_in},
                    "transaction_status": {
                        "$in": TransactionStatus.non_failure_status()
                    },
                }
            )
            if payout:
                log_data["result"] = f"has payout in {do_nothing_if_payout_in // JS_DAY} days, do nothing"
                logger.info(log_data)
                return

        try:
            refund_stripe_payment_intent(payment_intent_id, human_reason=human_reason)
            log_data["result"] = f"refund {payment_intent_id} succeeded"
        except Exception as e:
            if "has been charged back" in str(e):
                log_data["result"] = f"{payment_intent_id} has been charged back"
            elif "has already been refunded" in str(e):
                log_data["result"] = f"{payment_intent_id} has already been refunded"
            else:
                log_data["result"] = f"refund {payment_intent_id} got {str(e)}"
        finally:
            logger.info(log_data)


def ban_user_and_refund_orders(
    *,
    human_reason: str,
    user_id: Optional[str] = None,
    payment_intent_id: Optional[str] = None,
    charge_id: Optional[str] = None,
    created_days_within: int = 120,  # default is 120 days because user can only dispute payment within last 120 days
    mastercard_only: bool = False,  # only refund mastercard payment
    stripe_event_id: Optional[str] = None,
    stripe_event_type: Optional[EventTypes] = None,
):
    logger = get_step_logger("ban_user_and_refund_orders", expire_days=None)
    shared_content = {}
    if stripe_event_id:
        shared_content["stripe_event_id"] = stripe_event_id
    if stripe_event_type:
        shared_content["stripe_event_type"] = stripe_event_type.value
    if human_reason:
        shared_content["reason"] = human_reason
    if user_id:
        shared_content["user_id"] = user_id
    if payment_intent_id:
        shared_content["trigger_payment_intent_id"] = payment_intent_id
    if charge_id:
        shared_content["trigger_charge_id"] = charge_id

    base_log: LogContentType = {}

    mongo_user = None
    if user_id:
        mongo_user = User()
        mongo_user.load_by_id(user_id)
    else:
        customer_id = None
        if charge_id:
            charge = retrieve_charge(charge_id)
            customer_id = charge.customer if charge else None
        elif payment_intent_id:
            if payment_intent := retrieve_stripe_payment_intent(payment_intent_id):
                customer_id = payment_intent.customer
        if customer_id:
            user_data = User().get_collection().find_one({"stripe.id": customer_id})
            mongo_user = User(user_data)

    if not mongo_user:
        base_log["error"] = "cannot find the user"
        logger.set_shared_content(shared_content)
        logger.add_step("base_info", base_log)
        logger.finish_log()
        return

    user_id = mongo_user.get_id_assert()
    customer_id = cast(Optional[str], mongo_user.stripe.get("id"))

    print(f"going to ban user {user_id} and refund all orders")

    shared_content["user_id"] = user_id
    if customer_id:
        shared_content["customer_id"] = customer_id
    logger.set_shared_content(shared_content)

    # refund all payment intents

    if customer_id:
        payment_intents_cursor = StripeEvent.find(
            {
                "data.object.customer": customer_id,
                "type": EventTypes.PAYMENT_INTENT_SUCCEEDED,
                "created": {"$gt": get_js_time() / 1000 - created_days_within * DAY},
            }
        )
        payment_intents = list(payment_intents_cursor)
        payment_intent_ids = [p.data["object"]["id"] for p in payment_intents]  # type: ignore
        refunded_or_disputed = StripeEvent.find_fields(
            {
                "data.object.payment_intent": {"$in": payment_intent_ids},
                "type": {
                    "$in": [
                        EventTypes.CHARGE_REFUNDED,
                        EventTypes.CHARGE_DISPUTE_CREATED
                    ]
                },
            },
            field_fn=lambda e: cast(str, e.data["object"]["payment_intent"])  # type: ignore
        )

        need_to_refund = [
            p for p in payment_intents if p.data["object"]["id"] not in refunded_or_disputed  # type: ignore
        ]

        if mastercard_only:
            need_to_refund = [
                p for p in need_to_refund
                if get_payment_brand(mongo_user, p.data["object"]["payment_method"]) == "mastercard"  # type: ignore
            ]

        for pi in need_to_refund:
            assert pi.data is not None
            pid = pi.data["object"]["id"]
            pi_log: LogContentType = {
                "payment_intent_id": pid,
            }

            metadata = pi.data["object"].get("metadata", {})
            if PAYMENT_ENTRY in metadata:
                pi_log["payment_entry"] = metadata[PAYMENT_ENTRY]
            if TRANSACTION_ID_IN_METADATA in metadata:
                pi_log["transaction_id"] = metadata[TRANSACTION_ID_IN_METADATA]

            try:
                refund_stripe_payment_intent(pid, human_reason=human_reason)
            except Exception as e:
                if "has been charged back" in str(e):
                    pi_log["error"] = str(e)
                elif "has already been refunded" in str(e):
                    continue
                else:
                    raise
            print(f"refund {pid}")
            logger.add_step("refund_payment_intent", pi_log)

    # cancel membership and ban user

    membership = Membership.by_user_id(user_id)
    if membership and membership.status != MembershipStatus.NON_MEMBER:
        adapter = create_subscription_adapter_by_membership(membership)
        if adapter:
            try:
                adapter.cancel_subscription_by_membership(membership)
                base_log["membership_canceled"] = True
                print(f"canceled subscription {membership.subscription_id}")
            except SubscriptionNotFound as e:
                pass

    with mongo_user.lock_and_reload():
        mongo_user.active = False
        print("user banned")
        base_log["banned"] = True
        mongo_user.save(lock=False)

    logger.add_step("base_info", base_log)
    logger.finish_log()


@app.task(ignore_result=True)
def handle_stripe_webhook_events(event_id: str):
    """
    Handle
    - transaction change
    - user change
    - subscription change
    - refund
    - payment method change

    # Webhook Events:

    (ot: one-time, su: suscription, txnId: transactionId, L: lines, D: data, P: price, Pd: product, M: metadata)

    | case                          | payment intent | invoice                                        |
    |-------------------------------|----------------|------------------------------------------------|
    | ot only                       | y (txnId in M) | n                                              |
    | su only                       |                |                                                |
    |  - w/ trial                   | n              | y (txnId in L.D.M)                             |
    |  - w/o trial                  | y (no txnId)   | y (txnId in L.D.M)                             |
    | mixed                         |                |                                                |
    |  - ot price 0, su w/ trial    | n              | y (ot txnId in L.D.P.Pd.M, su txnId in L.D.M)  |
    |  - ot price > 0, su w/ trial  | y (no txnId)   | y (ot txnId in L.D.P.Pd.M, su txnId in L.D.M)  |
    |  - ot price 0, su w/o trial   | y (no txnId)   | y (ot txnId in L.D.P.Pd.M, su txnId in L.D.M)  |
    |  - ot price > 0, su w/o trial | y (no txnId)   | y (ot txnId in L.D.P.Pd.M, su txnId in L.D.M)  |
    """
    event = StripeEvent.by_event_id(event_id)
    if (
        not event
        or not event.id
        or not event.type
        or not event.data
        or not event.data.get("object")
    ):
        return

    if event.type in [
        EventTypes.PAYMENT_INTENT_SUCCEEDED,
        EventTypes.PAYMENT_INTENT_CANCELED,
        EventTypes.PAYMENT_INTENT_PAYMENT_FAILED,
    ]:
        payment_intent = cast(stripe.PaymentIntent, stripe.convert_to_stripe_object(event.data["object"]))
        _handle_stripe_payment_intent(payment_intent, event.id, event.type)

    elif event.type in [
        EventTypes.CHARGE_REFUNDED,
    ]:
        charge = cast(stripe.Charge, stripe.convert_to_stripe_object(event.data["object"]))
        _handle_stripe_charges_events(charge, event.id, event.type)

    elif event.type in [
        EventTypes.CHARGE_DISPUTE_CREATED,
        EventTypes.CHARGE_DISPUTE_CLOSED,
        EventTypes.CHARGE_DISPUTE_UPDATED,
    ]:
        try:
            dispute = cast(stripe.Dispute, stripe.convert_to_stripe_object(event.data["object"]))
            _handle_stripe_dispute_event(dispute, event.id, event.type)
        except RateLimitError:
            sleep(1)
            handle_stripe_webhook_events(event_id)

    elif event.type in [
        EventTypes.CUSTOMER_SUBSCRIPTION_CREATED,
        EventTypes.CUSTOMER_SUBSCRIPTION_DELETED,
        EventTypes.CUSTOMER_SUBSCRIPTION_UPDATED,
    ]:
        subscription = cast(stripe.Subscription, stripe.convert_to_stripe_object(event.data["object"]))
        _handle_stripe_subscriptions(subscription, event.id, event.type)

    elif event.type in [
        EventTypes.INVOICE_CREATED,
        EventTypes.INVOICE_FINALIZED,
        EventTypes.INVOICE_FINALIZATION_FAILED,
        EventTypes.INVOICE_PAYMENT_SUCCEEDED,
        EventTypes.INVOICE_PAID,
        EventTypes.INVOICE_PAYMENT_ACTION_REQUIRED,
        EventTypes.INVOICE_PAYMENT_FAILED,
        EventTypes.INVOICE_UPDATED,
        EventTypes.INVOICE_MARKED_UNCOLLECTIBLE,
    ]:
        invoice = cast(stripe.Invoice, stripe.convert_to_stripe_object(event.data["object"]))
        _handle_stripe_invoice_event(invoice, event.id, event.type)

    elif event.type in [
        EventTypes.PAYMENT_METHOD_ATTACHED,
        EventTypes.PAYMENT_METHOD_AUTOMATICALLY_UPDATED,
        EventTypes.PAYMENT_METHOD_DETACHED,
        EventTypes.PAYMENT_METHOD_UPDATED,
    ]:
        payment_method = cast(stripe.PaymentMethod, stripe.convert_to_stripe_object(event.data["object"]))
        previous_attributes = event.data.get("previous_attributes")
        _handle_stripe_payment_method_event(payment_method, previous_attributes, event.id, event.type)

    elif event.type == EventTypes.MANDATE_UPDATED:
        mandate = cast(stripe.Mandate, stripe.convert_to_stripe_object(event.data["object"]))
        previous_attributes = event.data.get("previous_attributes")
        _handle_stripe_mandate_event(mandate, previous_attributes, event.id, event.type)

    elif event.type in [
        EventTypes.RADAR_EARLY_FRAUD_WARNING_CREATED
    ]:
        warning = cast(stripe.radar.EarlyFraudWarning, stripe.convert_to_stripe_object(event.data["object"]))
        try:
            handle_stripe_radar_event(warning, event.id, event.type)
        except RateLimitError:
            sleep(1)
            handle_stripe_webhook_events.delay(event_id)  # type: ignore
            return

    StripeEvent.update_many(
        {
            "id": event_id
        },
        {
            "$set": {
                "handle_status": HandleStatus.HANDLED,
                "handled_time": datetime.now(tz=timezone.utc),
            }
        }
    )


@app.task(ignore_result=True)
def notify_order_status_update(
    order_id: str, user_id: str, status: str, action: Optional[str] = None
):
    """
    Send order status notifications.

    :param order_id: The order to send the update for.
    :param user_id: The user who the order belongs to.
    :param status: The order status.
    :param action: The order action, e.g. cancel_manual or cancel_auto.
    """
    order = Order()
    order.load_by_id(order_id)

    user = User()
    user.load_by_id(user_id)

    for item in order.orderItems:
        if item["type"] != Order.TYPE_DEAL:
            continue
        order.notify_update_by_notification(status, user, item)
        order.notify_update_by_email(status, user, item, action)


@app.task(ignore_result=True)
def set_payment_transaction_id_to_subscription(payment_txn_id: str, subscription_id: str):
    sub = stripe.Subscription.retrieve(
        subscription_id,
    )
    metadata = sub.metadata
    if not metadata:
        metadata = {}
    metadata[PAYMENT_TRANSACTION_ID_IN_METADATA] = payment_txn_id

    stripe.Subscription.modify(subscription_id, metadata=metadata)


def _give_checkout_gifts(
    user: User,
    order: Order,
    duo_deal_entry: DuoDealEntry,
    deal_id: str,
    transaction_id: str,
    gifts: List[str],
    cashback_id: Optional[str] = None,
):
    """
    Gives the checkout gifts to the user.

    :param user: The started user/completed user.
    :param order: The order of the started/completed user.
    :param duo_deal_entry: The duo deal entry.
    :param deal_id: The deal id for the duo deal.
    :param transaction_id: The transaction id.
    :param gifts: The gifts to give, either from duo_deal_entry.gifts, or COMPLETE_DUO_DEAL_GIFTS.
    :param cashback_id: The cashback id if specified.
    """
    user_id = user.get_id_assert()
    order_id = order.get_id_assert()
    is_member = user.is_member()

    # Give the points to the started user, but only if they're a member. Variants a and b.
    if DUO_DEAL_GIFT_POINTS in gifts and is_member:
        assert order.price is not None
        process_checkout_summary_point_rewards(
            user_id,
            order_id,
            duo_deal_entry.get_id_assert(),
            order.price
        )

    if DUO_DEAL_GIFT_CASHBACK in gifts and cashback_id and is_member:
        task = Task(
            "worker.tasks_users:give_cash_back.delay",
            kwargs={
                "order_id": order_id,
                "duo_deal_entry_id": duo_deal_entry.get_id(),
            },
        )
        task.add()

    if DUO_DEAL_GIFT_SPIN in gifts:
        # this gift is already given when duo deal entry is created
        # so here we just skip it
        pass


@app.task(ignore_result=True)
def send_delivery_update_notifications(email: str, data: Dict[str, Any]):
    """
    Sends delivery updates to users.

    :param email: The email to send the updates to.
    :param data: The data to be sent.
    """
    from worker.tasks_users import schedule_clevertap_event

    # Send email
    queue_email("delivery-update", data, email_address=email)

    # Send event to clevertap
    schedule_clevertap_event.delay(
        [email],
        Event.Names.DELIVERY_UPDATE,
        data=data,
    )


def _update_subscription_billing_cycle_for_active_member_paused(
    subscription: stripe.Subscription,
    membership: Membership
):
    try:
        print(f"Update billing cycle: Setting billing_cycle_anchor=now for subscription {subscription.id}")
        add_custom_attribute("subscription_id", subscription.id)
        add_custom_attribute("user_id", membership.user_id)
        # Update the subscription billing cycle to "now", set the collection_method so Stripe won't charge automatically
        updated_subscription = stripe.Subscription.modify(
            subscription.id,
            billing_cycle_anchor="now",
            proration_behavior="none",
            collection_method="send_invoice",
            days_until_due=1,
        )
        if updated_subscription:
            # Update the periods for the membership.
            membership.set_periods(updated_subscription)

            # Get the latest invoice created by the subscription update and mark it void immediately.
            invoice_id = get_subscription_latest_invoice_id(updated_subscription)
            assert invoice_id is not None
            add_custom_attribute("invoice_id", invoice_id)
            # Finalize the invoice so the status changes to open
            print(f"Update billing cycle: Finalizing invoice {invoice_id}")
            stripe.Invoice.finalize_invoice(invoice_id)
            print(f"Update billing cycle: Voiding invoice {invoice_id}")
            void_invoice(invoice_id)
            stripe.Invoice.modify(
                invoice_id,
                metadata={
                    INVOICE_UPDATED_OCCASION: "update_billing_cycle_for_active_member_paused",
                    INVOICE_VOIDED_OCCASION: "update_billing_cycle_for_active_member_paused",
                }
            )
        else:
            print(f"Update billing cycle: Could not update subscription {subscription.id}")
    except InvalidRequestError as ex:
        print(
            f"Update billing cycle: Could not update billing cycle or void invoice for subscription {subscription.id}: "
            f"{ex=}"
        )
        notice_error()

    # Reset the collection method to automatic charge
    stripe.Subscription.modify(subscription.id, collection_method="charge_automatically")


@app.task(ignore_result=True)
def send_stripe_membership_invoice_pubsub_event(event_id: str):
    def _print_error():
        print("Cannot send membership pubsub invoice event")

    event = StripeEvent.by_event_id(event_id)
    if not event or not event.data or not event.data.get("object"):
        _print_error()
        return

    invoice = cast(stripe.Invoice, stripe.convert_to_stripe_object(event.data["object"]))
    if not invoice:
        _print_error()
        return
    if isinstance(invoice.customer, stripe.Customer):
        customer = invoice.customer
    else:
        customer = retrieve_customer(stripe_customer_id=invoice.customer)

    if not customer:
        _print_error()
        return
    if customer.metadata and (user_id := customer.metadata.get("id")):
        try:
            user = User()
            user.load_by_id(user_id)

            order = _find_mixed_cart_order_from_user_id(user_id)
            send_membership_invoice_algo_event("membership_invoice_paid", user, invoice, order)
        except Exception:
            _print_error()


# Deprecated
# this task is moved to tasks_payment.py
# code here will be removed next week, after all existing tasks are consumed
@app.task(ignore_result=True)
def refund_payment_hold(payment_hold_id: str):
    """
    Refund a payment hold.

    This is done one hour after the payment hold is created, for ab-test variant "a".

    :param payment_hold_id: The payment hold to refund.
    """
    logger = get_structured_logger("refund_payment_hold")
    log_data: LogContentType = {
        "payment_hold_id": payment_hold_id,
        "success": False,
    }
    payment_hold: Optional[PaymentHold] = PaymentHold.find_one({"_id": payment_hold_id})
    if payment_hold and payment_hold.payment_intent:
        payment_intent = payment_hold.payment_intent
        log_data["payment_intent_id"] = payment_intent.id
        log_data["amount"] = payment_intent.amount
        try:
            stripe_refund = refund_stripe_payment_intent(
                payment_intent,
                human_reason="Refund payment hold",
                amount=payment_intent.amount,
                expand=["payment_intent"],
            )
            payment_hold.set_refunded_at(stripe_refund)
            log_data["refunded_at"] = get_js_time()
            log_data["success"] = True
        except InvalidRequestError as ex:
            log_data["error"] = f"Couldn't create refund: {ex.error.message if ex.error else 'unknown'} ({ex.code})"

    logger.info(log_data)


@app.task(ignore_result=True)
def restart_duo_deal(order_id: str, duo_deal_entry_id: str):
    from commonlib.deal.utils import get_duo_deal_restart_count, get_max_retry_count

    logger = get_structured_logger("tasks_orders")
    log_data: LogContentType = {
        "action": "restart_duo_deal_entry",
        "order_id": order_id,
    }

    def _log_error(error: str):
        log_data["error"] = error
        logger.info(log_data)
        print(error)

    now = get_js_time()
    try:
        order = Order()
        order.load_by_id(order_id)
        old_transaction = order.get_transaction()

        user_id = order.userId
        if not user_id:
            _log_error("Cannot find user's id")
            return

        try:
            user = User()
            user.load_by_id(user_id)
        except ModelNotFoundError:
            _log_error(f"Cannot load user {user_id}")
            return

        try:
            old_duo_deal_entry = DuoDealEntry()
            old_duo_deal_entry.load_by_id(duo_deal_entry_id)
        except ModelNotFoundError:
            _log_error(f"Cannot load duo deal entry {duo_deal_entry_id}")
            return

        restart_count = 0
        trace_id = order.traceId
        if trace_id:
            restart_count = get_duo_deal_restart_count(order)
        else:
            trace_id = create_request_id(user_id)
            order.traceId = trace_id
            order.save()
            old_transaction.traceId = trace_id
            old_transaction.save()
            # Add the trace id to the duo deal entry.
            old_duo_deal_entry.traceId = trace_id
            old_duo_deal_entry.save()

        user_max_retry = get_max_retry_count(user)

        if restart_count >= user_max_retry:
            log_data["cancel"] = f"User has retried max times: {restart_count}"
            logger.info(log_data)
            return

        old_transaction_data: Dict = deepcopy(old_transaction.get_data())
        del old_transaction_data["_id"]
        new_transaction: Transaction = Transaction(old_transaction_data)
        new_transaction.status = Transaction.STATUS_STARTED
        new_transaction.timestamp = now
        new_transaction.traceId = trace_id
        new_transaction.save(new=True)
        new_transaction_id = new_transaction.get_id()

        if not new_transaction_id:
            log_data["error"] = "Cannot get new transaction id"
            logger.info(log_data)
            return

        if not order.price:
            log_data["success"] = True
            logger.info(log_data)
            return

        # create stripe transaction
        metadata = {
            TRANSACTION_ID_IN_METADATA: new_transaction_id,
            USER_ID_IN_METADATA: user_id,
            PAYMENT_ENTRY: "restart_duo_deal_entry",
        }

        transaction_stripe_data = {"transactionId": new_transaction_id}
        transaction_stripe = TransactionStripe(transaction_stripe_data)
        transaction_stripe.save(new=True)

        assert order.price is not None
        payment_intent = create_payment_intent(
            user=user,
            amount=order.price,
            currency=order.currency,
            confirm=True,
            metadata=metadata,
            initiated_by_user=True,
            idempotency_key=new_transaction_id,
        )

        if payment_intent.status == PaymentIntentStatus.REQUIRES_PAYMENT_METHOD.value:
            cancel_payment_intent(payment_intent.id)
            _log_error("Payment intent status 'requires_payment_method'.")
            return

        transaction_stripe.paymentIntent = payment_intent
        transaction_stripe.save()

    except Exception as e:
        _log_error(str(e))

    log_data["success"] = True
    logger.info(log_data)


def sync_tracking_with_stripe(order: Order):
    from commonlib.settings_payment import PAYMENT_METHOD_STRIPE
    if not order:
        print("sync_tracking_with_stripe: order is None")
        return

    try:
        transaction = order.get_transaction()
    except ModelNotFoundError:
        print(f"sync_tracking_with_stripe: cannot find transaction by order_id {order.get_id()}")
        return

    if transaction.paymentMethod != PAYMENT_METHOD_STRIPE:
        return

    transaction_stripe = TransactionStripe()
    try:
        transaction_stripe.load_by_transaction_id(transaction.get_id_assert())
    except ModelNotFoundError:
        print(f"sync_tracking_with_stripe: cannot find transaction_stripe by transaction_id {transaction.get_id()}")
        return

    if not transaction_stripe.paymentIntent:
        return

    if not order.shippingAddress:
        return

    address = ShippingAddress(order.shippingAddress)

    shipping = stripe.PaymentIntent.ModifyParamsShipping(
        name=address.recipient,
        phone=address.phoneNumber,
        address=stripe.PaymentIntent.ModifyParamsShippingAddress(
            postal_code=address.zip,
            country=address.country,
            state=address.state,
            city=address.city,
            line1=address.street,
            line2=address.line2,
        )
    )

    for order_item in order.orderItems:
        tracking_number = order.get_order_item_tracking_number(order_item)
        if tracking_number:
            shipping["tracking_number"] = tracking_number
            break

    try:
        modify_payment_intent_shipping(transaction_stripe.paymentIntent.id, shipping)
    except Exception as e:
        print(f"sync_tracking_with_stripe: modify stripe payment intent failed {str(e)}")


def _refund_hold(user_id: str):
    hold = PaymentHold.find_one(
        {
            "user_id": user_id,
            "created_at": {"$gte": get_js_time() - 30 * JS_DAY},
            "released_at": None,
            "refunded_at": None
        },
        sort=[("created_at", DESCENDING)]
    )

    if hold:
        release_or_refund_payment_hold.delay(hold.get_id_assert(), "charge_and_refund_hold_for_prepaid_card")


@app.task(ignore_result=True)
def handle_chargeblast_event(event_id: str):
    from commonlib.models.chargeblast import ChargeblastEvent, EventType

    event = ChargeblastEvent.find_one({"_id": ObjectId(event_id)})
    if not event:
        return

    if event.event_type == EventType.ALERT_REFUNDED:
        assert event.request is not None
        customer_id = event.request.get("customerId")
        charge_id = event.request.get("externalOrder")
        sub_provider = event.request.get("subprovider")
        reason_code = event.request.get("reasonCode")
        if (
            customer_id and
            customer_id.startswith("cus_") and
            charge_id and
            charge_id.startswith("ch_") and
            sub_provider == "Ethoca" and
            reason_code == "Resolved"
        ):
            # refund the mastercard transaction within 120 days
            ban_user_and_refund_orders(
                human_reason="Ethoca chargeback resolved",
                charge_id=charge_id,
                mastercard_only=True,
            )
    else:
        return


def _reset_billing_cycle_for_invoice_with_multiple_attempt(
    user_id: str,
    log_data: LogContentType
):
    user = User()
    user.load_by_id(user_id)
    sub_id = user.stripe.get("subscription_id")
    if not sub_id:
        err = "User doesn't have subscription ID"
        print(err)
        log_data["error"] = err
        raise Exception(err)
    # get the existing invoice before resetting
    # these invoices need to be voided later
    try:
        invoice_list_object = list_all_open_invoices(sub_id)
        invoice_ids = [i.id for i in invoice_list_object if i and i.id]
    except Exception as e:
        err = f"failed to retrieve invoice list: {str(e)}"
        print(err)
        log_data["error"] = err
        raise

    print(f"Invoices needed to void: {invoice_ids}")
    # change the billing cycle
    try:
        new_sub = stripe.Subscription.modify(
            sub_id,
            billing_cycle_anchor="now",
            proration_behavior="none",
        )
    except Exception as e:
        err = f"failed to modify the billing cycle: {str(e)}"
        print(err)
        log_data["error"] = err
        raise
    # void previous invoices
    voided_invoices: List[str] = []
    voided_failed: List[str] = []
    for inv_id in invoice_ids:
        try:
            void_invoice(inv_id, "reset_billing_cycle_for_invoice_with_multiple_attempt")
            voided_invoices.append(inv_id)
        except Exception as e:
            err = f"failed to void invoice {inv_id}: {str(e)}"
            print(err)
            voided_failed.append(inv_id)
    log_data["voided_invoices"] = voided_invoices
    if voided_failed:
        log_data["error"] = f"failed to void invoices: {voided_failed}"

    latest_invoice_id = get_subscription_latest_invoice_id(new_sub)
    if latest_invoice_id:
        stripe.Invoice.modify(
            latest_invoice_id,
            metadata={INVOICE_UPDATED_OCCASION: "reset_billing_cycle_for_invoice_with_multiple_attempt"}
        )


@app.task(ignore_result=True)
def send_membership_cycle_invoice_paid_reward(user_id: str, invoice_id: str):
    from commonlib.duo_deal_auto_completion import add_ticket, get_monthly_ticket_reward

    user = User()
    user.load_by_id(user_id)
    membership = user.get_or_create_membership()
    try:
        add_ticket(
            user=user,
            reason=f"subscription_cycle_invoice_paid_{invoice_id}",
            amount=get_monthly_ticket_reward(membership),
        )
    except BlidzException:
        pass

    fill_up_season_progress(user_id, reason=f"subscription_cycle_invoice_paid_{invoice_id}")
