import json
import traceback
from time import time
from typing import Any, Dict, List, Optional, Union

from bson import ObjectId
from pytasched.tasks import Task

from commonlib.basic.structured_log import (
    LogContentType,
    get_step_logger,
    get_structured_logger,
)
from commonlib.clevertap.clevertap import <PERSON><PERSON>erTap, Event
from commonlib.coindata import COIN_TYPE_FREE
from commonlib.duo_deal_auto_completion import (
    add_ticket,
    get_upgrading_membership_ticket_reward,
)
from commonlib.exceptions import BlidzException, ModelNotFoundError
from commonlib.fincoin_wallet.origin import AddFincoinOrigin
from commonlib.fincoin_wallet.util import add_fincoin
from commonlib.models.bank_card_bound_reward import (
    BankCardBoundReward,
    BankCardBoundRewardCategory,
    BankCardBoundRewardType,
)
from commonlib.models.cashback import CashBack
from commonlib.models.coins import SpentCoins
from commonlib.models.deal import Deal
from commonlib.models.fincoin_wallet import FincoinExchangeRate
from commonlib.models.membership import Membership, SubscriptionPlatform, Tier
from commonlib.models.order import Order
from commonlib.models.user import User
from commonlib.notifications.notification_manager import notification_manager
from commonlib.notifications.push_notification import PushNotification
from commonlib.notifications.utils import PushMessage
from commonlib.payout.payout_turn import add_payout_turn
from commonlib.publisher import publish_to_user_channel
from commonlib.pusu_models.update_key import UpdateUserKey
from commonlib.revenue_cat.api_v2.api.subscription import cancel_subscription
from commonlib.revenue_cat.get_api_client import call_revenue_cat_api, get_api_v2_client
from commonlib.settings import (
    REASON_BANK_CARD_BOUND_REWARD,
    REASON_CHECKOUT_CASHBACK,
    REVENUE_CAT_PROJECT_ID,
)
from commonlib.settings_cash import CASH_TYPE_NON_REFUNDABLE
from commonlib.settings_gift_drop import WELCOME_SERIES_CAMPAIGN_ID
from commonlib.spin_wheel.user_spin_action import add_spin
from commonlib.stripe.helpers import cancel_subscription_immediately
from commonlib.stripe.subscription import SubscribeAction
from commonlib.utils.time_constants import DAY, HOUR
from commonlib.utils.user import add_user_cash, can_receive_backup_card_rewards
from commonlib.utils.utils import choose_value_for_env
from worker.app import app

structured_logger = get_structured_logger("tasks_users")


@app.task(ignore_result=True)
def remind_sign_up_mystery_box(user_id: str, game_given_time: int, try_count: int = 1):
    from commonlib.notifications.notification_manager import notification_manager

    structured_log_data: LogContentType = {
        "action": "remind_sign_up_mystery_box",
        "user_id": user_id,
        "try_count": try_count,
        "game_given_time": game_given_time,
    }

    user = User()

    try:
        user.load_by_id(user_id)
        mystery_box_games = user.miniGame.availableMiniGames.get("mysterybox", [])

        if not mystery_box_games:
            return

        sign_up_mystery_box_game = next(
            i for i in mystery_box_games if i["given"] == game_given_time
        )

        if not sign_up_mystery_box_game:
            return

        notification_manager.send_notification(
            user=user, notification=PushNotification(**PushMessage.WELCOME_MYSTERY_BOX_REMINDER))
        structured_log_data["sent_reminder"] = True
    except Exception as error:
        structured_log_data["error"] = str(error)

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def schedule_push_message(
    user_id: str,
    message: dict,
    mobile_notification: bool = True,
    desktop_notification: bool = True,
    data: Optional[dict] = None,
    params: Optional[dict] = None,
):
    from commonlib.notifications.notification_manager import notification_manager

    structured_log_data: LogContentType = {
        "action": "schedule_push_message",
        "message": message.get("name"),
        "user_id": user_id,
        "mobile_notification": mobile_notification,
        "desktop_notification": desktop_notification,
    }

    user = User()

    try:
        user.load_by_id(user_id)
        notification_manager.send_notification(
            user=user,
            notification=PushNotification(
                **message,
                format_data=data,
                custom_params={
                    "params": params,
                },
            ),

            in_app_notification=desktop_notification,
        )
    except Exception as error:
        structured_log_data["error"] = str(error)

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def remind_special_blidz_point(user_id: str, day: int):
    from commonlib.notifications.notification_manager import notification_manager

    structured_log_data: LogContentType = {
        "action": "remind_special_blidz_point",
        "user_id": user_id,
        "day": day,
    }

    try:
        coin_spent_count, _ = SpentCoins.get_spent_coins_for_user(user_id)
        if coin_spent_count == 0:
            user = User()
            user.load_by_id(user_id)
            notification_manager.send_notification(
                user=user, notification=PushNotification(**PushMessage.SPECIAL_POINT_REMINDER)
            )
            structured_log_data["sent_reminder"] = True
    except Exception as error:
        structured_log_data["error"] = str(error)

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def remind_unused_keys(deal_id: str, user_id: str, try_count: int):
    from commonlib.notifications.notification_manager import notification_manager

    structured_log_data: LogContentType = {
        "action": "remind_special_blidz_point",
        "deal_id": deal_id,
        "user_id": user_id,
        "count": try_count,
    }
    try:
        deal = Deal()
        deal.load_by_id(deal_id)
        if deal.state in [deal.STATE_CANCELED, deal.STATE_CLOSED]:
            return

        user_race_data = deal.usersInRace.get(user_id)
        key_left = 0
        if user_race_data:
            key_left = user_race_data.get("boostCount", 0) - user_race_data.get(
                "boostUsed", 0
            )
        if key_left <= 0:
            return

        user = User()
        user.load_by_id(user_id)
        notification_manager.send_notification(
            user=user,
            notification=PushNotification(
                **PushMessage.TREASURE_HIGH_UNUSED_KEYS,
                format_data={"keyCount": str(key_left)},
                custom_params={
                    "deal_id": deal_id,
                    "params": {
                        "id": deal_id,
                    },
                },
            ),
        )
        structured_log_data["sent_reminder"] = True

        delay_time = choose_value_for_env(DAY, 12 * HOUR)
        task = Task(
            "worker.tasks_users:remind_unused_keys.delay",
            kwargs={"deal_id": deal_id, "user_id": user_id, "try_count": try_count + 1},
            when=time() + float(delay_time),
        )
        task.add()
        structured_log_data["schedule_reminder"] = True

    except Exception as error:
        structured_log_data["error"] = str(error)

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def send_cashdrop_intro(user_id: str):
    from commonlib.models.gift_drop import UserGiftDrop
    from commonlib.notifications.notification_manager import notification_manager

    drops = UserGiftDrop.find(
        {
            "user_id": user_id,
            "campaign_id": WELCOME_SERIES_CAMPAIGN_ID,
            "claimed": False,
            "disabled": False,
        }
    )
    if not drops:
        return

    send_remind = False
    for drop in drops:
        if drop.can_claim_now():
            send_remind = True
            break
    if send_remind:
        try:
            user = User()
            user.load_by_id(user_id)
            notification_manager.send_notification(
                user=user,
                notification=PushNotification(**PushMessage.GIFT_DROP_INTRO),
            )
        except Exception as e:
            print(f"Cannot schedule Giftdrop Intro \n {str(e)}")


@app.task(ignore_result=True)
def add_cash(
    user_id: str,
    amount: int,
    cash_type: str,
    reason: str,
    push_message: Optional[Dict[str, Any]] = None,
    push_message_data: Optional[Dict[str, Any]] = None,
    order_id: Optional[str] = None,
):
    """[summary]

    Args:
        user_id (str): [description]
        amount (int): in cents
        cash_type (str): cash type
        reason (str): reason to add cash
        push_message: push message if available
        push_message_data: data for push message if necessary
    """
    from commonlib.notifications.notification_manager import notification_manager

    structured_log_data: LogContentType = {
        "action": "give_cash_to_user",
        "user_id": user_id,
        "amount": amount,
        "cash_type": cash_type,
        "reason": reason,
    }
    try:
        user = User()
        user.load_by_id(user_id)
        add_user_cash(
            cash_cents=amount,
            cash_type=cash_type,
            reason=reason,
            order_id=order_id,
            user=user
        )
        user.push_to_clients(user_cash=True)
        if push_message:
            format_data = push_message_data if push_message_data else {}
            notification_manager.send_notification(
                user=user,
                notification=PushNotification(**push_message, format_data=format_data),

            )
            structured_log_data["sent_push_message"] = True

    except Exception as error:
        structured_log_data["error"] = str(error)
        structured_log_data["traceback"] = traceback.format_exc()

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def add_coin(
    user_id: str,
    amount: int,
    reason: str,
    human_reason: str,
    push_message: Optional[Dict[str, Any]] = None,
    push_message_data: Optional[Dict[str, Any]] = None,
    coin_type: str = COIN_TYPE_FREE,
    mobile_notification: bool = True,
):
    """[summary]

    Args:
        user_id (str): [description]
        amount (int): in cents
        coin_type (str): coin type
        reason (str): reason to add coin
        push_message: push message if available
        push_mesage_data: data for push message if necessary
    """
    from commonlib.notifications.notification_manager import notification_manager

    structured_log_data: LogContentType = {
        "action": "give_coin_to_user",
        "user_id": user_id,
        "amount": amount,
        "reason": reason,
    }
    try:
        user = User()
        user.load_by_id(user_id)
        user.add_coins(
            count=amount, reason=reason, human_reason=human_reason, coin_type=coin_type
        )
        if push_message:
            format_data = push_message_data or {}
            notification_manager.send_notification(
                user=user,
                notification=PushNotification(
                    **push_message,
                    format_data=format_data,
                )
            )
            structured_log_data["sent_push_message"] = True

    except Exception as error:
        structured_log_data["error"] = str(error)

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def give_cash_back(order_id: str, duo_deal_entry_id: str):
    from sherlock import Lock

    from commonlib.utils.cashback import (
        calculate_cashback_amount,
        check_available_to_give_cashback,
        get_cashback_text,
    )

    structured_log_data: LogContentType = {
        "action": "give_blidz_cashback_to_user",
        "order_id": order_id,
        "duo_deal_entry_id": duo_deal_entry_id,
    }

    with Lock("give_cash_back_{}".format(order_id)):
        try:
            cashback = CashBack.get_ready_to_send(order_id, duo_deal_entry_id)
            if not cashback or not cashback.order_id or not cashback.user_id:
                return
            order = Order()
            order.load_by_id(cashback.order_id)

            assert order.price is not None
            check_available_to_give_cashback(duo_deal_entry_id, order.price)

            structured_log_data["userId"] = cashback.user_id
            cash_amount = calculate_cashback_amount(order.price, cashback)
            add_cash(
                user_id=cashback.user_id,
                amount=cash_amount,
                cash_type=CASH_TYPE_NON_REFUNDABLE,
                reason=REASON_CHECKOUT_CASHBACK,
                push_message=PushMessage.CASH_BACK,
                push_message_data={"cashback_name": get_cashback_text(cashback)},
            )
            with cashback.simple_update_session():
                cashback.sent = True
            structured_log_data["sent"] = True

        except Exception as err:
            structured_log_data["error"] = str(err)

    structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def initiate_account_deletion(user_id: str):
    """
    Initiate user account deletion.

    :param user_id:
    :return:
    """
    from commonlib.models.user_account_delete import UserAccountDelete
    from commonlib.stripe.helpers import cancel_subscription
    from commonlib.utils.time import get_js_time

    structured_log_data: LogContentType = {
        "action": "initiate_account_deletion",
        "user_id": user_id,
    }

    # If the user already has initiated the deletion, just skip this step.
    account_delete = UserAccountDelete.find_one({"user_id": user_id})
    if not account_delete:
        account_delete = UserAccountDelete(
            {"user_id": user_id, "initiated_time": get_js_time()}
        )
        account_delete.create()

        try:
            user = User()
            user.load_by_id(user_id)
            sub_obj = cancel_subscription(
                user,
                cancel_at_period_end=False,
                reason="account_deletion",
            )
            if sub_obj:
                structured_log_data["subscription_canceled"] = True
            else:
                structured_log_data["subscription_canceled"] = False
                structured_log_data["cancel_subscription_error"] = "no active subscription found"
        except ModelNotFoundError as e:
            raise e
        except Exception as e:
            structured_log_data["subscription_canceled"] = False
            structured_log_data["cancel_subscription_error"] = str(e)

        structured_logger.info(structured_log_data)


@app.task(ignore_result=True)
def recalculate_friend_count(user_id: str):
    """Recalculates the user's friend count."""
    from commonlib.models.user_stats import UserStats
    from commonlib.social_circles.graph.definitions import SocialCirclesGraph
    from commonlib.social_circles.models.nodes import User as ArangoUser

    try:
        arango_user: ArangoUser = ArangoUser.find_one({"_key": user_id})
        assert arango_user.key_ is not None
        result = SocialCirclesGraph.get_user_friends(arango_user.key_)
        user_stats = UserStats.by_user_id(user_id)

        # Since we recalculate the friend count, we can't just increment
        # the friend count, but we need to check the difference and then
        # increment by the diff.
        current_friends = user_stats.friend_count if user_stats else 0

        # First time we don't have any friends, so the given count is correct.
        # Or if we gave this function an incremental value, use that instead.
        count = result.count() or 0 if result else 0
        value = count
        if current_friends > 0:
            # Next time around, we already have a count, now we need to check the diff.
            value = count - current_friends

        UserStats.record(user_id, UserStats.FRIEND_COUNT, count=value)
    except ModelNotFoundError:
        print(f"Invalid user_id given or user not found: {user_id=}")


@app.task(ignore_result=True)
def schedule_clevertap_event(
    identifier: Union[str, List[str]],
    event_name: Event.Names,
    data: Optional[Dict[str, Any]] = None,
):
    """
    Queue clevertap events.
    """
    if not data:
        data = {}

    if isinstance(identifier, list):
        identifiers = identifier
    else:
        identifiers = [identifier]

    events = CleverTap.generate_events_to_send(
        identifiers=identifiers,  # type: ignore
        event_name=event_name,
        data=data,
        event_type="event",
    )
    CleverTap.queue_events(events)

    logger = get_step_logger("schedule_clevertap_event")
    for e in events:
        assert e.identity is not None
        logger.add_step(e.identity, {
            "event_name": e.event_name,
            "ts": e.ts,
            "event_data": json.dumps(e.event_data) if e.event_data else None
        })
    logger.finish_log()
    print("Queued CleverTap events.")


@app.task(ignore_result=True)
def add_is_friend_relationship_to_users(user_id: str, friend_ids: List[str]):
    """
    Adds the bidirectional relationship `is_friend` to the list of user ids.

    :param user_id: The user who is adding the friends.
    :param friend_ids: List of user ids the user wants to add as friends.
    """
    from commonlib.social_circles.models.relationships import SocialFeedRelationship
    from commonlib.social_circles.utils import add_arangodb_relation

    for uid in friend_ids:
        try:
            # Add (user_id) -- is_friend -> (friend)
            add_arangodb_relation(
                user_id,
                uid,
                SocialFeedRelationship,
                SocialFeedRelationship.Types.IS_FRIEND,
                {},
            )
        except Exception as ex:
            print(f"Could not create relationship: {user_id} -- is_friend --> {uid}. {ex=}")

        try:
            # Add (friend) -- is_friend -> (user_id)
            add_arangodb_relation(
                uid,
                user_id,
                SocialFeedRelationship,
                SocialFeedRelationship.Types.IS_FRIEND,
                {},
            )
        except Exception as ex:
            print(f"Could not create relationship: {user_id} <-- is_friend -- {uid}. {ex=}")


@app.task(ignore_result=True)
def give_authentication_rewards(user_id: str, lookup_key: Optional[str], reason: str):
    from commonlib.payout.payout import PayoutCooldown
    user = User()
    user.load_by_id(user_id)

    action = SubscribeAction.AUTHENTICATION
    if fincoin_to_add := action.fincoin_to_add():
        add_fincoin(
            origin=AddFincoinOrigin.AUTHENTICATION_REWARD,
            user_id=user_id,
            amount=fincoin_to_add,
            idempotent_id=str(ObjectId()),
            reason=action.get_reason(),
        )

    if payout_turn_to_add := action.payout_turn_to_add():
        add_payout_turn(
            user_id=user_id,
            amount=payout_turn_to_add,
            reason=action.get_reason(),
        )

    PayoutCooldown.add_payout_cooldown_flag_of_reset(user_id, action.get_reason())


@app.task(ignore_result=True)
def give_upgrade_subscription_rewards(user_id: str, prev_tier: Tier, after_tier: Tier):
    from commonlib.payout.payout import PayoutCooldown
    prev_tier = Tier(prev_tier)
    after_tier = Tier(after_tier)

    user = User()
    user.load_by_id(user_id)

    action = SubscribeAction.from_tiers(prev_tier, after_tier)
    assert action is not None
    if fincoin_to_add := action.fincoin_to_add():
        add_fincoin(
            origin=AddFincoinOrigin.UPGRADE_SUBSCRIPTION_REWARD,
            user_id=user_id,
            amount=fincoin_to_add,
            idempotent_id=str(ObjectId()),
            reason=action.get_reason(),
        )
    if payout_turn_to_add := action.payout_turn_to_add():
        add_payout_turn(
            user_id=user_id,
            amount=payout_turn_to_add,
            reason=action.get_reason(),
        )

    try:
        add_ticket(
            user=user,
            reason=f"upgrade_subscription_{prev_tier.value}_to_{after_tier.value}",
            amount=get_upgrading_membership_ticket_reward(after_tier),
            current_tier=after_tier,
        )
    except BlidzException:
        pass

    PayoutCooldown.add_payout_cooldown_flag_of_reset(user_id, action.get_reason())


@app.task(ignore_result=True)
def give_bank_card_bound_reward(user_id: str, payment_method_id: str):
    user = User.find_one({"_id": ObjectId(user_id)})
    if not user or not user.stripe or not user.stripe.get("paymentMethods"):
        print("no payment methods")
        return

    payment_methods = user.stripe["paymentMethods"]
    pm = payment_methods.get(payment_method_id)
    if not pm:
        print("payment method not found")
        return

    r = BankCardBoundReward()
    r.user_id = user_id
    r.card_fingerprint = pm.get("fingerprint")
    r.last4 = pm.get("last4")
    r.funding = pm.get("funding")
    r.payment_method_id = payment_method_id

    can_receive_general_reward, can_receive_credit_card_reward = can_receive_backup_card_rewards(
        user, payment_method_id
    )

    if can_receive_general_reward:
        add_spin(
            user_id=user_id,
            amount=1,
            reason="bank_card_bound_reward"
        )
        try:
            notification_manager.send_notification(
                user_id=user_id, notification=PushNotification(**PushMessage.GET_NEW_SPIN))
        except Exception:
            pass

        r.reward_category = BankCardBoundRewardCategory.GENERAL
        r.reward_type = BankCardBoundRewardType.SPIN
        r.reward_amount = 1
        r.create()

    if can_receive_credit_card_reward:
        add_cash(
            user_id=user_id,
            amount=1000,
            cash_type=CASH_TYPE_NON_REFUNDABLE,
            reason=REASON_BANK_CARD_BOUND_REWARD,
        )
        r2 = r.copy()
        r2.reward_category = BankCardBoundRewardCategory.CREDIT_CARD
        r2.reward_type = BankCardBoundRewardType.CASH
        r2.reward_amount = 1000
        r2.create()


@app.task(ignore_result=True)
def add_key(
    user_id: str,
    amount: int,
    coin_price: int = 0,
    reason: Optional[str] = None,
    human_reason: Optional[str] = None,
    source: Optional[str] = None,
    when: Optional[int] = None,
    additional_logger_name: Optional[str] = None,
    addition_log_data: Optional[Dict[str, Any]] = None,
):
    """
    Adds keys to the user.
    """
    from commonlib.models.treasure_hunt import UserKey

    log_data: LogContentType = {
        "action": "add_key",
        "user_id": user_id,
        "amount": amount,
        "reason": reason,
        "human_reason": human_reason,
        "source": source,
        "when": when,
    }

    try:
        user_key = UserKey.add_keys(
            user_id=user_id,
            amount=amount,
            reason=reason,
            human_reason=human_reason,
            source=source,
            when=when,
        )
    except Exception as ex:
        msg = f"Could not add keys for user {user_id}, error: {ex=}"
        print(msg)
        log_data["error"] = msg
        structured_logger.info(log_data)
        if additional_logger_name and addition_log_data:
            addition_log_data["error"] = msg
            get_structured_logger(additional_logger_name).info(addition_log_data)
        return

    try:
        # TODO: remove this after old client is deprecated
        user = User()
        user.load_by_id(user_id)
        user.push_to_clients(locked_deal_prices=True)

        publish_to_user_channel(
            user_id=user_id,
            content=UpdateUserKey(
                key_count=user_key.get_total(),
                next_free_key=user_key.next_free_key_time,
            ),
        )
    except Exception:
        pass

    structured_logger.info(log_data)

    if additional_logger_name and addition_log_data:
        get_structured_logger(additional_logger_name).info(addition_log_data)


@app.task(ignore_result=True)
def spend_key(
    user_key_id: str,
    deal_id: str,
    amount: int,
):
    """
    Adds keys to the user.
    """
    from commonlib.models.treasure_hunt import UserKey

    log_data: LogContentType = {
        "action": "spend_key",
        "user_key_id": user_key_id,
        "deal_id": deal_id,
        "amount": amount,
    }

    user_key = UserKey.by_id(user_key_id)
    spent_key_items = None
    try:
        if user_key:
            log_data["user_id"] = user_key.user_id
            spent_key_items = user_key.spend(deal_id, amount)
    except Exception as ex:
        msg = f"Could not spend keys for user {user_key_id}, error: {ex=}"
        print(msg)
        log_data["error"] = msg
        structured_logger.info(log_data)
        return

    try:
        if user_key and user_key.user_id:
            publish_to_user_channel(
                user_id=user_key.user_id,
                content=UpdateUserKey(
                    key_count=user_key.get_total(),
                    next_free_key=user_key.next_free_key_time,
                ),
            )
    except Exception:
        pass

    structured_logger.info(log_data)
    return spent_key_items


@app.task(ignore_result=True)
def update_user_stripe_info(
    user_id: str,
    params: List[str],
    email: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
):
    from commonlib.stripe.helpers import modify_customer
    log_data: LogContentType = {
        "action": "update_stripe_info",
        "user_id": user_id,
        "params": params
    }
    user = User()
    user.load_by_id(user_id)
    customer_id = user.stripe.get("id")
    if not customer_id:
        log_data["error"] = "no_customer_id"
        structured_logger.info(log_data)
        return

    try:
        modify_customer(customer_id=customer_id, email=email, metadata=metadata)
    except Exception as e:
        log_data["error"] = str(e)
        structured_logger.info(log_data)

    structured_logger.info(log_data)


@app.task(ignore_result=True)
def cancel_subscription_on_other_platforms(user_id: str, skip_platform: SubscriptionPlatform):
    skip_platform = SubscriptionPlatform(skip_platform)  # after deserialization from worker, this field is a str

    membership = Membership.by_user_id(user_id)
    if not membership or not membership.subscription_id:
        return

    logger = get_structured_logger("cancel_subscription_on_other_platforms")
    log_data: LogContentType = {
        "skip_platform": skip_platform,
        "stripe_canceled": False,
        "revenue_cat_canceled": False
    }

    if skip_platform != SubscriptionPlatform.STRIPE and membership.stripe_data:
        cancel_subscription_immediately(membership.stripe_data.id, reason=f"new_subscription_on_{skip_platform.value}")
        log_data["stripe_canceled"] = True

    if not skip_platform.is_on_revencue_cat() and membership.revenue_cat_data:
        sub_id = membership.revenue_cat_data.id
        with get_api_v2_client() as client:
            call_revenue_cat_api(
                lambda: cancel_subscription.sync_detailed(
                    REVENUE_CAT_PROJECT_ID,
                    sub_id,
                    client=client
                )
            )
        log_data["revenue_cat_canceled"] = True

    logger.info(log_data)


@app.task(ignore_result=True)
def increase_fincoin(
    origin_value: str,
    user_id: str,
    fincoin_amount: Optional[int] = None,
    currency_amount: Optional[int] = None,
    idempotent_id: Optional[str] = None,
    reason: Optional[str] = None,
    related_id: Optional[str] = None,
    related_object: Optional[str] = None,
):
    """[summary]

    Args:
        user_id (str): user's id
        fincoin_amount (int): in fincoin
        currency_amount (int): in cents
        reason (str): reason to add fincoin
    """

    structured_log_data: LogContentType = {
        "action": "increase_fincoin",
        "user_id": user_id,
        "fincoin_amount": fincoin_amount,
        "currency_amount": currency_amount,
        "reason": reason,
    }

    try:
        user = User()
        user.load_by_id(user_id)
        if fincoin_amount:
            amount = fincoin_amount
        elif currency_amount:
            amount = FincoinExchangeRate.to_fincoin(currency_amount, user.get_currency()).to_target
        else:
            raise ValueError("fincoin_amount or currency_amount must be provided")

        structured_log_data["added_fincoin_amount"] = amount
        add_fincoin(
            origin=AddFincoinOrigin(origin_value),
            user_id=user_id,
            amount=amount,
            idempotent_id=idempotent_id or str(ObjectId()),
            reason=reason,
            related_id=related_id,
            related_object=related_object,
        )

    except Exception as error:
        structured_log_data["error"] = str(error)
        structured_log_data["traceback"] = traceback.format_exc()

    structured_logger.info(structured_log_data)
