import json
from typing import Any, Dict, List, Optional, Tuple

from bson.objectid import ObjectId
from django.http import HttpRequest
from google.cloud import recaptchaenterprise_v1 as google_recaptcha
from payout.definitions import (
    DtoHistoryPayoutData,
    DtoPayoutSpecialOffer,
    PayoutHistoryRequest,
    PayoutHistoryResponse,
    PayoutNormalRequest,
    PayoutSpecialOfferRequest,
    PayoutStatsResp,
    PreviewSubscribeBenefitReq,
    PreviewSubscribeBenefitResp,
    UserPayoutSpecialOfferAvailableResp,
)
from payout.dto_transform import to_dto_payout_data, to_dto_payout_turn_data
from pymongo import DESCENDING
from sherlock import Lock

from backend.decorators import catch_error, check_client_version, log_request
from backend.recaptcha_util import make_recaptcha_assessment
from backend.utils import (
    api_response_new,
    get_client_ip,
    get_device_id,
    load_request_body,
    load_user,
)
from commonlib import live_config
from commonlib.basic.structured_log import Log<PERSON>ontentType, get_structured_logger
from commonlib.exceptions import BlacklistException, BlidzException
from commonlib.models.enums import PayoutOfferTypes
from commonlib.models.fincoin_wallet import FincoinExchangeRate, FincoinWallet
from commonlib.models.membership import Membership, MembershipStatus, Tier
from commonlib.models.payout.payout_budget import (
    PayoutBudget,
    PayoutBudgetLimitExceeded,
)
from commonlib.models.payout.payout_config import PAYOUT_CONFIG
from commonlib.models.payout.payout_log import PayoutType
from commonlib.models.payout.payout_request import PayoutRequest
from commonlib.models.payout.payout_transaction import PayoutTransaction
from commonlib.models.payout.user_payout_offers import UserPayoutOffer
from commonlib.models.user import User
from commonlib.payout.payout import (
    PayoutCooldown,
    VerifyOrGetAvailablePayoutAmountCustomAttributes,
    check_payout_rate_limits,
    general_payout,
    get_payout_info,
    verify_or_get_available_payout_amount,
)
from commonlib.payout.payout_turn import (
    CalculateMaximumPayoutTurnAmountCustomAttributes,
    get_payout_turn_amount,
)
from commonlib.payout.payout_turn_config import get_payout_turn_stack_limit
from commonlib.recaptcha.models.recaptcha_log import RecaptchaAction
from commonlib.spin_wheel.user_spin_action import add_spin
from commonlib.spin_wheel.withdrawal_action import on_withdraw
from commonlib.stripe.subscription import SubscribeAction
from commonlib.utils.blacklist import check_blacklist
from commonlib.utils.environment import is_staging
from commonlib.utils.payout import get_payout_stat
from commonlib.utils.time import get_js_time
from commonlib.utils.utils import (
    clean_email_tag,
    is_internal_email,
    verify_email_3rd_party,
)
from worker.tasks_payout import cancel_failed_payout_transactions


@log_request
@catch_error
@check_client_version
def history(request: HttpRequest):
    """
    ..http::post:: /api/payout/v2/history

    Show the user's payout history.
    """
    user = load_user(request, allow_zero_step_account=False)
    request_body = load_request_body(request, PayoutHistoryRequest)

    result = PayoutTransaction.find(
        {"user_id": user.get_id()},
        limit=request_body.size,
        skip=(request_body.page - 1) * request_body.size,
        sort=[("payout_time", DESCENDING)]
    )
    payout_transactions = list(result)

    payouts: List[DtoHistoryPayoutData] = []
    for payout_transaction in payout_transactions:
        if payout_transaction.payout_time and payout_transaction.amount:
            history_payout_data = DtoHistoryPayoutData(
                payout_id=payout_transaction.get_id_assert(),
                payout_time=payout_transaction.payout_time,
                amount=payout_transaction.amount,
                transaction_status=payout_transaction.transaction_status,
                payout_type=payout_transaction.payout_type,
                payout_account=payout_transaction.receiver,
            )
            payouts.append(history_payout_data)

    payout = to_dto_payout_data(user, user.get_or_create_membership())
    response = PayoutHistoryResponse(
        payouts=payouts,
        payout=payout
    )

    return api_response_new(content=response)


def verify_payout_account(payout_account: Optional[str], payout_type: PayoutType):
    if not payout_account:
        raise BlidzException(
            status=422,
            message="User doesn't have a Payout account.",
            code="no_payout_account"
        )
    if payout_type == PayoutType.PAYPAL:
        verify_paypal_account(payout_account)
    elif payout_type == PayoutType.GIFT_CARD:
        cleaned_email = clean_email_tag(payout_account)
        verify_email_3rd_party(cleaned_email)


def alloc_payout_budget(
    amount: int,
    user_id: str,
    membership: Membership,
    stripe_customer_id: Optional[str],
) -> str:
    # Make sure the payout amount under the payout budget.
    try:
        return PayoutBudget.alloc_payout_budget(
            amount,
            user_id,
            membership,
            stripe_customer_id,
        )
    except PayoutBudgetLimitExceeded as ex:
        raise BlidzException(status=400, message=str(ex), code="out_of_payout_budget")


def query_user_payout_offers(
    user_id: str,
    special_offer_types: Optional[List[PayoutOfferTypes]] = None
) -> List[UserPayoutOffer]:
    now = get_js_time()
    query: Dict[str, Any] = {
        "user_id": user_id,
        "claimed": False,
        "$or": [
            {"expire_at": {"$gte": now}},
            {"expire_at": None}
        ]
    }
    if special_offer_types:
        query.update({"offer_type": {"$in": special_offer_types}})
    offers_query_result = UserPayoutOffer.find(query)
    return list(offers_query_result)


def _get_user_payout_offers(special_offer_types: List[PayoutOfferTypes], user_id: str) -> List[UserPayoutOffer]:
    ret = query_user_payout_offers(user_id, special_offer_types)

    available_offer_types = [upo.offer_type for upo in ret]
    if PayoutOfferTypes.PAYOUT_FREE in special_offer_types:
        if PayoutOfferTypes.PAYOUT_FREE not in available_offer_types:
            payout_free_offer_amount = live_config.get("payout_special_offer").get(PayoutOfferTypes.PAYOUT_FREE, 0)
            new_user_payout = UserPayoutOffer({
                "user_id": user_id,
                "reason": PayoutOfferTypes.PAYOUT_FREE.value,
                "offer_type": PayoutOfferTypes.PAYOUT_FREE,
                "amount": payout_free_offer_amount
            })
            new_user_payout.create()
            ret.append(new_user_payout)

    return ret


def _get_validated_special_offers(
    user_special_offers: List[UserPayoutOffer],
    user: User,
    payout_account: Optional[str] = None,
) -> Tuple[List[UserPayoutOffer], int]:
    total_amount: int = 0
    validated_offers: List[UserPayoutOffer] = []

    if not user_special_offers:
        return validated_offers, 0
    for offer in user_special_offers:
        if not offer.amount:
            continue

        validated_offer = _validate_payout_special_offer(
            offer,
            user,
            payout_account=payout_account,
        )
        if not validated_offer:
            continue

        total_amount += offer.amount
        validated_offers.append(offer)

    return validated_offers, total_amount


def _validate_payout_special_offer(
    offer: UserPayoutOffer,
    user: User,
    payout_account: Optional[str] = None,
) -> bool:
    validation_status: bool = True
    if not offer.amount or not offer.offer_type or not offer.verify_available():
        return False

    query: Dict[str, Any] = {
        "user_id": user.get_id_assert(),
    }

    query.update({"extra_offers.reason": offer.reason})

    got_offer = PayoutTransaction.find_one(query)
    if got_offer:
        return False

    if offer.offer_type == PayoutOfferTypes.PAYOUT_FREE:
        validation_status = _validate_offer_type_free(user, payout_account)
    elif offer.offer_type == PayoutOfferTypes.SPIN_LEADERBOARD:
        validation_status = _validate_offer_type_spin_leaderboard(user, payout_account)
    return validation_status


def _validate_offer_type_spin_leaderboard(user: User, payout_account: Optional[str]) -> bool:
    validation_status: bool = True
    membership_status = user.get_or_create_membership().status
    if not payout_account:
        validation_status = False
    elif membership_status != MembershipStatus.ACTIVE_MEMBER:
        validation_status = False

    return validation_status


def _validate_offer_type_free(user: User, payout_account: Optional[str]) -> bool:
    validation_status: bool = True
    membership_status = user.get_or_create_membership().status
    if not payout_account:
        validation_status = False
    elif membership_status == MembershipStatus.NON_MEMBER and "@" in payout_account:
        # If user is non_member, require paypal login => payout_account must be not an email
        validation_status = False
    elif membership_status in [MembershipStatus.ACTIVE_MEMBER,
                               MembershipStatus.ACTIVE_MEMBER_NO_RENEWAL]:
        validation_status = False

    return validation_status


def _payout_lock_for_user(user_id: str) -> Lock:
    """
    To limit the user payout amount
    each payout action must under this lock

    :return: sherlock.Lock instance
    """
    return Lock(f"payout_transaction_local_lock_{user_id}")


@log_request
@catch_error
@check_client_version
def normal_payout(request: HttpRequest):
    """
    ..http::post:: /api/payout/v2/normal

    Adds the payout amount and user data to a payout queue.
    """
    payout_request = PayoutRequest()
    payout_request.uri = request.path
    payout_request.request_time = get_js_time()
    payout_request.request_body = json.loads(request.body) if request.body else {}
    try:
        return _normal_payout(request, payout_request_log=payout_request)
    except Exception as e:
        payout_request.error = str(e)
        raise e
    finally:
        payout_request.create()


def _normal_payout(request: HttpRequest, payout_request_log: PayoutRequest):
    request_body = load_request_body(request, PayoutNormalRequest)
    user = load_user(request, allow_zero_step_account=False)
    user_id = user.get_id_assert()
    payout_request_log.user_id = user_id
    payout_type = request_body.payout_type or PayoutType.PAYPAL
    payout_request_log.payout_type = payout_type

    # To easily disable the payouts via live_config.
    payout_enabled = bool(live_config.get("payout_enabled").get("enabled"))
    payout_request_log.payout_enabled = payout_enabled
    if not payout_enabled:
        payout_request_log.error = "payout_disabled"
        return api_response_new(status=429, error_code="limit_exceeded")

    assessment: Optional[google_recaptcha.Assessment] = None
    membership = user.get_or_create_membership()

    if not (
        is_staging() and
        not PAYOUT_CONFIG["check_rate_limit_staging"]
    ):
        try:
            ip_address = get_client_ip(request)
            payout_request_log.ip = ip_address
            device_id = get_device_id(request)
            if not device_id or device_id == "unknown":
                device_id = user.signupDeviceId
            payout_request_log.device_id = device_id
            check_payout_rate_limits(user, ip_address=ip_address, device_id=device_id)
            if request_body.recaptcha_token:
                assessment = make_recaptcha_assessment(
                    request=request,
                    token=request_body.recaptcha_token,
                    client_ip=ip_address,
                    expected_action=RecaptchaAction.PAYOUT
                )
            PayoutCooldown.check_payout_cooldown(user, membership)
        except BlidzException as ex:
            raise BlidzException(
                status=429,
                code=ex.code,
                message=ex.message,
            )

    payout_request_log.subscription_id = membership.subscription_id
    payout_request_log.membership_status = membership.status

    if assessment and (
        not membership.is_active_member(only_active_status=True)
    ) and (
        not is_internal_email(user.basicInfo.get("cleanEmail", ""))
    ):
        payout_request_log.recaptcha_score = assessment.risk_analysis.score
        # check non-active member only
        if (
            google_recaptcha.RiskAnalysis.ClassificationReason.UNEXPECTED_ENVIRONMENT
            in assessment.risk_analysis.reasons
            or assessment.risk_analysis.score < 0.7
        ):
            # most likely scammer
            raise BlidzException(
                status=400,
                code="payout_denied",
                message=(
                    f"risk_analysis_reason={str(assessment.risk_analysis.reasons)}, "
                    f"score={assessment.risk_analysis.score}"
                ),
            )

    payout_account, parameter = get_payout_info(user=user, payout_type=payout_type)
    payout_request_log.payout_account = payout_account
    verify_payout_account(payout_account=payout_account, payout_type=payout_type)
    is_first_payout = not PayoutTransaction.find_one({"user_id": user.get_id_assert()})
    payout_request_log.is_first_payout = is_first_payout

    if request_body.payout_fincoin:
        request_amount = FincoinExchangeRate.to_cent(request_body.payout_fincoin, user.get_currency()).to_target
    elif request_body.payout_amount:
        request_amount = request_body.payout_amount
    else:
        request_amount = None

    with _payout_lock_for_user(user_id):
        amount = verify_or_get_available_payout_amount(
            user,
            membership,
            specify_payout_amount=request_amount,
        )
        payout_request_log.request_amount = request_amount
        payout_request_log.payout_amount = amount

        special_offers_types: List[PayoutOfferTypes] = request_body.special_offers or []
        validated_extra_offers: List[UserPayoutOffer] = []
        validated_extra_offers_amount: int = 0
        user_special_offers: List[UserPayoutOffer] = []
        if special_offers_types:
            try:
                user_special_offers = _get_user_payout_offers(special_offers_types, user.get_id_assert())
                validated_extra_offers, validated_extra_offers_amount = _get_validated_special_offers(
                    user_special_offers,
                    user,
                )

            except Exception as ex:
                print(f"Cannot get payout offer {ex}")
                raise BlidzException(
                    status=400,
                    message="Cannot get payout offer",
                    code="cannot_get_payout_offer"
                )
        if validated_extra_offers:
            _claim_payout_offers(validated_extra_offers)

        try:
            payout_request_log.has_bypass_limit_flag = False
            allocation_id = alloc_payout_budget(
                amount=amount,
                user_id=user.get_id_assert(),
                membership=membership,
                stripe_customer_id=user.stripe.get("id"),
            )
            payout_request_log.allocation_id = allocation_id

            # Do the payout.
            payout_transaction = general_payout(
                user,
                amount=amount + validated_extra_offers_amount,
                fincoin_amount=FincoinExchangeRate.to_fincoin(
                    amount + validated_extra_offers_amount,
                    user.get_currency(),
                ).to_target,
                payout_type=payout_type,
                parameter=parameter,
                currency=user.get_currency(),
                extra_offers=validated_extra_offers,
                assessment=assessment,
                allocation_id=allocation_id,
            )

            payout_request_log.payout_transaction_id = payout_transaction.get_id_assert()
            payout_request_log.is_manual_payout = payout_transaction.is_manual_payout()
            payout_request_log.payout_time = payout_transaction.payout_time

            on_withdraw(
                user_id,
                amount=amount,
                currency=user.get_currency(),
                payout_transaction=payout_transaction,
            )
            PayoutCooldown.remove_payout_cooldown_flag_of_reset(user_id)
            cancel_failed_payout_transactions.delay(user_id, payout_transaction.get_id_assert())

        except BlidzException:
            raise

        except Exception as ex:
            raise BlidzException(status=400, message=str(ex), code="payout_failed")

    if is_first_payout:
        add_spin(user_id, 1, is_free_spin=True)
    return api_response_new()


@log_request
@catch_error
@check_client_version
def special_offers(request: HttpRequest):
    """
    ..http::post:: /api/payout/v2/special_offers

    Adds the payout amount and user data to a payout queue.
    This api is used only to give exclusive offer amount without withdrawing from user's cash
    """
    payout_request = PayoutRequest()
    payout_request.uri = request.path
    payout_request.request_time = get_js_time()
    payout_request.request_body = json.loads(request.body) if request.body else {}
    try:
        return _special_offers(request, payout_request_log=payout_request)
    except Exception as e:
        payout_request.error = str(e)
        raise e
    finally:
        payout_request.create()


def _special_offers(request: HttpRequest, payout_request_log: PayoutRequest):
    request_body = load_request_body(request, PayoutSpecialOfferRequest)
    user = load_user(request, allow_zero_step_account=False)
    payout_request_log.user_id = user.get_id_assert()
    payout_request_log.payout_type = PayoutType.PAYPAL

    # To easily disable the payouts via live_config.
    payout_enabled = bool(live_config.get("payout_enabled").get("enabled"))
    payout_request_log.payout_enabled = payout_enabled
    if not payout_enabled:
        payout_request_log.error = "payout_disabled"
        return api_response_new(status=429, error_code="limit_exceeded")

    payout_account, parameter = get_payout_info(user=user, payout_type=PayoutType.PAYPAL)
    payout_request_log.payout_account = payout_account
    verify_payout_account(payout_account=payout_account, payout_type=PayoutType.PAYPAL)

    payout_amount = live_config.get("payout_special_offer").get(PayoutOfferTypes.PAYOUT_FREE.value, 0)
    special_offers: List[PayoutOfferTypes] = request_body.special_offers or [PayoutOfferTypes.PAYOUT_FREE]
    try:
        user_special_offers = _get_user_payout_offers(special_offers, user.get_id_assert())
        validated_extra_offers, payout_amount = _get_validated_special_offers(
            user_special_offers,
            user,
            payout_account=payout_account
        )
    except Exception as ex:
        print(f"Cannot get payout offer {ex}")
        raise BlidzException(
            status=400,
            message="Cannot get payout offer",
            code="cannot_get_payout_offer"
        )

    payout_request_log.payout_amount = payout_amount
    if payout_amount <= 0:
        raise BlidzException(
            status=400,
            message="Cannot get payout offers",
            code="cannot_find_offer"
        )

    if validated_extra_offers:
        _claim_payout_offers(validated_extra_offers)

    with _payout_lock_for_user(user.get_id_assert()):
        try:
            # Do the payout.
            transaction = general_payout(
                user,
                amount=payout_amount,
                fincoin_amount=FincoinExchangeRate.to_fincoin(payout_amount, user.get_currency()).to_target,
                payout_type=PayoutType.PAYPAL,
                parameter=parameter,
                currency=user.get_currency(),
                extra_offers=validated_extra_offers,
            )
            payout_request_log.payout_transaction_id = transaction.get_id_assert()
            payout_request_log.is_manual_payout = transaction.is_manual_payout()
            payout_request_log.payout_time = transaction.payout_time

        except BlidzException:
            raise

        except Exception as ex:
            raise BlidzException(status=400, message=str(ex), code="payout_failed")

        return api_response_new()


def _claim_payout_offers(user_offers: List[UserPayoutOffer]):
    offer_ids = [ObjectId(upo.get_id_assert()) for upo in user_offers]
    now = get_js_time()
    try:
        UserPayoutOffer.update_many(
            {
                "_id": {"$in": offer_ids}
            },
            {
                "$set": {
                    "claimed": True,
                    "updated_at": now,
                }
            }
        )
    except Exception as e:
        raise BlidzException(status=400, message=str(e), code="fail_to_claim_offer")


def _check_blacklist(payout_account: str):
    try:
        # Check if the PayPal account is blacklisted.
        check_blacklist("paypal_account", payout_account)
    except BlacklistException:
        raise BlidzException(
            status=401,
            message="User is not allowed to payout",
            code="payout_account_blacklisted",
        )


def verify_paypal_account(payout_account: str):
    is_email: bool = "@" in payout_account

    _check_blacklist(payout_account)

    if is_email:
        # Clean up the email, i.e. remove any +tag from the email address.
        cleaned_email = clean_email_tag(payout_account)
        # Make sure the payout account is real.
        verify_email_3rd_party(cleaned_email)


@log_request
@catch_error
@check_client_version
def payout_stat(request: HttpRequest):
    """
    ..http::get:: /api/payout/v2/stat

    Return information related to payout, e.g., total payout amount to users
    """

    resp = PayoutStatsResp()
    payout_stats = get_payout_stat()
    resp.total_paid_payout_amount = payout_stats.get("total_paid_payout_amount")
    resp.one_day_payout_count = payout_stats.get("one_day_payout_count")

    return api_response_new(content=resp)


@log_request
@catch_error
@check_client_version
def payout_current_offers(request: HttpRequest):
    """
    ..http::get:: /api/payout/v2/current_offers

    Return information related to payout, e.g., total payout amount to users
    """
    user = load_user(req=request, allow_zero_step_account=True)

    user_offers_data = query_user_payout_offers(user.get_id_assert())
    if not user_offers_data:
        return api_response_new()

    offers: List[DtoPayoutSpecialOffer] = []

    for upo in user_offers_data:
        offers.append(DtoPayoutSpecialOffer(offer_type=upo.offer_type, amount=upo.amount))

    resp = UserPayoutSpecialOfferAvailableResp()
    resp.offers = offers

    return api_response_new(content=resp)


logger = get_structured_logger("preview_subscribe_benefit")


@log_request
@catch_error
@check_client_version
def preview_subscribe_benefit(request: HttpRequest):
    """
    ..http::get:: /api/payout/v2/preview_subscribe_benefit

    this API for preview subscription benefit.
    this handler also works on /api/payout/v2/preview_jump_level, (backwards compatibility)

    Return information related to payout, e.g., total payout amount to users
    """

    log_data: LogContentType = {}
    user = load_user(req=request, allow_zero_step_account=False)
    user_id = user.get_id_assert()
    log_data["user_id"] = user_id
    log_data["last_client_version"] = user.last_client_version
    body = load_request_body(request, PreviewSubscribeBenefitReq)
    to_lookup_key = body.to_lookup_key
    to_trial = body.to_trial
    resp = PreviewSubscribeBenefitResp()

    membership = user.get_or_create_membership()
    curr_tier = membership.tier if membership.is_active_member() else None
    new_tier = Tier.from_lookup_key(to_lookup_key)
    log_data["current_tier"] = curr_tier
    log_data["new_tier"] = new_tier

    subscribe_action = SubscribeAction.from_tiers(curr_tier, new_tier) if not to_trial else SubscribeAction.TO_TRIAL
    log_data["subscribe_action"] = subscribe_action.value if subscribe_action else None

    currency = user.get_currency()
    log_data["currency"] = currency
    fincoin_balance_to_add = subscribe_action.fincoin_to_add() if subscribe_action else 0
    log_data["fincoin_balance_to_add"] = fincoin_balance_to_add
    payout_turn_to_add = subscribe_action.payout_turn_to_add() if subscribe_action else 0
    log_data["payout_turn_to_add"] = payout_turn_to_add
    resp.fincoin_balance_to_add = fincoin_balance_to_add
    resp.payout_balance_to_add = FincoinExchangeRate.to_cent(resp.fincoin_balance_to_add or 0, currency).to_target
    log_data["payout_balance_to_add"] = resp.payout_balance_to_add
    resp.action = subscribe_action.value if subscribe_action else None

    try:
        current_payout_balance_available = verify_or_get_available_payout_amount(user, membership=membership)
    except BlidzException:
        current_payout_balance_available = 0

    log_data["current_payout_balance_available"] = current_payout_balance_available

    fincoin_wallet = FincoinWallet.by_user_id(user_id)
    current_fincoin_balance = fincoin_wallet.amount or 0 if fincoin_wallet else 0
    log_data["current_fincoin_balance"] = current_fincoin_balance
    current_payout_turn = get_payout_turn_amount(user_id, curr_tier)
    log_data["current_payout_turn"] = current_payout_turn
    next_tier_payout_turn_stack_limit = get_payout_turn_stack_limit(new_tier, user.last_client_version)
    log_data["next_tier_payout_turn_stack_limit"] = next_tier_payout_turn_stack_limit
    next_tier_payout_turn = min(current_payout_turn + payout_turn_to_add, next_tier_payout_turn_stack_limit)
    log_data["next_tier_payout_turn"] = next_tier_payout_turn
    next_tier_fincoin_balance = current_fincoin_balance + fincoin_balance_to_add
    log_data["next_tier_fincoin_balance"] = next_tier_fincoin_balance

    custom_attributes = VerifyOrGetAvailablePayoutAmountCustomAttributes.from_calculate_maximum_payout_turn_amount_custom_attributes(
        CalculateMaximumPayoutTurnAmountCustomAttributes(
            tier=new_tier, payout_turn=next_tier_payout_turn), fincoin_balance=next_tier_fincoin_balance, )
    try:
        new_payout_balance_available = verify_or_get_available_payout_amount(
            user,
            membership=membership,
            custom_attributes=custom_attributes,
        )
    except BlidzException:
        new_payout_balance_available = 0
    log_data["new_payout_balance_available"] = new_payout_balance_available

    payout_balance_available_to_add = max(new_payout_balance_available - current_payout_balance_available, 0)
    fincoin_balance_available_to_add = FincoinExchangeRate.to_fincoin(
        payout_balance_available_to_add,
        currency,
    ).to_target
    resp.payout_balance_available_to_add = payout_balance_available_to_add
    resp.fincoin_balance_available_to_add = fincoin_balance_available_to_add
    log_data["payout_balance_available_to_add"] = payout_balance_available_to_add
    log_data["fincoin_balance_available_to_add"] = fincoin_balance_available_to_add

    resp.new_tier_payout_turn = to_dto_payout_turn_data(user, new_tier)
    resp.new_tier_payout_turn.payout_turn_amount = next_tier_payout_turn

    logger.info(log_data)
    return api_response_new(content=resp)
