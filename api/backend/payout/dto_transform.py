from datetime import datetime
from typing import Dict, List, Optional, Set, cast

from payout.definitions import (
    DtoLastFailedPayout,
    DtoMilestone,
    DtoPayoutData,
    DtoPayoutLimitData,
    DtoPayoutTurnData,
    DtoSeasonProgressData,
    DtoSpinDataV2,
    DtoSpinDataV3,
    DtoTierPayoutLimit,
)
from pymongo import DESCENDING

from commonlib.exceptions import BlidzException
from commonlib.models.fincoin_wallet import FincoinExchangeRate, FincoinWallet
from commonlib.models.membership import Membership, MembershipPayoutTierLimitDef, Tier
from commonlib.models.payout.payout_transaction import PayoutTransaction
from commonlib.models.payout.payout_turn import PayoutTurn
from commonlib.models.payout.spin_level_config import (
    get_fincoin_target_by_level,
    get_sum_of_all_fincoin_targets,
)
from commonlib.models.payout.spin_progress_config import CompletedMilestone
from commonlib.models.payout.spin_wheel import (
    LineItemAction,
    SpinProgress,
    User<PERSON>pin,
    UserSpinLineItem,
    WithdrawalOfSpinWheel,
)
from commonlib.models.user import User
from commonlib.payout.payout import (
    PayoutCooldown,
    verify_or_get_available_payout_amount,
)
from commonlib.payout.payout_turn_config import (
    get_payout_turn_base_payout_amount,
    get_payout_turn_monthly_payout_cap,
    get_payout_turn_monthly_payout_period,
    get_payout_turn_stack_limit,
)


def to_dto_milestone(m: CompletedMilestone):
    return DtoMilestone(
        percentage=m["percentage"],
        reward_amount=m["reward_amount"],
        reward_fincoin_amount=m["reward_fincoin_amount"],
        milestone_name=m["milestone_name"],
        is_completed=m["is_completed"]
    )


def to_dto_spin_data_v2(
    user: User,
    spin_progress_milestones: Optional[List[CompletedMilestone]] = None,
    user_spin: Optional[UserSpin] = None,
    spin_progress: Optional[SpinProgress] = None,
) -> DtoSpinDataV2:
    ret = DtoSpinDataV2()

    user_id = user.get_id_assert()

    if not user_spin:
        user_spin = UserSpin.by_user_id(user_id)
    if user_spin:
        ret.spin_remaining = user_spin.spin_remaining or 0
        ret.next_free_spin_time = user_spin.next_free_spin_time
        if UserSpinLineItem.find_one({"user_id": user_id, "action": LineItemAction.SPEND}):
            ret.has_used_spin = True

    if not spin_progress:
        sp = SpinProgress.by_user_id(user_id)
    else:
        sp = spin_progress

    if spin_progress_milestones:
        ret.spin_progress_milestones = [to_dto_milestone(m) for m in spin_progress_milestones]

    season = DtoSeasonProgressData()
    season_start, season_end = SpinProgress.season_start_and_end_of_current_ts()

    if sp:
        ret.spin_fincoin_progress = sp.fincoin_progress or 0
        ret.spin_progress = FincoinExchangeRate.to_cent(ret.spin_fincoin_progress).to_target
        ret.spin_level = sp.level or 0
        season.season_start_at = sp.season_start_time or season_start
        season.season_end_at = sp.season_end_time or season_end
        season.total_season_progress, season.total_season_fincoin_progress = sp.total_season_progress()
    else:
        ret.spin_progress = 0
        ret.spin_fincoin_progress = 0
        ret.spin_level = 0
        season.season_start_at = season_start
        season.season_end_at = season_end
        season.total_season_progress, season.total_season_fincoin_progress = 0, 0

    season.name_of_season = datetime.fromtimestamp(season.season_start_at / 1000).strftime("%b. %Y")

    season.total_season_fincoin_target = get_sum_of_all_fincoin_targets()
    season.total_season_target = FincoinExchangeRate.to_cent(season.total_season_fincoin_target).to_target

    ret.season_progress = season

    ret.spin_fincoin_target = get_fincoin_target_by_level(user_id, ret.spin_level)
    ret.spin_target = FincoinExchangeRate.to_cent(ret.spin_fincoin_target).to_target

    return ret


def to_dto_spin_data_v3(
    user: User,
    user_spin: Optional[UserSpin] = None,
) -> DtoSpinDataV3:
    ret = DtoSpinDataV3()

    user_id = user.get_id_assert()

    if not user_spin:
        user_spin = UserSpin.by_user_id(user_id)
    if user_spin:
        ret.spin_remaining = user_spin.spin_remaining or 0
        ret.next_free_spin_time = user_spin.next_free_spin_time
        ret.has_used_spin = bool(UserSpinLineItem.find_one({"user_id": user_id, "action": LineItemAction.SPEND}))

    return ret


def to_dto_payout_turn_data(user: User, tier: Optional[Tier] = None) -> DtoPayoutTurnData:
    ret = DtoPayoutTurnData()

    user_id = user.get_id_assert()

    payout_turn = PayoutTurn.by_user_id(user_id)
    ret.payout_turn_amount = payout_turn.amount if payout_turn else 0
    ret.max_payout_turn_stack = get_payout_turn_stack_limit(tier, user.last_client_version)

    payout_turn_monthly_payout_period = get_payout_turn_monthly_payout_period(user.get_or_create_membership())
    current_amount = PayoutTransaction.get_payout_count_and_amount_in_current_limit_cycle(
        user_id,
        payout_turn_monthly_payout_period.start_js_time,
        payout_turn_monthly_payout_period.end_js_time
    ).total_amount
    ret.monthly_payout_cents = current_amount
    ret.monthly_payout_cap_cents = get_payout_turn_monthly_payout_cap(tier, user.last_client_version)

    ret.base_payout_amount_cents = get_payout_turn_base_payout_amount(user_id, tier)
    return ret


def to_dto_payout_limit_data(user: User) -> DtoPayoutLimitData:
    user_id = user.get_id_assert()
    membership = user.get_or_create_membership()

    limit = membership.get_payout_limit_cycle(user.get_currency())

    current_amount, _ = PayoutTransaction.get_payout_count_and_amount_in_current_limit_cycle(
        user_id,
        limit.limit_cycle_starts_at,
        limit.limit_cycle_ends_at
    )

    ret = DtoPayoutLimitData()

    limit_all: Dict[str, DtoTierPayoutLimit] = {}

    if limit.tier_limits:
        for t in limit.tier_limits:
            li = cast(MembershipPayoutTierLimitDef, limit.tier_limits[t])
            limit_all[t] = DtoTierPayoutLimit(
                money_limit=li["daily_limit_amount"],
                fincoin_limit=li["fincoin_limit_amount"],
                count_limit=li["daily_limit_count"],
            )

    ret.reset_at = limit.limit_cycle_ends_at
    ret.current_cycle_payout_amount = current_amount
    ret.current_cycle_payout_limit_amount_and_count = limit_all
    ret.skip_user_limit_check = False
    return ret


def to_dto_payout_data(
    user: User,
    membership: Membership,
    withdrawal: Optional[WithdrawalOfSpinWheel] = None
) -> DtoPayoutData:
    ret = DtoPayoutData()

    user_id = user.get_id_assert()

    limit = membership.get_payout_limit_cycle(user.get_currency())

    fincoin_wallet = FincoinWallet.by_user_id(user_id)
    fincoin_balance = fincoin_wallet.amount or 0 if fincoin_wallet else 0

    ret.payout_balance = FincoinExchangeRate.to_cent(fincoin_balance, user.get_currency()).to_target
    ret.fincoin_balance = fincoin_balance
    ret.payout_balance_cap = limit.payout_balance_cap
    ret.has_done_normal_payout = False

    try:
        ret.payout_balance_available = verify_or_get_available_payout_amount(
            user,
            membership,
        )
    except BlidzException:
        ret.payout_balance_available = 0

    ret.fincoin_balance_available = FincoinExchangeRate.to_fincoin(
        ret.payout_balance_available,
        user.get_currency(),
    ).to_target

    payout_txns = PayoutTransaction.find(
        {
            "user_id": user_id,
        },
        projection=["extra_offers"],
        sort=[("extra_offers.reason", DESCENDING)],
        batch_size=3
    )
    special_offers: Set[str] = set()
    for txn in payout_txns:
        if not txn.extra_offers:
            ret.has_done_normal_payout = True
            # the payout_txns is ordered by extra_offers descendingly
            # so if it runs to this point, it must have iterated all special payouts
            break

        for offer in txn.extra_offers:
            if not offer.reason:
                continue
            special_offers.add(offer.reason)
    if special_offers:
        ret.special_payouts_sent = list(special_offers)

    ret.payout_limit = to_dto_payout_limit_data(user)

    if not withdrawal:
        withdrawal = WithdrawalOfSpinWheel.by_user_id(user_id)

    if withdrawal:
        ret.total_paid_out_amount = withdrawal.total_withdraw_amount
        if withdrawal.last_failed_payout_before_account_reset:
            ret.last_failed_payout = DtoLastFailedPayout(
                amount=withdrawal.last_failed_payout_before_account_reset.amount,
                failed_at=withdrawal.last_failed_payout_before_account_reset.failed_at
            )
        ret.total_payout_attempt = withdrawal.withdrawal_count

    if not ret.total_paid_out_amount:
        ret.total_paid_out_amount = 0

    ret.payout_turn = to_dto_payout_turn_data(
        user,
        membership.tier if membership.is_active_member(only_active_status=True) else None,
    )

    status = membership.status
    tier = membership.tier
    ret.payout_cooldown_interval = PayoutCooldown.get_remaining_cooldown_milliseconds(user_id, status, tier)
    return ret
