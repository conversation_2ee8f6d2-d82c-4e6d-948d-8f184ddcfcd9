from typing import Dict, List, Optional

from pydantic.v1 import Field, PositiveInt

from commonlib.api_model import (
    ApiModel,
    EmptyRequest,
    EmptyResponse,
    OptionalField,
    Service,
)
from commonlib.models.enums import PayoutOfferTypes
from commonlib.models.payout.payout_defs import PayoutType
from commonlib.paypal_payout import TransactionStatus


class DtoSeasonProgressData(ApiModel):
    name_of_season: Optional[str] = OptionalField("The name of season. It's based on month name for now")
    season_start_at: Optional[int] = OptionalField("Start time of season")
    season_end_at: Optional[int] = OptionalField("End time of season")
    total_season_target: Optional[int] = OptionalField("Total target of season")
    total_season_fincoin_target: Optional[int] = OptionalField("Total fincoin target of season")
    total_season_progress: Optional[int] = OptionalField("Total progress of season")
    total_season_fincoin_progress: Optional[int] = OptionalField("Total fincoin progress of season")


class DtoMilestone(ApiModel):
    percentage: int
    reward_amount: int
    reward_fincoin_amount: int
    milestone_name: str
    is_completed: bool


class DtoSpinDataV2(ApiModel):
    spin_progress: Optional[int] = OptionalField("The progress of current level")
    spin_fincoin_progress: Optional[int] = OptionalField("The fincoin progress of current level")
    spin_target: Optional[int] = OptionalField("The target of current level")
    spin_fincoin_target: Optional[int] = OptionalField("The fincoin target of current level")
    spin_level: Optional[int] = OptionalField("The level of spin progress")
    spin_remaining: Optional[int] = OptionalField("The spins remaining for the user")
    next_free_spin_time: Optional[int] = OptionalField("The timestamp when the next free spin is given.")
    has_used_spin: Optional[bool] = OptionalField("Whether user has used any spin")
    season_progress: Optional[DtoSeasonProgressData] = OptionalField("The season data")
    spin_progress_milestones: Optional[List[DtoMilestone]] = OptionalField("spin progress milestones")


class DtoSpinDataV3(ApiModel):
    spin_remaining: Optional[int] = OptionalField("The spins remaining for the user")
    next_free_spin_time: Optional[int] = OptionalField("The timestamp when the next free spin is given.")
    has_used_spin: Optional[bool] = OptionalField("Whether user has used any spin")


class DtoTierPayoutLimit(ApiModel):
    money_limit: Optional[int] = OptionalField(
        "How much money user can withdrew in current 'limit cycle'. "
        "'Limit cycle' means: "
        "  for v2: non_member/yearly: calendar month, monthly/weekly: subscription period. "
        "  for v2.1, a cycle is one day"
    )
    fincoin_limit: Optional[int] = OptionalField(
        "How much fincoin user can withdrew in current 'limit cycle'. "
        "'Limit cycle' means: "
        "  for v2: non_member/yearly: calendar month, monthly/weekly: subscription period. "
        "  for v2.1, a cycle is one day"
    )
    count_limit: Optional[int] = OptionalField(
        "How many withdrawals user has done in current 'limit cycle'. "
        "'Limit cycle' means: "
        "  for v2: non_member/yearly: calendar month, monthly/weekly: subscription period. "
        "  for v2.1, a cycle is one day"
    )


class DtoPayoutLimitData(ApiModel):
    reset_at: Optional[int] = OptionalField("The reset time of limit. In milliseconds")
    current_cycle_payout_amount: Optional[int] = OptionalField(
        "How much money user has withdrew in current 'limit cycle'. "
        "'Limit cycle' means: a cycle is one day"
    )
    current_cycle_payout_limit_amount_and_count: Optional[Dict[str, DtoTierPayoutLimit]] = OptionalField(
        "How much money user can withdraw at most in current 'limit cycle'. "
        "'Limit cycle' means: a cycle is one day. "
        "key-value is: tier -> limit, including a 'default' to represent non active member"
    )
    skip_user_limit_check: Optional[bool] = OptionalField("Whether to skip the limit check for user for next payout")


class DtoLastFailedPayout(ApiModel):
    amount: Optional[int] = OptionalField("the amount")
    failed_at: Optional[int] = OptionalField("the timestamp when it failed")


class DtoPayoutTurnData(ApiModel):
    payout_turn_amount: Optional[int] = OptionalField("The amount of payout turn")
    max_payout_turn_stack: Optional[int] = OptionalField("The stack of payout turn")
    monthly_payout_cents: Optional[int] = OptionalField("The monthly payout in cents")
    monthly_payout_cap_cents: Optional[int] = OptionalField("The monthly payout cap in cents")
    base_payout_amount_cents: Optional[int] = OptionalField("The base payout amount in cents")


class DtoPayoutData(ApiModel):
    payout_balance: Optional[int] = OptionalField("The current balance which user has")
    fincoin_balance: Optional[int] = OptionalField("The current fincoin balance which user has")
    payout_balance_available: Optional[int] = OptionalField("The available balance which user can payout")
    fincoin_balance_available: Optional[int] = OptionalField("The available fincoin balance which user can payout")
    payout_balance_cap: Optional[int] = OptionalField(
        "The upper limit of payout balance can reach. Note for old users, this cap may be exceeded"
    )
    payout_limit: Optional[DtoPayoutLimitData] = OptionalField("The limit of payout")
    has_done_normal_payout: Optional[bool] = OptionalField("Whether we have done normal payout to user")
    special_payouts_sent: Optional[List[str]] = OptionalField("The list of special payouts we have sent to user")
    total_paid_out_amount: Optional[int] = OptionalField("Total money we paid out to this user")
    last_failed_payout: Optional[DtoLastFailedPayout] = OptionalField("Last failed payout before account reset")
    total_payout_attempt: Optional[int] = OptionalField("Total payout transactions recorded")

    payout_turn: Optional[DtoPayoutTurnData] = OptionalField("The payout turn data")

    payout_cooldown_interval: Optional[int] = OptionalField("The cooldown interval (milliseconds) for payout")


class DtoSpinWheelDataV2(ApiModel):
    numbers: Optional[List[int]] = OptionalField("The numbers on the wheel, including the outcome and surpluses")
    fincoin_numbers: Optional[List[int]] = OptionalField(
        "The fincoin numbers on the wheel, including the outcome and surpluses"
    )
    outcome: Optional[int] = OptionalField("The outcome value")
    fincoin_outcome: Optional[int] = OptionalField("The fincoin outcome value")
    next_outcome: Optional[int] = OptionalField("The next outcome value")
    next_fincoin_outcome: Optional[int] = OptionalField("The next fincoin outcome value")
    outcome_index: Optional[int] = OptionalField("The index (or segment) of the outcome in the numbers list")
    fincoin_outcome_index: Optional[int] = OptionalField(
        "The index (or segment) of the fincoin outcome in the fincoin_numbers list"
    )
    total_cash: Optional[int] = OptionalField("User total cash, including free and paid cash")
    spin: Optional[DtoSpinDataV2] = OptionalField("The spin data")
    payout: Optional[DtoPayoutData] = OptionalField("The payout data")


class DtoSpinWheelDataV3(ApiModel):
    numbers: Optional[List[int]] = OptionalField("The numbers on the wheel, including the outcome and surpluses")
    fincoin_numbers: Optional[List[int]] = OptionalField(
        "The fincoin numbers on the wheel, including the outcome and surpluses"
    )
    outcome: Optional[int] = OptionalField("The outcome value")
    fincoin_outcome: Optional[int] = OptionalField("The fincoin outcome value")
    next_outcome: Optional[int] = OptionalField("The next outcome value")
    next_fincoin_outcome: Optional[int] = OptionalField("The next fincoin outcome value")
    outcome_index: Optional[int] = OptionalField("The index (or segment) of the outcome in the numbers list")
    fincoin_outcome_index: Optional[int] = OptionalField(
        "The index (or segment) of the fincoin outcome in the fincoin_numbers list"
    )
    spin: Optional[DtoSpinDataV3] = OptionalField("The spin data")
    payout: Optional[DtoPayoutData] = OptionalField("The payout data")


class DtoHistoryPayoutData(ApiModel):
    payout_id: str = OptionalField("The payout id")
    payout_time: int = OptionalField("Timestamp when the payout happened.")
    amount: int = OptionalField("Payout amount.")
    transaction_status: Optional[TransactionStatus] = OptionalField("The transaction status.")
    payout_type: Optional[PayoutType] = OptionalField("The payout type.")
    payout_account: Optional[str] = OptionalField("The payout email or PayPal account")


class PayoutHistoryResponse(ApiModel):
    payouts: List[DtoHistoryPayoutData] = OptionalField("List of PayPal payouts.")
    payout: Optional[DtoPayoutData] = OptionalField("The payout data")


class PayoutHistoryRequest(ApiModel):
    page: PositiveInt = Field(default=1, description="Pagination, start from 1")
    size: PositiveInt = Field(default=20, description="Pagination")


class PayoutNormalRequest(ApiModel):
    special_offers: Optional[List[PayoutOfferTypes]] = OptionalField("Special offers to include in the payout")
    payout_amount: Optional[PositiveInt] = OptionalField("Custom amount to payout")
    payout_fincoin: Optional[PositiveInt] = OptionalField("Custom fincoin to payout")
    recaptcha_token: Optional[str] = OptionalField("recaptcha token for assessment")

    payout_type: Optional[PayoutType] = OptionalField("payout type: paypal or gift_card")


class PayoutSpecialOfferRequest(ApiModel):
    special_offers: Optional[List[PayoutOfferTypes]] = OptionalField("Special offers to include in the payout")


class PayoutStatsResp(ApiModel):
    total_paid_payout_amount: Optional[int] = OptionalField("Total payout amount which was paid to users")
    one_day_payout_count: Optional[int] = OptionalField("Total payout count last 24h")


class DtoPayoutSpecialOffer(ApiModel):
    offer_type: Optional[PayoutOfferTypes] = OptionalField("Type of offer")
    amount: Optional[int] = OptionalField("Amount of offer")


class UserPayoutSpecialOfferAvailableResp(ApiModel):
    offers: Optional[List[DtoPayoutSpecialOffer]] = OptionalField("Offers user can get")


class PreviewSubscribeBenefitReq(ApiModel):
    to_lookup_key: str = Field(default=..., description="The target membership lookup key")
    to_trial: Optional[bool] = OptionalField("To preview the benefit of trial membership")


class PreviewSubscribeBenefitResp(ApiModel):
    payout_balance_to_add: Optional[int] = OptionalField("How many payout balance to add")
    fincoin_balance_to_add: Optional[int] = OptionalField("How many fincoin to add")
    action: Optional[str] = OptionalField("The action of jumping level")
    new_tier_payout_turn: Optional[DtoPayoutTurnData] = OptionalField("The payout turn for the new tier")
    payout_balance_available_to_add: Optional[int] = OptionalField("The available payout balance to add")
    fincoin_balance_available_to_add: Optional[int] = OptionalField("The available fincoin to add")


service = Service(
    title="Payout",
    base_url="payout",
    paths=[
        Service.Path(
            "/v2/history",
            description="Returns history for the currently logged-in user.",
            req=PayoutHistoryRequest,
            resp=PayoutHistoryResponse,
        ),
        Service.Path(
            "/v2/normal",
            description="Normal payout which payout amount is deducted from user cash.",
            req=PayoutNormalRequest,
            resp=EmptyResponse,
        ),
        Service.Path(
            "/v2/special_offers",
            description="Special offers payout which payout amount is not deducted from user cash",
            req=PayoutSpecialOfferRequest,
            resp=EmptyResponse,
        ),
        Service.Path(
            "/v2/stat",
            description="Payout information",
            req=EmptyRequest,
            resp=PayoutStatsResp,
        ),
        Service.Path(
            "/v2/current_offers",
            description="Payout offers the user can get",
            req=EmptyRequest,
            resp=UserPayoutSpecialOfferAvailableResp,
        ),
        Service.Path(
            "/v2/preview_jump_level",
            description="Preview the payout balance to add for subscribe membership",
            req=PreviewSubscribeBenefitReq,
            resp=PreviewSubscribeBenefitResp,
        ),
        Service.Path(
            "/v2/preview_subscribe_benefit",
            description="Preview the payout balance to add for subscribe membership",
            req=PreviewSubscribeBenefitReq,
            resp=PreviewSubscribeBenefitResp,
        ),
    ],
)
