import importlib
import json
from pathlib import Path
from typing import Any, Dict, List

from django.core.management.base import CommandParser

from backend.command_utils import BlidzCommand
from commonlib.api_model import Service, generate_schemas

definition_modules = [
    ##################################
    # Write it in alphabetical order
    ##################################
    "ab_test.definitions",
    "ai.definitions",
    "api.definitions",
    "bank.definitions",
    "checkin_calendar.definitions",
    "checkout.checkout_definitions",
    "crawler.definitions",
    "dashboard.definitions",
    "deal.definitions",
    "duo_deal_auto_completion.definitions",
    "exchange.definitions",
    "fiat_wallet.definitions",
    "fincash_wallet.definitions",
    "fincoin_wallet.definitions",
    "games.definitions",
    "gift_drop.definitions",
    "marketplace.definitions",
    "membership.definitions",
    "mini_game.definitions",
    "mission_system.definitions",
    "onboarding.definitions",
    "order.definitions",
    "payment.definitions",
    "payment.stripe_definitions",
    "payout.definitions",
    "payout.spin_definitions",
    "product_review.definitions",
    "product.product_like_definitions",
    "pubsub_algo.definitions",
    "revenue_cat.definitions",
    "shopping_cart.definitions",
    "system.definitions",
    "social_circles.definitions",
    "social_circles.feed_definitions",
    "social_circles.referral_definitions",
    "user.definitions",
    "user_upload_video.definitions",
    "watch_feed.definitions",

]


class Command(BlidzCommand):
    help = "Generate API schema based on the definitions of requests and responses"

    def add_arguments(self, parser: CommandParser):
        pass

    @staticmethod
    def _format_path_url(base_url: str, path_url: str):
        if not path_url.startswith("/"):
            full_url = f"{base_url}/{path_url}"
        else:
            full_url = f"{base_url}{path_url}"

        if not full_url.startswith("/"):
            full_url = f"/{full_url}"

        return full_url

    @staticmethod
    def _format_parameters(path: Service.Path) -> list:
        parameters = [
            {"$ref": "#/components/schemas/device-id"},
            {"$ref": "#/components/schemas/x-blidz-client-version"},
            {"$ref": "#/components/schemas/sessionid"},
        ]
        if path.method.lower() != "get":
            # Get requests won't need the CSRF token.
            parameters.append({"$ref": "#/components/schemas/csrftoken"})
            parameters.append({"$ref": "#/components/schemas/X-CSRFToken"})

        if path.parameters:
            parameters.extend([p.to_dict() for p in path.parameters])
        return parameters

    @staticmethod
    def _format_responses(path: Service.Path, schema_namespace: str) -> dict:
        responses = {
            "200": {
                "description": "OK",
                "content": {
                    "application/json": {
                        "schema": {
                            "type": "object",
                            "properties": {
                                "status": {
                                    "type": "integer",
                                    "description": "The HTTP status",
                                },
                                "type": {
                                    "type": "string",
                                    "description": "Whether it's success or error",
                                    "enum": ["success", "error"],
                                },
                                "content": {
                                    # hope there aren't any responses with same name
                                    "$ref": f"#/components/schemas/{path.resp.__name__}"
                                },
                            },
                        }
                    }
                },
            },
        }

        if path.responses:
            for response in path.responses:
                responses[response.status_code] = response.to_dict()

        return responses

    def _format_path(self, path: Service.Path, schema_namespace: str):
        method_obj: Dict[str, Any] = {
            "description": path.description,
            "parameters": self._format_parameters(path),
        }
        if path.method.lower() == "post":
            method_obj["requestBody"] = {
                "required": True,
                "content": {
                    "application/json": {
                        "schema": {
                            # hope there aren't any requests with the same name
                            "$ref": f"#/components/schemas/{path.req.__name__}"
                        }
                    }
                },
            }

        method_obj["responses"] = self._format_responses(path, schema_namespace)

        return {
            path.method: method_obj
        }

    def _collect_models(self, service: Service):
        models = []
        for path in service.paths:
            models.append(path.req)
            models.append(path.resp)

        return models

    @staticmethod
    def _get_common_schemas():
        """Get common schema components used across all APIs"""
        return {
            "Error": {
                "title": "error",
                "type": "object",
                "properties": {
                    "status": {
                        "description": "The status of the response.",
                        "type": "string",
                    },
                    "type": {
                        "description": "The response type. Error in this case.",
                        "type": "string",
                    },
                    "error": {
                        "description": "The error message.",
                        "type": "string",
                    },
                },
            },
            "device-id": {
                "name": "device-id",
                "in": "header",
                "description": "device id",
                "schema": {"type": "string", "default": "{{device-id}}"},
            },
            "x-blidz-client-version": {
                "name": "x-blidz-client-version",
                "in": "header",
                "description": "client version",
                "schema": {
                    "type": "string",
                    "default": "{{x-blidz-client-version}}",
                },
            },
            "X-CSRFToken": {
                "name": "X-CSRFToken",
                "in": "header",
                "description": "CSRF token in header",
                "schema": {"type": "string", "default": "{{X-CSRFToken}}"},
            },
            "sessionid": {
                "name": "sessionid",
                "in": "cookie",
                "description": "session id in cookie",
                "schema": {"type": "string", "default": "{{sessionid}}"},
            },
            "csrftoken": {
                "name": "csrftoken",
                "in": "cookie",
                "description": "CSRF token in cookie",
                "schema": {"type": "string", "default": "{{csrftoken}}"},
            },
        }

    def _generate_module_schema(self, module_name: str, service: Service):
        """Generate schema for a single module"""
        models = self._collect_models(service)
        model_schemas = generate_schemas(models, ref_prefix="#/components/schemas/")

        schema_namespace = service.base_url.replace("/", "_")
        paths = {
            self._format_path_url(service.base_url, p.url): self._format_path(p, schema_namespace)
            for p in service.paths
        }

        return {
            "openapi": "3.1.0",
            "info": {
                "title": f"Blidz {module_name.replace('.definitions', '').replace('.', ' ').title()} API",
                "version": "1.0"
            },
            "servers": [
                {"url": "{{host}}"},
            ],
            "paths": paths,
            "components": {
                "schemas": {
                    **model_schemas,
                    **self._get_common_schemas(),
                }
            },
        }

    def _generate_separate_files(self):
        """Generate separate schema files for each module"""
        api_schemas_dir = Path(__file__).parent.parent.parent.parent / "api_schemas"
        api_schemas_dir.mkdir(exist_ok=True)

        generated_files = []

        for module_name in definition_modules:
            try:
                module = importlib.import_module(module_name)
                service: Service = module.service
            except Exception as e:
                print(f"Warning: Could not import module {module_name}: {e}")
                continue

            # Generate module-specific schema
            module_schema = self._generate_module_schema(module_name, service)

            # Create filename from module name
            # e.g., "user.definitions" -> "user.json"
            # e.g., "payment.stripe_definitions" -> "payment_stripe.json"
            filename = module_name.replace(".", "_").replace("_definitions", "") + ".json"
            file_path = api_schemas_dir / filename

            with open(file_path, "w") as f:
                json.dump(module_schema, f, indent=4)
                f.write("\n")

            generated_files.append(filename)
            print(f"Generated: {filename}")

        return generated_files

    def _generate_combined_file(self):
        """Generate the combined schema file (original functionality)"""
        all_models: List[Any] = []
        all_paths: Dict[str, Any] = {}

        for module in definition_modules:
            try:
                module = importlib.import_module(module)
                service: Service = module.service
            except Exception:
                continue

            models = self._collect_models(service)
            all_models += models

        all_model_schemas = generate_schemas(all_models, ref_prefix="#/components/schemas/")

        for module in definition_modules:
            try:
                module = importlib.import_module(module)
                service: Service = module.service
            except Exception:
                continue
            schema_namespace = service.base_url.replace("/", "_")
            all_paths.update(
                {
                    self._format_path_url(service.base_url, p.url): self._format_path(p, schema_namespace)
                    for p in service.paths
                }
            )

        content_obj = {
            "openapi": "3.1.0",
            "info": {"title": "Blidz APIs", "version": "1.0"},
            "servers": [
                {"url": "{{host}}"},
            ],
            "paths": all_paths,
            "components": {
                "schemas": {
                    **all_model_schemas,
                    **self._get_common_schemas(),
                }
            },
        }

        content_str = json.dumps(content_obj, indent=4)

        file_path = Path(__file__).parent.parent.parent.parent / "api_schemas" / "blidz_apis.json"
        with open(file_path, "w") as f:
            f.write(content_str)
            f.write("\n")

        print("Generated: blidz_apis.json")
        return content_str

    def handle(self, *args: Any, **options: Any):
        # Ensure api_schemas directory exists
        api_schemas_dir = Path(__file__).parent.parent.parent.parent / "api_schemas"
        api_schemas_dir.mkdir(exist_ok=True)

        print("Generating combined schema file...")
        self._generate_combined_file()

        print("\nGenerating separate module schema files...")
        self._generate_separate_files()
