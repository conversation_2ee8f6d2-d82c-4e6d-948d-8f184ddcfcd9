{"openapi": "3.1.0", "info": {"title": "Blidz Payout API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/payout/v2/history": {"post": {"description": "Returns history for the currently logged-in user.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayoutHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PayoutHistoryResponse"}}}}}}}}}, "/payout/v2/normal": {"post": {"description": "Normal payout which payout amount is deducted from user cash.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayoutNormalRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/payout/v2/special_offers": {"post": {"description": "Special offers payout which payout amount is not deducted from user cash", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayoutSpecialOfferRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/payout/v2/stat": {"post": {"description": "Payout information", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PayoutStatsResp"}}}}}}}}}, "/payout/v2/current_offers": {"post": {"description": "Payout offers the user can get", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/UserPayoutSpecialOfferAvailableResp"}}}}}}}}}, "/payout/v2/preview_jump_level": {"post": {"description": "Preview the payout balance to add for subscribe membership", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PreviewSubscribeBenefitReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PreviewSubscribeBenefitResp"}}}}}}}}}, "/payout/v2/preview_subscribe_benefit": {"post": {"description": "Preview the payout balance to add for subscribe membership", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PreviewSubscribeBenefitReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PreviewSubscribeBenefitResp"}}}}}}}}}}, "components": {"schemas": {"PayoutHistoryRequest": {"title": "PayoutHistoryRequest", "type": "object", "properties": {"page": {"description": "Pagination, start from 1", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "size": {"description": "Pagination", "default": 20, "exclusiveMinimum": 0, "type": "integer"}}}, "TransactionStatus": {"title": "TransactionStatus", "description": "An enumeration.", "enum": ["PENDING", "WAITING_MANUAL_PROCESS", "MANUAL_PROCESSING", "UNCLAIMED", "ONHOLD", "BLOCKED", "RETURNED", "SUCCESS", "FAILED", "REFUNDED", "REVERSED", "CANCELLED"], "type": "string"}, "PayoutType": {"title": "PayoutType", "description": "An enumeration.", "enum": ["paypal", "gift_card"], "type": "string"}, "DtoHistoryPayoutData": {"title": "DtoHistoryPayoutData", "type": "object", "properties": {"payoutId": {"description": "The payout id", "type": "string"}, "payoutTime": {"description": "Timestamp when the payout happened.", "type": "integer"}, "amount": {"description": "Payout amount.", "type": "integer"}, "transactionStatus": {"description": "The transaction status.", "allOf": [{"$ref": "#/components/schemas/TransactionStatus"}]}, "payoutType": {"description": "The payout type.", "allOf": [{"$ref": "#/components/schemas/PayoutType"}]}, "payoutAccount": {"description": "The payout email or PayPal account", "type": "string"}}}, "DtoTierPayoutLimit": {"title": "DtoTierPayoutLimit", "type": "object", "properties": {"moneyLimit": {"description": "How much money user can withdrew in current 'limit cycle'. 'Limit cycle' means:   for v2: non_member/yearly: calendar month, monthly/weekly: subscription period.   for v2.1, a cycle is one day", "type": "integer"}, "fincoinLimit": {"description": "How much fincoin user can withdrew in current 'limit cycle'. 'Limit cycle' means:   for v2: non_member/yearly: calendar month, monthly/weekly: subscription period.   for v2.1, a cycle is one day", "type": "integer"}, "countLimit": {"description": "How many withdrawals user has done in current 'limit cycle'. 'Limit cycle' means:   for v2: non_member/yearly: calendar month, monthly/weekly: subscription period.   for v2.1, a cycle is one day", "type": "integer"}}}, "DtoPayoutLimitData": {"title": "DtoPayoutLimitData", "type": "object", "properties": {"resetAt": {"description": "The reset time of limit. In milliseconds", "type": "integer"}, "currentCyclePayoutAmount": {"description": "How much money user has withdrew in current 'limit cycle'. 'Limit cycle' means: a cycle is one day", "type": "integer"}, "currentCyclePayoutLimitAmountAndCount": {"description": "How much money user can withdraw at most in current 'limit cycle'. 'Limit cycle' means: a cycle is one day. key-value is: tier -> limit, including a 'default' to represent non active member", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoTierPayoutLimit"}}, "skipUserLimitCheck": {"description": "Whether to skip the limit check for user for next payout", "type": "boolean"}}}, "DtoLastFailedPayout": {"title": "DtoLastFailedPayout", "type": "object", "properties": {"amount": {"description": "the amount", "type": "integer"}, "failedAt": {"description": "the timestamp when it failed", "type": "integer"}}}, "DtoPayoutTurnData": {"title": "DtoPayoutTurnData", "type": "object", "properties": {"payoutTurnAmount": {"description": "The amount of payout turn", "type": "integer"}, "maxPayoutTurnStack": {"description": "The stack of payout turn", "type": "integer"}, "monthlyPayoutCents": {"description": "The monthly payout in cents", "type": "integer"}, "monthlyPayoutCapCents": {"description": "The monthly payout cap in cents", "type": "integer"}, "basePayoutAmountCents": {"description": "The base payout amount in cents", "type": "integer"}}}, "DtoPayoutData": {"title": "DtoPayoutData", "type": "object", "properties": {"payoutBalance": {"description": "The current balance which user has", "type": "integer"}, "fincoinBalance": {"description": "The current fincoin balance which user has", "type": "integer"}, "payoutBalanceAvailable": {"description": "The available balance which user can payout", "type": "integer"}, "fincoinBalanceAvailable": {"description": "The available fincoin balance which user can payout", "type": "integer"}, "payoutBalanceCap": {"description": "The upper limit of payout balance can reach. Note for old users, this cap may be exceeded", "type": "integer"}, "payoutLimit": {"description": "The limit of payout", "allOf": [{"$ref": "#/components/schemas/DtoPayoutLimitData"}]}, "hasDoneNormalPayout": {"description": "Whether we have done normal payout to user", "type": "boolean"}, "specialPayoutsSent": {"description": "The list of special payouts we have sent to user", "type": "array", "items": {"type": "string"}}, "totalPaidOutAmount": {"description": "Total money we paid out to this user", "type": "integer"}, "lastFailedPayout": {"description": "Last failed payout before account reset", "allOf": [{"$ref": "#/components/schemas/DtoLastFailedPayout"}]}, "totalPayoutAttempt": {"description": "Total payout transactions recorded", "type": "integer"}, "payoutTurn": {"description": "The payout turn data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutTurnData"}]}, "payoutCooldownInterval": {"description": "The cooldown interval (milliseconds) for payout", "type": "integer"}}}, "PayoutHistoryResponse": {"title": "PayoutHistoryResponse", "type": "object", "properties": {"payouts": {"description": "List of PayPal payouts.", "type": "array", "items": {"$ref": "#/components/schemas/DtoHistoryPayoutData"}}, "payout": {"description": "The payout data", "allOf": [{"$ref": "#/components/schemas/DtoPayoutData"}]}}}, "PayoutOfferTypes": {"title": "PayoutOfferTypes", "description": "An enumeration.", "enum": ["payout_free", "spin_leaderboard", "membership_auth_completed"], "type": "string"}, "PayoutNormalRequest": {"title": "PayoutNormalRequest", "type": "object", "properties": {"specialOffers": {"description": "Special offers to include in the payout", "type": "array", "items": {"$ref": "#/components/schemas/PayoutOfferTypes"}}, "payoutAmount": {"description": "Custom amount to payout", "exclusiveMinimum": 0, "type": "integer"}, "payoutFincoin": {"description": "Custom fincoin to payout", "exclusiveMinimum": 0, "type": "integer"}, "recaptchaToken": {"description": "recaptcha token for assessment", "type": "string"}, "payoutType": {"description": "payout type: paypal or gift_card", "allOf": [{"$ref": "#/components/schemas/PayoutType"}]}}}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "PayoutSpecialOfferRequest": {"title": "PayoutSpecialOfferRequest", "type": "object", "properties": {"specialOffers": {"description": "Special offers to include in the payout", "type": "array", "items": {"$ref": "#/components/schemas/PayoutOfferTypes"}}}}, "EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "PayoutStatsResp": {"title": "PayoutStatsResp", "type": "object", "properties": {"totalPaidPayoutAmount": {"description": "Total payout amount which was paid to users", "type": "integer"}, "oneDayPayoutCount": {"description": "Total payout count last 24h", "type": "integer"}}}, "DtoPayoutSpecialOffer": {"title": "DtoPayoutSpecialOffer", "type": "object", "properties": {"offerType": {"description": "Type of offer", "allOf": [{"$ref": "#/components/schemas/PayoutOfferTypes"}]}, "amount": {"description": "Amount of offer", "type": "integer"}}}, "UserPayoutSpecialOfferAvailableResp": {"title": "UserPayoutSpecialOfferAvailableResp", "type": "object", "properties": {"offers": {"description": "Offers user can get", "type": "array", "items": {"$ref": "#/components/schemas/DtoPayoutSpecialOffer"}}}}, "PreviewSubscribeBenefitReq": {"title": "PreviewSubscribeBenefitReq", "type": "object", "properties": {"toLookupKey": {"description": "The target membership lookup key", "type": "string"}, "toTrial": {"description": "To preview the benefit of trial membership", "type": "boolean"}}, "required": ["toLookup<PERSON>ey"]}, "PreviewSubscribeBenefitResp": {"title": "PreviewSubscribeBenefitResp", "type": "object", "properties": {"payoutBalanceToAdd": {"description": "How many payout balance to add", "type": "integer"}, "fincoinBalanceToAdd": {"description": "How many fincoin to add", "type": "integer"}, "action": {"description": "The action of jumping level", "type": "string"}, "newTierPayoutTurn": {"description": "The payout turn for the new tier", "allOf": [{"$ref": "#/components/schemas/DtoPayoutTurnData"}]}, "payoutBalanceAvailableToAdd": {"description": "The available payout balance to add", "type": "integer"}, "fincoinBalanceAvailableToAdd": {"description": "The available fincoin to add", "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}