{"openapi": "3.1.0", "info": {"title": "Blidz Social_Circles Feed_Definitions API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/social_circles/feed/list": {"post": {"description": "To get user own social feed by paging.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeedListRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/FeedListResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/feed/{post_id}/like": {"post": {"description": "Like & unlike a post.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}, {"in": "path", "name": "post_id", "required": true, "schema": {"type": "string"}, "description": "The post ID."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostLikeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PostLikeResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/feed/{post_id}/reshare": {"post": {"description": "Reshare a post.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}, {"in": "path", "name": "post_id", "required": true, "schema": {"type": "string"}, "description": "The post ID."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostReshareRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/PostReshareResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "422": {"description": "User can't reshare own posts.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/feed/{post_id}/claim_friend_reward": {"post": {"description": "Claim a friend reward from a given social feed post.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}, {"in": "path", "name": "post_id", "required": true, "schema": {"type": "string"}, "description": "The post ID."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimFriendRewardReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimFriendRewardResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "422": {"description": "If the user cannot claim the reward, e.g. because they're the host, completed or claimed user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"FeedListRequest": {"title": "FeedListRequest", "type": "object", "properties": {"page": {"description": "Paging page number.", "default": 1, "minimum": 1, "type": "integer"}, "pageSize": {"description": "Paging size number", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}, "userId": {"description": "Get only post of this user id", "type": "string"}, "channel": {"description": "Algo channel defined by client", "type": "string"}, "includeGlobal": {"description": "Include global posts.", "default": true, "type": "boolean"}}}, "DtoPostTypes": {"title": "DtoPostTypes", "description": "An enumeration.", "enum": ["post_product_like", "post_duo_deal_completion", "post_sign_up_own", "post_referral_success_own", "post_active_duo_deal_own", "post_popular_product", "post_play_treasure_hunt_own"], "type": "string"}, "SimpleUserData": {"title": "SimpleUserData", "description": "Base model for user data.", "type": "object", "properties": {"userId": {"title": "Userid", "description": "The user's ID.", "type": "string"}, "avatarUrl": {"title": "Avatarurl", "description": "The user's avatar <PERSON><PERSON>.", "type": "string"}, "firstName": {"title": "Firstname", "description": "The user's first name.", "type": "string"}, "phoneNumber": {"title": "Phonenumber", "description": "The user's phone number.", "type": "string"}, "membershipStatus": {"title": "Membershipstatus", "description": "The user's membership status.", "type": "string"}}}, "DtoPostProductData": {"title": "DtoPostProductData", "type": "object", "properties": {"productId": {"description": "The product id of post when post type associated with product like", "type": "string"}, "productName": {"description": "The product name of post when post type associated with product like", "type": "string"}, "productLikeCount": {"description": "The product name of post when post type associated with product like. The FE needs to add this count and post_liked_count to show", "type": "integer"}, "deal": {"description": "The deal data.", "type": "object"}, "recentUserBought": {"description": "User info on latest user who bought the product.", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}, "boughtCount": {"description": "Count of purchases of this product.", "type": "integer"}}}, "PriceComponent": {"title": "PriceComponent", "type": "object", "properties": {"non_refundable_fee": {"title": "Non Refundable Fee", "description": "Non refundable fee.", "type": "integer"}, "total_default_shipping": {"title": "Total Default Shipping", "description": "Total default shipping.", "type": "integer"}, "blidz_cash_discount": {"title": "<PERSON><PERSON><PERSON> Cash Discount", "description": "<PERSON><PERSON><PERSON> Cash Discount.", "type": "integer"}, "additive_fee": {"title": "Additive Fee", "description": "Additive fee.", "type": "integer"}, "req_id": {"title": "Req <PERSON>d", "description": "The request Id", "type": "string"}}}, "PriceComponents": {"title": "PriceComponents", "description": "Class for storing the total JIP and JIN prices for a story.", "type": "object", "properties": {"joinInternalPaying": {"title": "Joininternalpaying", "description": "The JIP price components.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}, "joinInternalNonpaying": {"title": "Joininternalnonpaying", "description": "The JIN price components.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}}}, "RewardType": {"title": "RewardType", "description": "An enumeration.", "enum": ["coin", "cash", "mystery_box", "key", "spin", "game_turn"], "type": "string"}, "RewardStatus": {"title": "RewardStatus", "description": "An enumeration.", "enum": ["available", "claimed"], "type": "string"}, "FriendReward": {"title": "Friend<PERSON>eward", "type": "object", "properties": {"rewardType": {"description": "The reward type", "allOf": [{"$ref": "#/components/schemas/RewardType"}]}, "rewardCount": {"title": "Rewardcount", "description": "The amount of rewards to give. E.g. count = 2 means that we give out 2 rewards at once.", "type": "integer"}, "rewardLimit": {"title": "Rewardlimit", "description": "Total amount of rewards available.", "type": "integer"}, "claimedCount": {"title": "Claimedcount", "description": "The amount of claimed rewards.", "default": 0, "type": "integer"}, "status": {"description": "The reward status, claimed or available.", "default": "available", "allOf": [{"$ref": "#/components/schemas/RewardStatus"}]}, "claimedBy": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "The user information on which users claimed the reward.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/SimpleUserData"}}, "excludedUsers": {"title": "Excludedusers", "description": "List of user ids to be excluded from getting the reward, e.g. the host user and the completing user.", "default": [], "type": "array", "items": {"type": "string"}}}, "required": ["rewardType", "rewardCount", "rewardLimit"]}, "DtoPostDuoDeal": {"title": "DtoPostDuoDeal", "type": "object", "properties": {"duoDealEntryId": {"description": "The duo deal entry id of post when post type associated with duo deal", "type": "string"}, "deal": {"description": "The deal data.", "type": "object"}, "expiresAt": {"description": "The duo deal expires time of post when post type associated with duo deal", "type": "integer"}, "priceComponents": {"description": "The price components for JIN and JIP for the deal.", "allOf": [{"$ref": "#/components/schemas/PriceComponents"}]}, "priceComponent": {"description": "The price component JIP or JIN for the deal.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}, "recentUserBought": {"description": "User info on latest user who bought the product.", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}, "boughtCount": {"description": "Count of purchases of this product.", "type": "integer"}, "friendReward": {"description": "The friend reward connected to this duo deal post.", "allOf": [{"$ref": "#/components/schemas/FriendReward"}]}}}, "DtoPostSignUpOwn": {"title": "DtoPostSignUpOwn", "type": "object", "properties": {"userId": {"description": "User's id", "type": "string"}}}, "DtoPostReferralSuccessOwn": {"title": "DtoPostReferralSuccessOwn", "type": "object", "properties": {"inviter": {"description": "The user data who's the inviter of referral relationship", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}, "invited": {"description": "The user data who's the invited of referral relationship", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}}}, "DtoPlayTreasureHunt": {"title": "DtoPlayTreasureHunt", "type": "object", "properties": {"dealId": {"description": "Deal's id of treasure hunt", "type": "string"}, "racersCount": {"description": "Number of racers in treasure hunt", "type": "integer"}, "recentRacers": {"description": "Number of racers in treasure hunt", "type": "array", "items": {"$ref": "#/components/schemas/SimpleUserData"}}}}, "DtoPostInfo": {"title": "DtoPostInfo", "type": "object", "properties": {"id": {"description": "Post unique ID", "type": "string"}, "type": {"description": "The type of trigger post", "allOf": [{"$ref": "#/components/schemas/DtoPostTypes"}]}, "owner": {"description": "The user data who's the owner of the post", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}, "imageUrl": {"description": "The image url of post", "type": "string"}, "description": {"description": "The description of post", "type": "string"}, "likedCount": {"description": "The liked count of post", "type": "integer"}, "likedByMe": {"description": "The user liked this post", "type": "boolean"}, "createdAt": {"description": "The created time of post.", "type": "integer"}, "resharedBy": {"description": "The user data who reshared this post", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}, "myPost": {"description": "The tag of post which is requiring user's", "type": "boolean"}, "productLike": {"$ref": "#/components/schemas/DtoPostProductData"}, "duoDealCompletion": {"$ref": "#/components/schemas/DtoPostDuoDeal"}, "signUpOwn": {"$ref": "#/components/schemas/DtoPostSignUpOwn"}, "referralSuccessOwn": {"$ref": "#/components/schemas/DtoPostReferralSuccessOwn"}, "activeDuoDealOwn": {"$ref": "#/components/schemas/DtoPostDuoDeal"}, "popularProduct": {"$ref": "#/components/schemas/DtoPostProductData"}, "playTreasureHuntOwn": {"$ref": "#/components/schemas/DtoPlayTreasureHunt"}}}, "DtoPriceBreakdownForDuoDeal": {"title": "DtoPriceBreakdownForDuoDeal", "type": "object", "properties": {"itemPrice": {"description": "The item price.", "type": "integer"}, "shippingFee": {"description": "The shipping fee.", "type": "integer"}, "processingFee": {"description": "The processing fee.", "type": "integer"}, "discount": {"description": "The discount.", "type": "integer"}, "fincoinDiscountAmount": {"description": "The fincoin amount to be used.", "type": "integer"}, "fincoinDiscountMonetaryValue": {"description": "The monetary value of fincoin in cents.", "type": "integer"}, "finalPrice": {"description": "The final price.", "type": "integer"}, "strikethroughPrice": {"description": "The strikethrough price.", "type": "integer"}}}, "DtoPriceBreakdownForFlashSale": {"title": "DtoPriceBreakdownForFlashSale", "type": "object", "properties": {"fincoinAmount": {"description": "The fincoin amount to be used.", "type": "integer"}, "fincoinMonetaryValue": {"description": "The monetary value of fincoin in cents.", "type": "integer"}, "shippingFee": {"description": "The shipping fee.", "type": "integer"}, "strikethroughPrice": {"description": "The strikethrough price.", "type": "integer"}, "finalPrice": {"description": "The final price paid in money. Only include shipping fee.", "type": "integer"}}}, "DtoPriceBreakdownForTreasureHunt": {"title": "DtoPriceBreakdownForTreasureHunt", "description": "Price breakdown for treasure hunt products", "type": "object", "properties": {"strikethroughPrice": {"description": "The strikethrough price in cents.", "type": "integer"}}}, "DtoPricesOfBuyingTypes": {"title": "DtoPricesOfBuyingTypes", "type": "object", "properties": {"teamBuy": {"description": "The team buy price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "join": {"description": "The join price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "buyAlone": {"description": "The buy alone price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "flashSale": {"description": "The flash sale price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForFlashSale"}]}, "treasureHunt": {"description": "The treasure hunt price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForTreasureHunt"}]}}}, "FeedListResponse": {"title": "FeedListResponse", "type": "object", "properties": {"list": {"description": "Post list.", "type": "array", "items": {"$ref": "#/components/schemas/DtoPostInfo"}}, "page": {"description": "Paging page number.", "type": "integer"}, "size": {"description": "Paging size number", "type": "integer"}, "priceBreakdown": {"description": "The price breakdown for the deals in the story list.", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}}, "PostLikeRequest": {"title": "PostLikeRequest", "type": "object", "properties": {}}, "PostLikeResponse": {"title": "PostLikeResponse", "type": "object", "properties": {}}, "PostReshareRequest": {"title": "PostReshareRequest", "type": "object", "properties": {}}, "PostReshareResponse": {"title": "PostReshareResponse", "type": "object", "properties": {}}, "ClaimFriendRewardReq": {"title": "ClaimFriendRewardReq", "type": "object", "properties": {"reqId": {"description": "The request Id", "type": "string"}, "channel": {"description": "Algo channel defined by client", "type": "string"}}}, "ClaimFriendRewardResponse": {"title": "ClaimFriendRewardResponse", "type": "object", "properties": {"data": {"description": "The post info.", "allOf": [{"$ref": "#/components/schemas/DtoPostInfo"}]}, "priceBreakdown": {"description": "The price breakdown for the skus in the post.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}