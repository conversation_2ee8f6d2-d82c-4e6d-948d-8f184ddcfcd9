{"openapi": "3.1.0", "info": {"title": "Blidz Social_Circles Referral_Definitions API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/social_circles/referral/earnings": {"post": {"description": "To get user own earning information by referral system.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReferralEarnsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ReferralEarnsResponse"}}}}}}}}}}, "components": {"schemas": {"ReferralEarnsRequest": {"title": "ReferralEarnsRequest", "type": "object", "properties": {"page": {"description": "Paging page number.", "default": 1, "minimum": 1, "type": "integer"}, "pageSize": {"description": "Paging size number", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}}}, "DtoEarningType": {"title": "DtoEarningType", "description": "An enumeration.", "enum": ["cash", "coin", "key", "spin", "payout_balance"], "type": "string"}, "DtoTotalEarning": {"title": "DtoTotalEarning", "type": "object", "properties": {"type": {"description": "Enum: cash, coin.", "allOf": [{"$ref": "#/components/schemas/DtoEarningType"}]}, "amount": {"description": "Cents", "type": "integer"}}}, "DtoActivityType": {"title": "DtoActivityType", "description": "An enumeration.", "enum": ["", "sign_up", "first_duo_deal_completion", "first_real_money_purchase", "refer_friends", "login", "view_deals", "checkout_summary", "view_story", "open_mystery_box", "purchase", "add_contact_book", "become_trial_member", "become_active_member"], "type": "string"}, "DtoActivityInfo": {"title": "DtoActivityInfo", "type": "object", "properties": {"userId": {"description": "Id of activity user.", "type": "string"}, "avatarUrl": {"description": "Avatar url of activity user.", "type": "string"}, "type": {"description": "Enum: sign_up, first_duo_deal_completion, first_real_money_purchase, login, view_deals, checkout_summary, view_story, open_mystery_box, purchase.", "allOf": [{"$ref": "#/components/schemas/DtoActivityType"}]}, "title": {"description": "Name of activity action.", "type": "string"}, "time": {"description": "The action happened time.", "type": "integer"}, "earningAmount": {"description": "Referral earned amount.", "type": "integer"}, "earningType": {"description": "Referral earned type. Enum: cash, coin.", "allOf": [{"$ref": "#/components/schemas/DtoEarningType"}]}, "directionType": {"description": "Enum: sign_up, first_duo_deal_completion, first_real_money_purchase, refer_friends. This type help FE to show the button and next action.", "allOf": [{"$ref": "#/components/schemas/DtoActivityType"}]}, "directionDesc": {"description": "Desc of direction actions.", "type": "string"}}}, "ReferralEarnsResponse": {"title": "ReferralEarnsResponse", "type": "object", "properties": {"totalEarnings": {"description": "User total earnings by referral system.", "type": "array", "items": {"$ref": "#/components/schemas/DtoTotalEarning"}}, "activities": {"description": "Referral reward activities list.", "type": "array", "items": {"$ref": "#/components/schemas/DtoActivityInfo"}}, "page": {"description": "Paging page number.", "type": "integer"}, "size": {"description": "Paging size number", "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}