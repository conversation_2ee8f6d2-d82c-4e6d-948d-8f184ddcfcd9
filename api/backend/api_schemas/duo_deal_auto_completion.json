{"openapi": "3.1.0", "info": {"title": "Blidz Duo_Deal_Auto_Completion API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/duo_deal_auto_completion/details": {"post": {"description": "Get user's current duo deal auto completion ticket", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DtoDuoDealAutoCompletionDetailsResp"}}}}}}}}}, "/duo_deal_auto_completion/complete": {"post": {"description": "Complete the duo deal entry by spending the duo deal auto completion ticket", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DtoDuoDealAutoCompletionCompleteReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DtoDuoDealAutoCompletionCompleteResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoDuoDealAutoCompletionDetailsResp": {"title": "DtoDuoDealAutoCompletionDetailsResp", "type": "object", "properties": {"ticket": {"description": "Current duo deal auto completion ticket", "type": "integer"}, "stackLimits": {"description": "Stack limit of the membership lookup key", "type": "object", "additionalProperties": {"type": "integer"}}, "usedTicketInBillingCycle": {"description": "Used ticket in billing cycle", "type": "integer"}, "gotTicketInBillingCycle": {"description": "Got ticket in billing cycle", "type": "integer"}}}, "DtoDuoDealAutoCompletionCompleteReq": {"title": "DtoDuoDealAutoCompletionCompleteReq", "type": "object", "properties": {"duoDealEntryId": {"description": "Duo deal entry id", "type": "string"}, "orderId": {"description": "Order id", "type": "string"}}}, "DtoDuoDealAutoCompletionCompleteResp": {"title": "DtoDuoDealAutoCompletionCompleteResp", "type": "object", "properties": {"ticket": {"description": "Current duo deal auto completion ticket", "type": "integer"}, "orderDetail": {"description": "Order detail", "type": "object"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}