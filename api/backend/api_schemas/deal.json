{"openapi": "3.1.0", "info": {"title": "Blidz Deal API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/deal/deal_suggestions": {"post": {"description": "Returns a client_list_data of deals suggested for the given video title", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealSuggestionRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DealSuggestionResponse"}}}}}}}}}, "/deal/google_categories": {"post": {"description": "Returns a list of Google categories to show in the client", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleCategoryTreeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GoogleCategoryTreeResponse"}}}}}}}}}, "/deal/google_categories_in_dashboard": {"post": {"description": "Get recommended categories for dashboard", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleCategoryTreeInDashboardRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GoogleCategoryTreeResponse"}}}}}}}}}, "/deal/google_categories/list": {"post": {"description": "Returns a list of deals belonging to a specified Google category tree", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealsInGoogleCategoryTreeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DealsInGoogleCategoryTreeResponse"}}}}}}}}}, "/deal/brands/deals": {"post": {"description": "Returns a list of deals for the given brand", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BrandDealRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/BrandDealResponse"}}}}}}}}}, "/deal/category_with_sections": {"post": {"description": "Get the google categories along with several deals in each category", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryWithSectionsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CategoryWithSectionsResponse"}}}}}}}}}, "/deal/bestseller_deals": {"post": {"description": "Get bestseller deals in each tab", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BestsellerDealsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/BestsellerDealsResponse"}}}}}}}}}, "/deal/deal_feed": {"post": {"description": "Get deals in specified feed", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealFeedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DealFeedResponse"}}}}}}}}}, "/deal/search_deals": {"post": {"description": "Search deals", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchDealRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DealFeedResponse"}}}}}}}}}, "/deal/similar_deals": {"post": {"description": "Get similar deals for a given deal", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimilarDealsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DealFeedResponse"}}}}}}}}}, "/deal/deal_data": {"post": {"description": "Get deal data", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealDataRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DealDataResponse"}}}}}}}}}, "/deal/duo_deal_entry_data": {"post": {"description": "Get duo deal entry data", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DuoDealEntryDataRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/DuoDealEntryDataResponse"}}}}}}}}}, "/deal/subsidy_categories": {"post": {"description": "Get subsidy categories", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SubsidyCategoriesResp"}}}}}}}}}}, "components": {"schemas": {"DealSuggestionRequest": {"title": "DealSuggestionRequest", "type": "object", "properties": {"q": {"description": "The product name to search for.", "type": "string"}, "channel": {"description": "Channel defined by client", "type": "string"}}, "required": ["q"]}, "LockCost": {"title": "LockCost", "type": "object", "properties": {"coins": {"title": "Coins", "default": 0, "type": "integer"}}}, "PriceDetail": {"title": "PriceDetail", "type": "object", "properties": {"coins": {"title": "Coins", "type": "integer"}, "price": {"title": "Price", "type": "integer"}, "shipping": {"title": "Shipping", "type": "integer"}, "shippingAsCoins": {"title": "Shippingascoins", "type": "integer"}}, "required": ["coins", "price", "shipping", "shippingAsCoins"]}, "PriceProperty": {"title": "PriceProperty", "type": "object", "properties": {"lockCost": {"$ref": "#/components/schemas/LockCost"}, "restricted": {"title": "Restricted", "type": "boolean"}, "price": {"title": "Price", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceDetail"}}}}, "PriceLevelSet": {"title": "PriceLevelSet", "type": "object", "properties": {"solo": {"$ref": "#/components/schemas/PriceProperty"}, "target": {"$ref": "#/components/schemas/PriceProperty"}, "join": {"$ref": "#/components/schemas/PriceProperty"}, "retail": {"$ref": "#/components/schemas/PriceProperty"}, "sale": {"$ref": "#/components/schemas/PriceProperty"}, "min": {"$ref": "#/components/schemas/PriceProperty"}, "max": {"$ref": "#/components/schemas/PriceProperty"}}}, "DealAttributes": {"title": "DealAttributes", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "optionCount": {"title": "Optioncount", "type": "integer"}}}, "DealClientListData": {"title": "DealClientListData", "type": "object", "properties": {"_id": {"pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "state": {"type": "string"}, "priceLevels": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "array", "items": {}}}}, "lowestPriceInUsdCent": {"type": "integer"}, "reallocatedPrice": {"type": "integer"}, "price": {"type": "integer"}, "fincoinPrice": {"type": "integer"}, "strikethroughPrice": {"type": "integer"}, "_productId": {"type": "string"}, "productName": {"type": "string"}, "image": {"type": "string"}, "rating": {"type": "number"}, "ratingCount": {"type": "integer"}, "reviews": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "retailPrice": {"type": "object", "additionalProperties": {"type": "integer"}}, "duoDeal": {"default": true, "type": "boolean"}, "gamified": {"default": false, "type": "boolean"}, "dealRace": {"default": false, "type": "boolean"}, "quantity": {"default": {}, "type": "object"}, "minLockCost": {"default": 1, "type": "integer"}, "priceCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "coinCostCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "imageKitEnabled": {"default": true, "type": "boolean"}, "lastUpdated": {"type": "integer"}, "duoDealEntries": {"type": "array", "items": {"type": "object"}}, "timerStart": {"type": "integer"}, "timerEnd": {"type": "integer"}, "endTime": {"type": "integer"}, "recentRacers": {"type": "object"}, "amountOfRacers": {"type": "integer"}, "usersWon": {"type": "object"}, "maxRacer": {"type": "integer"}, "leaderboardActivated": {"type": "boolean"}, "productLikes": {"type": "integer"}, "productCost": {"type": "integer"}, "supplierName": {"type": "string"}, "supplierStoreId": {"type": "string"}, "fromProducer": {"type": "string"}, "reqId": {"type": "string"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/DealAttributes"}}, "soldCount": {"type": "integer"}, "data": {"description": "Client List Data as a string.", "type": "string"}, "branded": {"type": "boolean"}, "startTime": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}, "hasAdultContent": {"type": "boolean"}}, "required": ["_id", "state", "_productId", "productName", "type"]}, "DealSuggestionResponse": {"title": "DealSuggestionResponse", "type": "object", "properties": {"deals": {"description": "List of 'inspirational' deals.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "GoogleCategoryTreeRequest": {"title": "GoogleCategoryTreeRequest", "type": "object", "properties": {"parentId": {"description": "The parent id of the category tree to return.", "type": "string"}, "entryPoint": {"description": "The entry point of tab or base64 string of google category.", "type": "string"}, "count": {"description": "The count of categories", "default": 100, "exclusiveMinimum": 0, "type": "integer"}}}, "GoogleCategoryTree": {"title": "GoogleCategoryTree", "type": "object", "properties": {"categoryId": {"description": "The ID of the category.", "type": "string"}, "parentId": {"description": "The parent this category belongs to.", "type": "string"}, "category": {"description": "The category name.", "type": "string"}, "fullCategories": {"description": "The full category tree.", "type": "string"}, "imageUrl": {"description": "The category image url if specified.", "type": "string"}}, "required": ["categoryId"]}, "GoogleCategoryTreeResponse": {"title": "GoogleCategoryTreeResponse", "type": "object", "properties": {"categories": {"description": "The list of categories in the tree.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/GoogleCategoryTree"}}}}, "GoogleCategoryTreeInDashboardRequest": {"title": "GoogleCategoryTreeInDashboardRequest", "type": "object", "properties": {"count": {"description": "The count of categories", "default": 12, "exclusiveMinimum": 0, "type": "integer"}}}, "DealsInGoogleCategoryTreeRequest": {"title": "DealsInGoogleCategoryTreeRequest", "type": "object", "properties": {"categoryId": {"description": "The category id to return deals for.", "type": "string"}, "page": {"description": "Pagination", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "size": {"description": "Pagination", "default": 20, "exclusiveMinimum": 0, "type": "integer"}, "entryPoint": {"description": "The entry point of tab or base64 string of google category.", "type": "string"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["categoryId"]}, "DealsInGoogleCategoryTreeResponse": {"title": "DealsInGoogleCategoryTreeResponse", "type": "object", "properties": {"deals": {"description": "List of deals in given Google Category tree.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}, "total": {"description": "Total count of deal", "type": "integer"}}}, "BrandDealRequest": {"title": "BrandDealRequest", "type": "object", "properties": {"brandName": {"description": "The brand name to search for.", "type": "string"}, "page": {"description": "Pagination", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "size": {"description": "Pagination", "default": 20, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["brandName"]}, "BrandDealResponse": {"title": "BrandDealResponse", "type": "object", "properties": {"deals": {"description": "The deals belonging to the given brand.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "CategoryWithSectionsRequest": {"title": "CategoryWithSectionsRequest", "type": "object", "properties": {"entryPoint": {"description": "The entry point of tab or base64 string of google category.", "type": "string"}, "categoryCount": {"description": "The count of categories", "default": 6, "exclusiveMinimum": 0, "type": "integer"}, "dealCountPerCategory": {"description": "The count of deals per category", "default": 2, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined by client", "type": "string"}}, "required": ["entryPoint"]}, "DtoCategoryWithDeals": {"title": "DtoCategoryWithDeals", "type": "object", "properties": {"category": {"description": "category info", "allOf": [{"$ref": "#/components/schemas/GoogleCategoryTree"}]}, "deals": {"description": "deals info", "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "CategoryWithSectionsResponse": {"title": "CategoryWithSectionsResponse", "type": "object", "properties": {"categoriesWithDeals": {"description": "Categories with deals", "type": "array", "items": {"$ref": "#/components/schemas/DtoCategoryWithDeals"}}}}, "BestsellerDealsRequest": {"title": "BestsellerDealsRequest", "type": "object", "properties": {"entryPoint": {"description": "The entry point of tab or base64 string of google category.", "type": "string"}, "dealCount": {"description": "The count of deals", "default": 6, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["entryPoint"]}, "BestsellerDealsResponse": {"title": "BestsellerDealsResponse", "type": "object", "properties": {"deals": {"description": "The deals of bestseller belonging to tab.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}, "total": {"description": "Total count of deal", "type": "integer"}}}, "EntryPoint": {"title": "EntryPoint", "description": "An enumeration.", "enum": ["popular", "low_value", "branded", "deal_race", "flash_sale", "dealFreeze", "high_discount", "best_seller", "hot_sale", "fast_delivery", "gc_grocery", "watches", "gadgets", "shoes", "fashion", "accessories", "walletsBags", "automotive", "homeDecor", "hobbies", "phoneUpgrades", "sportsOutdoors", "kids", "health", "diy", "adult", "google_category", "category_recommend", "subsidy_deals", "super_deals", "free_deals", "budget_deals"]}, "DealFeedRequest": {"title": "DealFeedRequest", "type": "object", "properties": {"entryPoint": {"description": "The entry point of tab or base64 string of google category.", "anyOf": [{"$ref": "#/components/schemas/EntryPoint"}, {"type": "string"}]}, "page": {"description": "The page of deal feed. Start from 1.", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size of deal feed.", "default": 20, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["entryPoint"]}, "DealFeedResponse": {"title": "DealFeedResponse", "type": "object", "properties": {"deals": {"description": "The deals in specified feed.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}, "total": {"description": "Total count of deal", "type": "integer"}}}, "SearchDealRequest": {"title": "SearchDealRequest", "type": "object", "properties": {"q": {"description": "The query text to search for.", "maxLength": 100, "type": "string"}, "page": {"description": "The page of deal feed. Start from 1.", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size of deal feed.", "default": 20, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["q"]}, "SimilarDealsRequest": {"title": "SimilarDealsRequest", "type": "object", "properties": {"dealId": {"description": "The deal id to get similar deals for.", "type": "string"}, "page": {"description": "The page of similar deals. Start from 1.", "default": 1, "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "The page size of similar deals.", "default": 20, "exclusiveMinimum": 0, "type": "integer"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["dealId"]}, "DtoBuySource": {"title": "DtoBuySource", "description": "An enumeration.", "enum": ["internal", "external", "ad_link"], "type": "string"}, "DealDataRequest": {"title": "DealDataRequest", "type": "object", "properties": {"dealId": {"description": "The deal id to get details for.", "type": "string"}, "buySource": {"description": "The buy source of the deal.", "default": "internal", "allOf": [{"$ref": "#/components/schemas/DtoBuySource"}]}, "channel": {"description": "Algo channel defined in client", "type": "string"}, "reqId": {"description": "The request id.", "type": "string"}}, "required": ["dealId"]}, "DtoPriceBreakdownForDuoDeal": {"title": "DtoPriceBreakdownForDuoDeal", "type": "object", "properties": {"itemPrice": {"description": "The item price.", "type": "integer"}, "shippingFee": {"description": "The shipping fee.", "type": "integer"}, "processingFee": {"description": "The processing fee.", "type": "integer"}, "discount": {"description": "The discount.", "type": "integer"}, "fincoinDiscountAmount": {"description": "The fincoin amount to be used.", "type": "integer"}, "fincoinDiscountMonetaryValue": {"description": "The monetary value of fincoin in cents.", "type": "integer"}, "finalPrice": {"description": "The final price.", "type": "integer"}, "strikethroughPrice": {"description": "The strikethrough price.", "type": "integer"}}}, "DtoPriceBreakdownForFlashSale": {"title": "DtoPriceBreakdownForFlashSale", "type": "object", "properties": {"fincoinAmount": {"description": "The fincoin amount to be used.", "type": "integer"}, "fincoinMonetaryValue": {"description": "The monetary value of fincoin in cents.", "type": "integer"}, "shippingFee": {"description": "The shipping fee.", "type": "integer"}, "strikethroughPrice": {"description": "The strikethrough price.", "type": "integer"}, "finalPrice": {"description": "The final price paid in money. Only include shipping fee.", "type": "integer"}}}, "DtoPriceBreakdownForTreasureHunt": {"title": "DtoPriceBreakdownForTreasureHunt", "description": "Price breakdown for treasure hunt products", "type": "object", "properties": {"strikethroughPrice": {"description": "The strikethrough price in cents.", "type": "integer"}}}, "DtoPricesOfBuyingTypes": {"title": "DtoPricesOfBuyingTypes", "type": "object", "properties": {"teamBuy": {"description": "The team buy price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "join": {"description": "The join price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "buyAlone": {"description": "The buy alone price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "flashSale": {"description": "The flash sale price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForFlashSale"}]}, "treasureHunt": {"description": "The treasure hunt price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForTreasureHunt"}]}}}, "DealDataResponse": {"title": "DealDataResponse", "type": "object", "properties": {"deal": {"description": "The deal details.", "type": "object"}, "priceBreakdown": {"description": "The price breakdown for each buying type.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}, "DuoDealEntryDataRequest": {"title": "DuoDealEntryDataRequest", "type": "object", "properties": {"duoDealEntryId": {"description": "The duo deal entry id.", "type": "string"}, "channel": {"description": "Algo channel defined in client", "type": "string"}}, "required": ["duoDealEntryId"]}, "DuoDealEntryDataResponse": {"title": "DuoDealEntryDataResponse", "type": "object", "properties": {"duoDealEntry": {"description": "The duo deal entry details.", "type": "object"}, "deal": {"description": "The deal details.", "type": "object"}, "priceBreakdown": {"description": "The price breakdown for each buying type.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}, "EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "SubsidyCategoriesResp": {"title": "SubsidyCategoriesResp", "type": "object", "properties": {"categories": {"description": "The list of subsidy google categories.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/GoogleCategoryTree"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}