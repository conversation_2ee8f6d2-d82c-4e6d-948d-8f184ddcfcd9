{"openapi": "3.1.0", "info": {"title": "Blidz Mission_System API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/mission_system/live_mission_projects": {"get": {"description": "Get mission projects with state 'active' or 'grace'", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/LiveMissionProjectsResp"}}}}}}}}}, "/mission_system/mission_projects/{missionProjectState}": {"get": {"description": "Get mission projects with specific state", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"in": "path", "name": "missionProjectState", "required": true, "schema": {"type": "string", "enum": ["not_started", "active", "grace", "closed"]}, "description": "The mission project state"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MissionProjectsWithStateResp"}}}}}}}}}, "/mission_system/user_missions": {"get": {"description": "Get user missions which are on going or have rewards available", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/UserMissionsResp"}}}}}}}}}, "/mission_system/claim_rewards": {"post": {"description": "Claim rewards in specific cycle and/or claim final rewards of a user mission", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimRewardsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimRewardsResp"}}}}}}}}}, "/mission_system/claim_project_rewards": {"post": {"description": "Claim rewards in all user missions in the specific project", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimProjectRewardsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimProjectRewardsResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoMissionAction": {"title": "DtoMissionAction", "description": "An enumeration.", "enum": ["view_deal", "duodeal_completed_for_host", "treasure_hunt_open_box", "buy_cash_pack", "complete_deal_checkout_buy_alone", "complete_deal_checkout_flash_sale", "complete_deal_checkout_deal_freeze", "treasure_hunt_enter", "mystery_box_claim"], "type": "string"}, "DtoMissionCycleDuration": {"title": "DtoMissionCycleDuration", "description": "An enumeration.", "enum": ["daily", "weekly", "monthly"], "type": "string"}, "DtoRewardType": {"title": "DtoRewardType", "description": "An enumeration.", "enum": ["coin", "cash", "mystery_box", "key", "spin", "game_turn"], "type": "string"}, "DtoMissionReward": {"title": "DtoMissionReward", "type": "object", "properties": {"type": {"$ref": "#/components/schemas/DtoRewardType"}, "amount": {"description": "The amount of the reward. For `coin` it's the coin amount. For `cash` it's the cent. For `mystery_box`, it's ignored", "type": "integer"}}}, "DtoMissionProject": {"title": "DtoMissionProject", "type": "object", "properties": {"name": {"description": "The name of this mission project, like 'daily_view_deals'", "type": "string"}, "description": {"description": "The description of this mission", "type": "string"}, "priority": {"description": "The priority of this mission. Used to sort the missions in UI. Lower number means higher priority", "type": "integer"}, "iconFile": {"description": "Icon file", "type": "string"}, "startTime": {"description": "Timestamp of the start time of this project. If this is null, the project starts now", "minimum": 0, "type": "integer"}, "endTime": {"description": "Timestamp of the end time of this project. If this is null, the project lives forever", "minimum": 0, "type": "integer"}, "action": {"description": "The required action of the mission", "allOf": [{"$ref": "#/components/schemas/DtoMissionAction"}]}, "cycleDuration": {"description": "The duration of mission", "allOf": [{"$ref": "#/components/schemas/DtoMissionCycleDuration"}]}, "cycleDescription": {"description": "Show user the descrption of current cycle", "type": "string"}, "cycleRequiredActionCount": {"description": "How many times the action must be performed in each cycle", "exclusiveMinimum": 0, "type": "integer"}, "cycleRepeatCount": {"description": "How many cycles this mission consists of", "exclusiveMinimum": 0, "type": "integer"}, "cycleRewards": {"description": "The rewards in each cycle", "type": "array", "items": {"$ref": "#/components/schemas/DtoMissionReward"}}, "finalRewards": {"description": "The big rewards if all cycles are completed. It's ok if mission doesn't have a big reward", "type": "array", "items": {"$ref": "#/components/schemas/DtoMissionReward"}}}}, "LiveMissionProjectsResp": {"title": "LiveMissionProjectsResp", "type": "object", "properties": {"missionProjects": {"description": "The mission projects", "type": "array", "items": {"$ref": "#/components/schemas/DtoMissionProject"}}}}, "MissionProjectsWithStateResp": {"title": "MissionProjectsWithStateResp", "type": "object", "properties": {"missionProjects": {"description": "The mission projects", "type": "array", "items": {"$ref": "#/components/schemas/DtoMissionProject"}}}}, "DtoUserMissionCycle": {"title": "DtoUserMissionCycle", "type": "object", "properties": {"cycleNumber": {"description": "The index of current cycle, start from 0", "minimum": 0, "type": "integer"}, "startTime": {"description": "The timestamp of start time of this cycle", "minimum": 0, "type": "integer"}, "endTime": {"description": "The timestamp of end time of this cycle", "minimum": 0, "type": "integer"}, "actionCount": {"description": "How many times the action have be performed in this cycle", "minimum": 0, "type": "integer"}, "completed": {"description": "Is this cycle completed. i.e. user has performed enough actions in this cycle", "type": "boolean"}, "rewardIndex": {"description": "The index of reward in mission project", "minimum": 0, "type": "integer"}, "rewardsClaimed": {"description": "Is the reward in this cycle claimed", "type": "boolean"}, "rewardsClaimedTime": {"description": "The timestamp of claiming the rewards", "minimum": 0, "type": "integer"}}}, "DtoUserMissionState": {"title": "DtoUserMissionState", "description": "An enumeration.", "enum": ["on_going", "completed", "over"], "type": "string"}, "DtoUserMission": {"title": "DtoUserMission", "type": "object", "properties": {"_id": {"description": "The id of this user mission. For zero-step account, this field is not presented", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "projectName": {"description": "The name of corresponding mission project", "type": "string"}, "userId": {"description": "The id of user", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "startTime": {"description": "The timestamp of start time of this mission instance", "minimum": 0, "type": "integer"}, "endTime": {"description": "The timestamp of complete/over time of this mission instance", "minimum": 0, "type": "integer"}, "cycles": {"description": "The status of cycles of this mission instance", "type": "array", "items": {"$ref": "#/components/schemas/DtoUserMissionCycle"}}, "completedCycleCountTotal": {"description": "How many cycles are completed", "minimum": 0, "type": "integer"}, "finalRewardsClaimed": {"description": "Whether the final rewards are claimed", "type": "boolean"}, "finalRewardsClaimedTime": {"description": "The timestamp of claiming the final rewards", "minimum": 0, "type": "integer"}, "lastActionTime": {"description": "The timestamp of when the last action is performed", "minimum": 0, "type": "integer"}, "missionState": {"description": "The state of mission", "allOf": [{"$ref": "#/components/schemas/DtoUserMissionState"}]}}}, "UserMissionsResp": {"title": "UserMissionsResp", "type": "object", "properties": {"userMissions": {"description": "The missions of the user", "type": "array", "items": {"$ref": "#/components/schemas/DtoUserMission"}}}}, "ClaimRewardsReq": {"title": "ClaimRewardsReq", "type": "object", "properties": {"userMissionId": {"description": "The id of user mission", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "cycleNumber": {"description": "Which cycle is the rewards in. If this one and `claimFinalReward` are both null, then claim all available rewards in this user mission", "minimum": 0, "type": "integer"}, "claimFinalReward": {"description": "Whether to claim final rewards. If this one and `claimFinalReward` are both null, then claim all available rewards in this user mission", "type": "boolean"}}, "required": ["userMissionId"]}, "ClaimRewardsResp": {"title": "ClaimRewardsResp", "type": "object", "properties": {"rewards": {"description": "The claimed rewards", "type": "array", "items": {"$ref": "#/components/schemas/DtoMissionReward"}}, "userMission": {"description": "The updated user mission", "allOf": [{"$ref": "#/components/schemas/DtoUserMission"}]}}}, "ClaimProjectRewardsReq": {"title": "ClaimProjectRewardsReq", "type": "object", "properties": {"projectName": {"description": "Name of mission project", "type": "string"}}, "required": ["projectName"]}, "ClaimProjectRewardsResp": {"title": "ClaimProjectRewardsResp", "type": "object", "properties": {"rewards": {"description": "The claimed rewards", "type": "array", "items": {"$ref": "#/components/schemas/DtoMissionReward"}}, "userMissions": {"description": "The updated user missions", "type": "array", "items": {"$ref": "#/components/schemas/DtoUserMission"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}