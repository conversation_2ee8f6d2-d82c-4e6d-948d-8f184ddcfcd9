{"openapi": "3.1.0", "info": {"title": "Blidz Social_Circles API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/social_circles/stories/list_owners": {"get": {"description": "List all story owners.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListOwnersResponse"}}}}}}}}}, "/social_circles/stories/by_type/{story_type}": {"get": {"description": "List stories by story type.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"in": "path", "name": "story_type", "required": true, "schema": {"type": "string", "enum": ["duo_deal"]}, "description": "The story type. Currently only duo_deal supported."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListByTypeResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "422": {"description": "If the story type is invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/stories/me": {"get": {"description": "List logged-in user's stories.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListMyStoriesResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/stories/view": {"post": {"description": "Mark a story owner's all stories as viewed.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkViewedRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/MarkViewedResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "422": {"description": "If duo deal entries are missing, or owner is oneself.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/stories/{story_id}": {"get": {"description": "Read one story by <PERSON>.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"in": "path", "name": "story_id", "required": true, "schema": {"type": "string"}, "description": "The story ID"}, {"in": "query", "name": "reqId", "required": false, "schema": {"type": "string"}, "description": "The request Id"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ReadStoryResponse"}}}}}}, "401": {"description": "If the user is not logged in.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "If the story is not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/stories/by_user/{user_id}": {"get": {"description": "Load stories by user id.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"in": "path", "name": "user_id", "required": true, "schema": {"type": "string"}, "description": "The user id."}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/StoriesByUserIdResponse"}}}}}}, "401": {"description": "If the user not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/social_circles/list_referrals": {"post": {"description": "List all referrals made by the logged-in user.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListReferralsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListReferralsResponse"}}}}}}}}}, "/social_circles/show_referral_progress": {"post": {"description": "Show the referral progress of the invited user.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShowReferralProgressRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ShowReferralProgressResponse"}}}}}}}}}, "/social_circles/referral_info": {"post": {"description": "Show the referral info for the logged-in user.", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReferralInfoRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ReferralInfoResponse"}}}}}}}}}}, "components": {"schemas": {"ListOwnersRequest": {"title": "ListOwnersRequest", "type": "object", "properties": {"includeConnections": {"description": "Include connections.", "default": true, "type": "boolean"}, "includeGlobal": {"description": "Include global story owners.", "default": true, "type": "boolean"}}}, "DocumentModel": {"title": "DocumentModel", "description": "Base document class.", "type": "object", "properties": {"_key": {"title": " Key", "type": "string"}, "_rev": {"title": " Rev", "type": "string"}}}, "commonlib__social_circles__models__relationships__Edge__Types": {"title": "Types", "description": "Edge type definitions.\n\nIf the enumeration value increases, consider whether to add it in \"get_user_story_feed\"", "enum": ["buy_together_nonpaying", "buy_together_paying", "join_external_nonpaying", "join_external_paying", "join_internal_nonpaying", "join_internal_paying", "share_duo_invite", "access_duo_invite", "owner_of", "has_viewed", "refer", "referred_by", "liked_post", "reshare_post", "is_friend", "post_product_like", "post_duo_deal_completion", "post_sign_up_own", "post_referral_success_own", "post_active_duo_deal_own", "post_popular_product", "post_play_treasure_hunt_own"], "type": "string"}, "DuoDealRelationship": {"title": "DuoDealRelationship", "description": "DuoDealRelationship model in the graph.", "type": "object", "properties": {"_key": {"title": " Key", "type": "string"}, "_rev": {"title": " Rev", "type": "string"}, "_from": {"title": " From", "anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/DocumentModel"}]}, "_to": {"title": " To", "anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/DocumentModel"}]}, "internalData": {"title": "Internaldata", "description": "Internal data for recording relationships.", "type": "object"}, "type": {"description": "The edge type.", "allOf": [{"$ref": "#/components/schemas/commonlib__social_circles__models__relationships__Edge__Types"}]}, "createdAt": {"title": "Createdat", "description": "The datetime when the edge was created.", "type": "string", "format": "date-time"}, "updatedAt": {"title": "Updatedat", "description": "The datetime when the edge was updated.", "type": "string", "format": "date-time"}, "duoDealEntryIds": {"title": "Duodealentryids", "description": "A list of duo deal entry id's for this relationship.", "type": "array", "items": {"type": "string"}}}, "required": ["_from", "_to", "type", "duoDealEntryIds"]}, "commonlib__social_circles__models__nodes__Node__Types": {"title": "Types", "description": "Node type definitions.", "enum": ["story", "user"], "type": "string"}, "User": {"title": "User", "description": "User model in the graph.", "type": "object", "properties": {"_key": {"title": " Key", "type": "string"}, "_rev": {"title": " Rev", "type": "string"}, "type": {"default": "user", "allOf": [{"$ref": "#/components/schemas/commonlib__social_circles__models__nodes__Node__Types"}]}, "createdAt": {"title": "Createdat", "description": "The datetime when the edge was created.", "type": "string", "format": "date-time"}, "updatedAt": {"title": "Updatedat", "description": "The datetime when the edge was updated.", "type": "string", "format": "date-time"}, "djangoId": {"title": "Djangoid", "description": "The Django ID of the user.", "type": "integer"}, "email": {"title": "Email", "description": "The user's email address.", "type": "string"}, "firstName": {"title": "Firstname", "description": "The user's first name.", "type": "string"}, "imageUrl": {"title": "<PERSON><PERSON><PERSON>", "description": "The user's image, if they have one.", "type": "string"}, "facebookLogin": {"title": "Facebooklogin", "description": "Whether the user logged in with Facebook or not.", "type": "boolean"}}, "required": ["djangoId", "email", "firstName"]}, "Statuses": {"title": "Statuses", "description": "An enumeration.", "enum": ["enabled", "disabled"], "type": "string"}, "PrivacyTypes": {"title": "PrivacyTypes", "description": "An enumeration.", "enum": ["public", "private", "connections"], "type": "string"}, "commonlib__social_circles__models__nodes__Story__Types": {"title": "Types", "description": "An enumeration.", "enum": ["duo_deal", "post_product_like", "post_duo_deal_completion", "post_sign_up_own", "post_referral_success_own", "post_active_duo_deal_own", "post_popular_product", "post_play_treasure_hunt_own"], "type": "string"}, "PriceComponent": {"title": "PriceComponent", "type": "object", "properties": {"non_refundable_fee": {"title": "Non Refundable Fee", "description": "Non refundable fee.", "type": "integer"}, "total_default_shipping": {"title": "Total Default Shipping", "description": "Total default shipping.", "type": "integer"}, "blidz_cash_discount": {"title": "<PERSON><PERSON><PERSON> Cash Discount", "description": "<PERSON><PERSON><PERSON> Cash Discount.", "type": "integer"}, "additive_fee": {"title": "Additive Fee", "description": "Additive fee.", "type": "integer"}, "req_id": {"title": "Req <PERSON>d", "description": "The request Id", "type": "string"}}}, "PriceComponents": {"title": "PriceComponents", "description": "Class for storing the total JIP and JIN prices for a story.", "type": "object", "properties": {"joinInternalPaying": {"title": "Joininternalpaying", "description": "The JIP price components.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}, "joinInternalNonpaying": {"title": "Joininternalnonpaying", "description": "The JIN price components.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}}}, "LabelsObject": {"title": "LabelsObject", "type": "object", "properties": {"description": {"title": "Description", "description": "User customizable post description", "type": "string"}, "under_button_labels": {"title": "Under Button Labels", "description": "The labels under button", "type": "string"}, "cta_label": {"title": "Cta Label", "type": "string"}, "cta_destination": {"title": "Cta Destination", "type": "string"}}}, "SimpleUserData": {"title": "SimpleUserData", "description": "Base model for user data.", "type": "object", "properties": {"userId": {"title": "Userid", "description": "The user's ID.", "type": "string"}, "avatarUrl": {"title": "Avatarurl", "description": "The user's avatar <PERSON><PERSON>.", "type": "string"}, "firstName": {"title": "Firstname", "description": "The user's first name.", "type": "string"}, "phoneNumber": {"title": "Phonenumber", "description": "The user's phone number.", "type": "string"}, "membershipStatus": {"title": "Membershipstatus", "description": "The user's membership status.", "type": "string"}}}, "PostProductData": {"title": "PostProductData", "type": "object", "properties": {"product_id": {"title": "Product Id", "description": "Blidz’s PID of the product associated with the Post", "type": "string"}, "product_name": {"title": "Product Name", "description": "Product name of the product associated with the Post", "type": "string"}, "product_like_count": {"title": "Product Like Count", "description": "The total product like count of the product associated with the Post", "type": "integer"}, "deal": {"title": "Deal", "description": "The deal data.", "type": "object"}, "recent_user_bought": {"title": "Recent User Bought", "description": "User info on latest user who bought the product.", "allOf": [{"$ref": "#/components/schemas/SimpleUserData"}]}, "bought_count": {"title": "Bought Count", "description": "Count of purchases of this product.", "type": "integer"}}}, "RewardType": {"title": "RewardType", "description": "An enumeration.", "enum": ["coin", "cash", "mystery_box", "key", "spin", "game_turn"], "type": "string"}, "RewardStatus": {"title": "RewardStatus", "description": "An enumeration.", "enum": ["available", "claimed"], "type": "string"}, "FriendReward": {"title": "Friend<PERSON>eward", "type": "object", "properties": {"rewardType": {"description": "The reward type", "allOf": [{"$ref": "#/components/schemas/RewardType"}]}, "rewardCount": {"title": "Rewardcount", "description": "The amount of rewards to give. E.g. count = 2 means that we give out 2 rewards at once.", "type": "integer"}, "rewardLimit": {"title": "Rewardlimit", "description": "Total amount of rewards available.", "type": "integer"}, "claimedCount": {"title": "Claimedcount", "description": "The amount of claimed rewards.", "default": 0, "type": "integer"}, "status": {"description": "The reward status, claimed or available.", "default": "available", "allOf": [{"$ref": "#/components/schemas/RewardStatus"}]}, "claimedBy": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "The user information on which users claimed the reward.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/SimpleUserData"}}, "excludedUsers": {"title": "Excludedusers", "description": "List of user ids to be excluded from getting the reward, e.g. the host user and the completing user.", "default": [], "type": "array", "items": {"type": "string"}}}, "required": ["rewardType", "rewardCount", "rewardLimit"]}, "PostDuoDeal": {"title": "PostDuoDeal", "type": "object", "properties": {"duo_deal_entry_id": {"title": "Duo Deal Entry Id", "description": "The duo deal entry id.", "type": "string"}, "deal": {"title": "Deal", "description": "The deal data.", "type": "object"}, "expires_at": {"title": "Expires At", "description": "When the deal expires.", "type": "string", "format": "date-time"}, "price_components": {"title": "Price Components", "description": "The price components for JIN and JIP for the deal.", "allOf": [{"$ref": "#/components/schemas/PriceComponents"}]}, "price_component": {"title": "Price Component", "description": "The price component JIP or JIN for the deal.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}, "friend_reward": {"title": "Friend <PERSON><PERSON>", "description": "A reward connected to the duo deal.", "allOf": [{"$ref": "#/components/schemas/FriendReward"}]}}}, "PostSignUpOwn": {"title": "PostSignUpOwn", "type": "object", "properties": {"user_id": {"title": "User Id", "description": "User's id.", "type": "string"}}}, "PostReferralSuccessOwn": {"title": "PostReferralSuccessOwn", "type": "object", "properties": {"inviter_id": {"title": "Inviter Id", "description": "Inviter id by referral success", "type": "string"}, "inviter_name": {"title": "Inviter Name", "description": "Inviter name by referral success", "type": "string"}, "inviter_avatar_url": {"title": "Inviter Avatar Url", "description": "Inviter avatar url by referral success", "type": "string"}, "invited_id": {"title": "Invited Id", "description": "Invited id by referral success", "type": "string"}, "invited_name": {"title": "Invited Name", "description": "Invited name by referral success", "type": "string"}, "invited_avatar_url": {"title": "Invited Avatar Url", "description": "Invited avatar url by referral success", "type": "string"}}}, "PostPlayTreasureHunt": {"title": "PostPlayTreasureHunt", "type": "object", "properties": {"deal_id": {"title": "Deal Id", "description": "Deal's id of treasure hunt", "type": "string"}, "racers_count": {"title": "Racers Count", "description": "Number of racers in treasure hunt", "type": "integer"}, "recent_racers": {"title": "Recent Racers", "description": "Number of racers in treasure hunt", "type": "array", "items": {"$ref": "#/components/schemas/SimpleUserData"}}}}, "Story": {"title": "Story", "description": "Story model in the graph.", "type": "object", "properties": {"_key": {"title": " Key", "type": "string"}, "_rev": {"title": " Rev", "type": "string"}, "type": {"default": "story", "allOf": [{"$ref": "#/components/schemas/commonlib__social_circles__models__nodes__Node__Types"}]}, "createdAt": {"title": "Createdat", "description": "The datetime when the edge was created.", "type": "string", "format": "date-time"}, "updatedAt": {"title": "Updatedat", "description": "The datetime when the edge was updated.", "type": "string", "format": "date-time"}, "status": {"description": "The status of the story.", "default": "enabled", "allOf": [{"$ref": "#/components/schemas/Statuses"}]}, "privacyType": {"description": "The privacy type of the story.", "default": "public", "allOf": [{"$ref": "#/components/schemas/PrivacyTypes"}]}, "storyType": {"description": "The type of the story.", "allOf": [{"$ref": "#/components/schemas/commonlib__social_circles__models__nodes__Story__Types"}]}, "owner": {"title": "Owner", "description": "The owner of the story.", "type": "string"}, "ownerName": {"title": "<PERSON><PERSON>ame", "description": "The user's first name who's the owner of the post", "type": "string"}, "ownerAvatarUrl": {"title": "Owneravatarurl", "description": "The user's avatar url who's the owner of the post", "type": "string"}, "ownerMembershipStatus": {"title": "Ownermembershipstatus", "description": "The owner user's membership status.", "type": "string"}, "imageUrl": {"title": "<PERSON><PERSON><PERSON>", "description": "URL of the image to show in the post", "type": "string"}, "postLikedCount": {"title": "Postlikedcount", "description": "The POST liked count", "type": "integer"}, "duoDealEntryId": {"title": "Duodealentryid", "description": "The duo deal entry id.", "type": "string"}, "deal": {"title": "Deal", "description": "The deal data.", "type": "object"}, "expiresAt": {"title": "Expiresat", "description": "When the deal expires.", "type": "string", "format": "date-time"}, "priceComponents": {"title": "Pricecomponents", "description": "The price components for JIN and JIP for the deal.", "allOf": [{"$ref": "#/components/schemas/PriceComponents"}]}, "priceComponent": {"title": "Pricecomponent", "description": "The price component for JIN or JIP for the deal.", "allOf": [{"$ref": "#/components/schemas/PriceComponent"}]}, "labelOfMe": {"$ref": "#/components/schemas/LabelsObject"}, "labelOfOthers": {"$ref": "#/components/schemas/LabelsObject"}, "postProductLike": {"$ref": "#/components/schemas/PostProductData"}, "postDuoDealCompletion": {"$ref": "#/components/schemas/PostDuoDeal"}, "postSignUpOwn": {"$ref": "#/components/schemas/PostSignUpOwn"}, "postReferralSuccessOwn": {"$ref": "#/components/schemas/PostReferralSuccessOwn"}, "postActiveDuoDealOwn": {"$ref": "#/components/schemas/PostDuoDeal"}, "postPopularProduct": {"$ref": "#/components/schemas/PostProductData"}, "postPlayTreasureHuntOwn": {"$ref": "#/components/schemas/PostPlayTreasureHunt"}}, "required": ["storyType", "owner"]}, "DtoListStoriesData": {"title": "DtoListStoriesData", "type": "object", "properties": {"user": {"description": "The user.", "allOf": [{"$ref": "#/components/schemas/User"}]}, "stories": {"description": "The stories.", "type": "array", "items": {"$ref": "#/components/schemas/Story"}}}, "required": ["user", "stories"]}, "DtoPriceBreakdownForDuoDeal": {"title": "DtoPriceBreakdownForDuoDeal", "type": "object", "properties": {"itemPrice": {"description": "The item price.", "type": "integer"}, "shippingFee": {"description": "The shipping fee.", "type": "integer"}, "processingFee": {"description": "The processing fee.", "type": "integer"}, "discount": {"description": "The discount.", "type": "integer"}, "fincoinDiscountAmount": {"description": "The fincoin amount to be used.", "type": "integer"}, "fincoinDiscountMonetaryValue": {"description": "The monetary value of fincoin in cents.", "type": "integer"}, "finalPrice": {"description": "The final price.", "type": "integer"}, "strikethroughPrice": {"description": "The strikethrough price.", "type": "integer"}}}, "DtoPriceBreakdownForFlashSale": {"title": "DtoPriceBreakdownForFlashSale", "type": "object", "properties": {"fincoinAmount": {"description": "The fincoin amount to be used.", "type": "integer"}, "fincoinMonetaryValue": {"description": "The monetary value of fincoin in cents.", "type": "integer"}, "shippingFee": {"description": "The shipping fee.", "type": "integer"}, "strikethroughPrice": {"description": "The strikethrough price.", "type": "integer"}, "finalPrice": {"description": "The final price paid in money. Only include shipping fee.", "type": "integer"}}}, "DtoPriceBreakdownForTreasureHunt": {"title": "DtoPriceBreakdownForTreasureHunt", "description": "Price breakdown for treasure hunt products", "type": "object", "properties": {"strikethroughPrice": {"description": "The strikethrough price in cents.", "type": "integer"}}}, "DtoPricesOfBuyingTypes": {"title": "DtoPricesOfBuyingTypes", "type": "object", "properties": {"teamBuy": {"description": "The team buy price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "join": {"description": "The join price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "buyAlone": {"description": "The buy alone price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForDuoDeal"}]}, "flashSale": {"description": "The flash sale price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForFlashSale"}]}, "treasureHunt": {"description": "The treasure hunt price.", "allOf": [{"$ref": "#/components/schemas/DtoPriceBreakdownForTreasureHunt"}]}}}, "StoryOwnersResponse": {"title": "StoryOwnersResponse", "type": "object", "properties": {"relations": {"description": "All the outbound relations from the current user.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DuoDealRelationship"}}, "storyOwners": {"description": "A list of story owners that the current user has a relation to.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/DtoListStoriesData"}}, "priceBreakdown": {"description": "The price breakdown for the deals in the story list.", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}}, "ListOwnersResponse": {"title": "ListOwnersResponse", "type": "object", "properties": {"data": {"description": "The response for listing story owners.", "allOf": [{"$ref": "#/components/schemas/StoryOwnersResponse"}]}, "priceBreakdown": {"description": "The price breakdown for the deals in the story list.", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}, "required": ["data"]}, "ListByTypeRequest": {"title": "ListByTypeRequest", "type": "object", "properties": {}}, "ListByTypeResponse": {"title": "ListByTypeResponse", "type": "object", "properties": {"data": {"description": "List of stories of the given type.", "type": "array", "items": {"$ref": "#/components/schemas/Story"}}, "priceBreakdown": {"description": "The price breakdown for the deals in the story list.", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}, "required": ["data"]}, "ListMyStoriesRequest": {"title": "ListMyStoriesRequest", "type": "object", "properties": {}}, "ListMyStoriesResponse": {"title": "ListMyStoriesResponse", "type": "object", "properties": {"data": {"description": "The data.", "allOf": [{"$ref": "#/components/schemas/DtoListStoriesData"}]}, "priceBreakdown": {"description": "The price breakdown for the deals in the story list.", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}}, "required": ["data"]}, "MarkViewedRequest": {"title": "MarkViewedRequest", "type": "object", "properties": {"ownerId": {"description": "The user's ID who's the owner of the story.", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "duoDealEntryIds": {"description": "A list of Duo Deal Entry IDs that the user has viewed.", "type": "array", "items": {"type": "string"}}}, "required": ["ownerId", "duoDealEntryIds"]}, "MarkViewedResponse": {"title": "MarkViewedResponse", "type": "object", "properties": {"data": {"description": "Empty response.", "type": "object"}}}, "ReadStoryRequest": {"title": "ReadStoryRequest", "type": "object", "properties": {}}, "ReadStoryResponse": {"title": "ReadStoryResponse", "type": "object", "properties": {"data": {"description": "The story if found.", "allOf": [{"$ref": "#/components/schemas/Story"}]}, "priceBreakdown": {"description": "The price breakdown for the deal.", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DtoPricesOfBuyingTypes"}}}, "required": ["data"]}, "StoriesByUserIdRequest": {"title": "StoriesByUserIdRequest", "type": "object", "properties": {"limit": {"description": "Stories paging limit label.", "type": "integer"}, "offset": {"description": "Stories paging offset label.", "type": "integer"}}}, "StoriesByUserIdResponse": {"title": "StoriesByUserIdResponse", "type": "object", "properties": {"data": {"description": "List of user own stories.", "type": "object", "additionalProperties": {"anyOf": [{"$ref": "#/components/schemas/User"}, {"type": "array", "items": {"$ref": "#/components/schemas/Story"}}]}}}, "required": ["data"]}, "ReferralStatus": {"title": "ReferralStatus", "description": "An enumeration.", "enum": ["completed", "ongoing"], "type": "string"}, "ListReferralsRequest": {"title": "ListReferralsRequest", "type": "object", "properties": {"page": {"description": "Paging page number.", "default": 1, "minimum": 1, "type": "integer"}, "pageSize": {"description": "Paging size number", "default": 20, "minimum": 1, "maximum": 100, "type": "integer"}, "referralStatus": {"description": "The referral status.", "allOf": [{"$ref": "#/components/schemas/ReferralStatus"}]}}}, "RewardTypeForRewardSystem": {"title": "RewardTypeForRewardSystem", "description": "Defines different reward types.", "enum": ["cash", "coin", "mystery_box", "key", "spin", "gift_drop", "signup", "signup_650", "signup_660", "signup_663", "game_turn", "payout_balance", "fincoin", "fincoin_cent", "payout_turn"], "type": "string"}, "ReferReward": {"title": "ReferReward", "type": "object", "properties": {"rewardType": {"description": "The reward type", "allOf": [{"$ref": "#/components/schemas/RewardTypeForRewardSystem"}]}, "amount": {"description": "The amount of rewards to get.", "type": "integer"}}}, "ReferralStep": {"title": "ReferralStep", "description": "An enumeration.", "enum": ["join_with_referral_link", "sign_up", "start_trial", "become_member", "get_referral_rewards"], "type": "string"}, "ReferralStepData": {"title": "ReferralStepData", "type": "object", "properties": {"step": {"description": "The step name", "allOf": [{"$ref": "#/components/schemas/ReferralStep"}]}, "clientName": {"description": "The name shown in client.", "type": "string"}, "completed": {"description": "Whether the step completed or not.", "default": false, "type": "boolean"}}, "required": ["step"]}, "ReferralUser": {"title": "ReferralUser", "type": "object", "properties": {"userId": {"description": "The invited user's ID.", "type": "string"}, "avatar": {"description": "The invited user's avatar.", "type": "string"}, "firstName": {"description": "The invited user's first name.", "type": "string"}, "lastName": {"description": "The invited user's last name.", "type": "string"}, "playerName": {"description": "The invited user's player name.", "type": "string"}, "rewards": {"description": "The referral rewards you'll get.", "type": "array", "items": {"$ref": "#/components/schemas/ReferReward"}}, "currentStep": {"description": "The current step the user is at.", "allOf": [{"$ref": "#/components/schemas/ReferralStep"}]}, "completedSteps": {"description": "The completed steps.", "type": "array", "items": {"$ref": "#/components/schemas/ReferralStep"}}, "steps": {"description": "The steps that are necessary for the rewards.", "type": "array", "items": {"$ref": "#/components/schemas/ReferralStepData"}}}}, "Referral": {"title": "Referral", "type": "object", "properties": {"inviter": {"description": "The inviter side of the referral.", "allOf": [{"$ref": "#/components/schemas/ReferralUser"}]}, "invitee": {"description": "The invitee side of the referral", "allOf": [{"$ref": "#/components/schemas/ReferralUser"}]}}, "required": ["inviter", "invitee"]}, "ListReferralsResponse": {"title": "ListReferralsResponse", "type": "object", "properties": {"referrals": {"description": "List of referrals.", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/Referral"}}, "clientRequiredSteps": {"description": "List of required steps shown in client", "default": ["sign_up", "start_trial", "become_member", "get_referral_rewards"], "type": "array", "items": {"$ref": "#/components/schemas/ReferralStep"}}}}, "ShowReferralProgressRequest": {"title": "ShowReferralProgressRequest", "type": "object", "properties": {"inviteeId": {"description": "The invitee id to view the progress for.", "type": "string"}}, "required": ["inviteeId"]}, "ShowReferralProgressResponse": {"title": "ShowReferralProgressResponse", "type": "object", "properties": {"steps": {"description": "The steps that are necessary for the rewards.", "type": "array", "items": {"$ref": "#/components/schemas/ReferralStepData"}}, "currentStep": {"description": "The current step the user is at.", "allOf": [{"$ref": "#/components/schemas/ReferralStep"}]}, "inviter": {"description": "The inviter data.", "allOf": [{"$ref": "#/components/schemas/ReferralUser"}]}, "invitee": {"description": "The invitee data.", "allOf": [{"$ref": "#/components/schemas/ReferralUser"}]}, "completedSteps": {"description": "The completed steps.", "type": "array", "items": {"$ref": "#/components/schemas/ReferralStep"}}, "clientRequiredSteps": {"description": "List of required steps shown in client", "default": ["sign_up", "start_trial", "become_member", "get_referral_rewards"], "type": "array", "items": {"$ref": "#/components/schemas/ReferralStep"}}}}, "ReferralInfoRequest": {"title": "ReferralInfoRequest", "type": "object", "properties": {}}, "DtoEarningType": {"title": "DtoEarningType", "description": "An enumeration.", "enum": ["cash", "coin", "key", "spin", "payout_balance"], "type": "string"}, "DtoTotalEarning": {"title": "DtoTotalEarning", "type": "object", "properties": {"type": {"description": "Enum: cash, coin.", "allOf": [{"$ref": "#/components/schemas/DtoEarningType"}]}, "amount": {"description": "Cents", "type": "integer"}}}, "ReferralInfoResponse": {"title": "ReferralInfoResponse", "type": "object", "properties": {"inviter": {"description": "The inviter of the logged-in user.", "allOf": [{"$ref": "#/components/schemas/ReferralUser"}]}, "totalEarnings": {"description": "Target user total earnings from referral system. Either logged-in user or other user's profile.", "type": "array", "items": {"$ref": "#/components/schemas/DtoTotalEarning"}}, "rewards": {"description": "The referral rewards you'll get.", "type": "array", "items": {"$ref": "#/components/schemas/ReferReward"}}, "totalInvites": {"description": "The total invites for the user.", "type": "integer"}, "totalIncompleteInvites": {"description": "The total incomplete invites.", "type": "integer"}, "clientRequiredSteps": {"description": "List of required steps shown in client", "default": ["sign_up", "start_trial", "become_member", "get_referral_rewards"], "type": "array", "items": {"$ref": "#/components/schemas/ReferralStep"}}, "currentReferral": {"description": "The current referral where current user is an invitee or an inviter", "allOf": [{"$ref": "#/components/schemas/Referral"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}