{"openapi": "3.1.0", "info": {"title": "Blidz Gift_Drop API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/gift_drop/user_recent_gift_drops": {"get": {"description": "Get multiple gift drops of a user, which are about to be able to claim. Sort by the drop time ascendingly", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"in": "query", "name": "maxCount", "required": false, "schema": {"type": "integer", "default": 20}, "description": "The max count of drops"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/UserRecentGiftDropsResp"}}}}}}}}}, "/gift_drop/claim_gift_drop": {"post": {"description": "Claim gift drop of a user", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimGiftDropReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimGiftDropResp"}}}}}}}}}, "/gift_drop/claim_all_gift_drops": {"post": {"description": "Claim all gift drops of a user", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimAllGiftDropsReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimAllGiftDropsResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "DtoRewardType": {"title": "DtoRewardType", "description": "An enumeration.", "enum": ["coin", "cash", "mystery_box", "special_offer", "key", "spin", "game_turn"], "type": "string"}, "DtoGift": {"title": "DtoGift", "type": "object", "properties": {"giftType": {"description": "The type of reward", "allOf": [{"$ref": "#/components/schemas/DtoRewardType"}]}, "amount": {"description": "The amount of the reward. For `coin` it's the coin amount. For `cash` it's the cent.", "minimum": 0, "type": "integer"}}}, "DtoUserGiftDrop": {"title": "DtoUserGiftDrop", "type": "object", "properties": {"_id": {"description": "The drop ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "userId": {"description": "User ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "campaignId": {"description": "The ID of the compaign to which this drop belongs", "pattern": "^[a-zA-Z0-9_-]+$", "type": "string"}, "campaignTitle": {"description": "The title of the compaign to which this drop belongs", "type": "string"}, "campaignIcon": {"description": "The icon of the campaign to show on the UI", "type": "string"}, "dropTime": {"description": "The time when this drop is valid to claim. If 0 or null, then the drop is valid to claim now", "minimum": 0, "type": "integer"}, "validUntil": {"description": "The time when this drop can no longer be claimed. If null, then this drop can always be claiemd", "minimum": 0, "type": "integer"}, "gifts": {"description": "The gifts in this drop", "type": "array", "items": {"$ref": "#/components/schemas/DtoGift"}}, "claimed": {"description": "Whether this drop is claimed", "type": "boolean"}}}, "UserRecentGiftDropsResp": {"title": "UserRecentGiftDropsResp", "type": "object", "properties": {"giftDrops": {"description": "The gift drops", "type": "array", "items": {"$ref": "#/components/schemas/DtoUserGiftDrop"}}}}, "ClaimGiftDropReq": {"title": "ClaimGiftDropReq", "type": "object", "properties": {"dropId": {"description": "The drop ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}}, "required": ["dropId"]}, "ClaimGiftDropResp": {"title": "ClaimGiftDropResp", "type": "object", "properties": {"nextDrop": {"description": "The next drop", "allOf": [{"$ref": "#/components/schemas/DtoUserGiftDrop"}]}}}, "ClaimAllGiftDropsReq": {"title": "ClaimAllGiftDropsReq", "type": "object", "properties": {"maxCount": {"description": "The max count of next drops", "type": "integer"}}}, "ClaimAllGiftDropsResp": {"title": "ClaimAllGiftDropsResp", "type": "object", "properties": {"claimedDrops": {"description": "Claimed gift drops", "type": "array", "items": {"$ref": "#/components/schemas/DtoUserGiftDrop"}}, "nextDrops": {"description": "The next drops", "type": "array", "items": {"$ref": "#/components/schemas/DtoUserGiftDrop"}}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}