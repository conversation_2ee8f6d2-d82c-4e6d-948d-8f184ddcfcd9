{"openapi": "3.1.0", "info": {"title": "Blidz Checkout Checkout_Definitions API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/checkout/checkout_price": {"post": {"description": "Returns a checkout price of deals", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutPriceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CheckoutPriceLegacyResponse"}}}}}}}}}, "/checkout/v2/checkout_price": {"post": {"description": "Returns a checkout price of deals", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutPriceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CheckoutPriceResponse"}}}}}}}}}, "/checkout/checkout_prices_for_detail_page": {"post": {"description": "Returns checkout prices for team_buy, join, and buy_alone buying types", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutPriceRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CheckoutPriceForDetailPageResponse"}}}}}}}}}}, "components": {"schemas": {"Options": {"title": "Options", "type": "object", "properties": {"skuId": {"description": "SKU ID", "type": "string"}, "attributes": {"description": "The attribute of sku. For example: {\"color\": \"red\"}", "type": "object"}, "priceLevel": {"description": "Price level, one of min, max, sale, target, solo, join. In Meituan model, when duodeal is team_buy, price_level must be none.", "type": "string"}}}, "Item": {"title": "<PERSON><PERSON>", "type": "object", "properties": {"type": {"description": "should be 'deal', because other types of items are all deprecated", "type": "string"}, "id": {"description": "should be deal id, because other types of items are all deprecated", "type": "string"}, "currency": {"description": "Currency of payment", "type": "string"}, "options": {"description": "Options", "allOf": [{"$ref": "#/components/schemas/Options"}]}, "shippingOptionCode": {"description": "The shipping option code. If omitted, use default blidz shipping", "type": "string"}, "buySource": {"description": "Whether user place orders in app or via an external link, one of internal, external", "type": "string"}, "duoDealEntryId": {"description": "The duo deal entry ID if user is joining a duo deal", "type": "string"}, "reqId": {"description": "Request Id", "type": "string"}, "shoppingCartItemId": {"description": "The ID of shopping cart item", "type": "string"}, "channel": {"description": "Original deal channel", "type": "string"}, "allowAutoCompleteByTicket": {"description": "Whether allow auto complete by ticket", "type": "boolean"}}}, "UserPlatform": {"title": "UserPlatform", "description": "An enumeration.", "enum": ["android_web", "desktop_web", "ios_web", "android_app", "ios_app", "unknown"], "type": "string"}, "CheckoutPriceRequest": {"title": "CheckoutPriceRequest", "type": "object", "properties": {"currency": {"description": "Currency of payment.", "type": "string"}, "items": {"description": "Detail of payment.", "type": "array", "items": {"$ref": "#/components/schemas/Item"}}, "userPlatform": {"description": "The platform used.", "allOf": [{"$ref": "#/components/schemas/UserPlatform"}]}, "priceId": {"description": "The price id to use for membership fee.", "type": "string"}, "channel": {"description": "The channel that sent to algorithm.", "type": "string"}}, "required": ["currency", "items"]}, "PriceItem": {"title": "PriceItem", "type": "object", "properties": {"id": {"description": "If type is deal, then deal id. If type is cash, then cash pack id. If type is coin pack, then coin pack id", "type": "string"}, "type": {"description": "The type of object that user pays for, one of deal, cash, coin pack", "type": "string"}, "strikethroughPrice": {"description": "Deal original price. It's none in virtual product.", "type": "integer"}, "itemPrice": {"description": "The item price in this pricing process. The sku_initial_price", "type": "integer"}, "universalItemPrice": {"description": "The item price in this pricing process.", "type": "integer"}, "buyingTypeDiscount": {"description": "Discount base on buying type. Non-positive integer.", "type": "integer"}, "processingFee": {"description": "Processing fee. The non_refundable_fee.", "type": "integer"}, "shippingFee": {"description": "Shipping fee. The default_shipping + extra_shipping.", "type": "integer"}, "cashCoupon": {"description": "Cash coupon. The free_cash_discount. Non-positive integer.", "type": "integer"}, "memberCoupon": {"description": "Cash coupon for member only. Non-positive integer.", "type": "integer"}, "blidzCash": {"description": "<PERSON>lidz cash. The paid_cash_discount. Non-positive integer.", "type": "integer"}, "fincoinDiscountAmount": {"description": "Fincoin discount. The fincoin_discount. Non-positive integer.", "type": "integer"}, "fincoinDiscountMonetaryValue": {"description": "Fincoin discount in cents. The fincoin_discount_cents. Non-positive integer.", "type": "integer"}, "total": {"description": "Final price. The calculation formula is: item_price + processing_fee + shipping_fee + cash_coupon + blidz_cash", "type": "integer"}}}, "PriceObject": {"title": "PriceObject", "type": "object", "properties": {"items": {"description": "Item list.", "type": "array", "items": {"$ref": "#/components/schemas/PriceItem"}}, "totalPrice": {"description": "Total price of item list.", "type": "integer"}, "totalStrikethroughPrice": {"description": "Total strikethrough price of item list.", "type": "integer"}, "totalCashCoupon": {"description": "Total cash coupon of item list.", "type": "integer"}, "totalBlidzCash": {"description": "Total Blidz cash of item list.", "type": "integer"}, "membershipFee": {"description": "In memberPrices, Membership fee 899. It is 0 when trial not used or user already is member or in nonMemberPrices.", "type": "integer"}, "totalPointReward": {"description": "The checkout points reward to give to the user.", "type": "integer"}, "totalBuyingTypeDiscount": {"description": "Total discount base on buy type to give to the user.", "type": "integer"}, "totalMemberCoupon": {"description": "The member's discount (call coupon) to give to the user.", "type": "integer"}, "totalFincoinDiscountAmount": {"description": "The fincoin discount to give to the user.", "type": "integer"}, "totalFincoinDiscountMonetaryValue": {"description": "The fincoin discount in cents to give to the user.", "type": "integer"}}}, "CheckoutPriceLegacyResponse": {"title": "CheckoutPriceLegacyResponse", "type": "object", "properties": {"userIsMember": {"description": "The user's membership status, including trial.", "type": "boolean"}, "trialUsed": {"description": "User trial membership status.", "type": "boolean"}, "nonMemberPrice": {"description": "Non-member price item.", "allOf": [{"$ref": "#/components/schemas/PriceObject"}]}, "memberPrice": {"description": "tier_0 Member price item.", "allOf": [{"$ref": "#/components/schemas/PriceObject"}]}}}, "CheckoutPriceResponse": {"title": "CheckoutPriceResponse", "type": "object", "properties": {"userIsMember": {"description": "The user's membership status, including trial.", "type": "boolean"}, "trialUsed": {"description": "User trial membership status.", "type": "boolean"}, "currentPrice": {"description": "Current price item.", "allOf": [{"$ref": "#/components/schemas/PriceObject"}]}, "upgradeAvailable": {"description": "Whether user can upgrade membership.", "type": "boolean"}, "upgradeCost": {"description": "The cost(cents) to upgrade membership.", "type": "integer"}, "nextTierLookupKey": {"description": "Next tier lookup key.", "type": "string"}, "nextTierPrice": {"description": "Next tier price item.", "allOf": [{"$ref": "#/components/schemas/PriceObject"}]}}}, "DtoPriceObjectForOneBuyingType": {"title": "DtoPriceObjectForOneBuyingType", "type": "object", "properties": {"currentPrice": {"description": "Current price item.", "allOf": [{"$ref": "#/components/schemas/PriceObject"}]}, "nextTierPrice": {"description": "Next tier price item.", "allOf": [{"$ref": "#/components/schemas/PriceObject"}]}}}, "CheckoutPriceForDetailPageResponse": {"title": "CheckoutPriceForDetailPageResponse", "type": "object", "properties": {"userIsMember": {"description": "The user's membership status, including trial.", "type": "boolean"}, "trialUsed": {"description": "User trial membership status.", "type": "boolean"}, "upgradeAvailable": {"description": "Whether user can upgrade membership.", "type": "boolean"}, "upgradeCost": {"description": "The cost(cents) to upgrade membership.", "type": "integer"}, "nextTierLookupKey": {"description": "Next tier lookup key.", "type": "string"}, "teamBuyPrice": {"description": "Team buy price item.", "allOf": [{"$ref": "#/components/schemas/DtoPriceObjectForOneBuyingType"}]}, "joinPrice": {"description": "Join price item.", "allOf": [{"$ref": "#/components/schemas/DtoPriceObjectForOneBuyingType"}]}, "buyAlonePrice": {"description": "Buy alone price item.", "allOf": [{"$ref": "#/components/schemas/DtoPriceObjectForOneBuyingType"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}