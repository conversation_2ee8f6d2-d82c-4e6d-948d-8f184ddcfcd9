{"openapi": "3.1.0", "info": {"title": "Blidz Games API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/games/game_status": {"post": {"description": "The status of each game. For level-based games, the next level and reward. For others, the earned cash", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GameStatusReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GameStatusResp"}}}}}}}}}, "/games/buy_turn": {"post": {"description": "Buy a turn", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuyTurnReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/BuyTurnResp"}}}}}}}}}, "/games/candy_jam/info": {"post": {"description": "[<PERSON> Jam]: All level numbers and rewards. And some other information", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CandyJamInfoReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CandyJamInfoResp"}}}}}}}}}, "/games/candy_jam/start_level": {"post": {"description": "[Candy Jam]: Start a level. Note: the response is encrypted by xxtea", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CandyJamStartLevelReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CandyJamStartLevelResp"}}}}}}}}}, "/games/candy_jam/finish_level": {"post": {"description": "[Candy Jam]: Finish a level. Note: the request should be encrypted by xxtea", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CandyJamFinishLevelReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/CandyJamFinishLevelResp"}}}}}}}}}, "/games/candy_jam/buy_shuffle": {"post": {"description": "[Candy Jam]: Buy a shuffle", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuyShuffleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/games/snake/info": {"post": {"description": "[Snake]: some information of the game. Like the turn price", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnakeInfoReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SnakeInfoResp"}}}}}}}}}, "/games/snake/start_level": {"post": {"description": "[Snake]: Start a level. Note: the response is encrypted by xxtea", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnakeStartLevelReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SnakeStartLevelResp"}}}}}}}}}, "/games/snake/finish_level": {"post": {"description": "[Snake]: Finish a level. Note: The request should be encrypted by xxtea", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnakeFinishLevelReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/SnakeFinishLevelResp"}}}}}}}}}}, "components": {"schemas": {"GameStatusReq": {"title": "GameStatusReq", "type": "object", "properties": {"statusCount": {"description": "The number of game status to return", "default": 2, "exclusiveMinimum": 0, "type": "integer"}, "userId": {"description": "Id of the user requires game status", "type": "string"}}}, "DtoGameId": {"title": "DtoGameId", "description": "An enumeration.", "enum": ["candy_jam", "snake"], "type": "string"}, "DtoRewardType": {"title": "DtoRewardType", "description": "An enumeration.", "enum": ["cash", "coin", "key", "spin", "game_turn", "mystery_box"], "type": "string"}, "DtoReward": {"title": "DtoReward", "type": "object", "properties": {"rewardType": {"description": "The type of reward", "allOf": [{"$ref": "#/components/schemas/DtoRewardType"}]}, "rewardAmount": {"description": "The amount of reward", "exclusiveMinimum": 0, "type": "integer"}}}, "DtoStatus": {"title": "DtoStatus", "type": "object", "properties": {"gameId": {"description": "The ID of the game", "allOf": [{"$ref": "#/components/schemas/DtoGameId"}]}, "nextLevel": {"description": "For level-based game, the next level number", "minimum": 0, "type": "integer"}, "nextReward": {"description": "For level-based game, the reward for the next level", "allOf": [{"$ref": "#/components/schemas/DtoReward"}]}, "finishedAllLevels": {"description": "For level-based game, whether the user has finished all levels", "type": "boolean"}, "totalCash": {"description": "For non-level-based game, the total cash earned", "minimum": 0, "type": "integer"}}}, "DtoPriceType": {"title": "DtoPriceType", "description": "An enumeration.", "enum": ["coin"], "type": "string"}, "DtoPrice": {"title": "DtoPrice", "type": "object", "properties": {"type": {"description": "Price type", "allOf": [{"$ref": "#/components/schemas/DtoPriceType"}]}, "amount": {"description": "Price amount", "minimum": 0, "type": "integer"}}}, "DtoPowerUpPrices": {"title": "DtoPowerUpPrices", "type": "object", "properties": {"snipes": {"description": "Sniper power up price.", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "moves": {"description": "Moves power up price.", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "shuffles": {"description": "Shuffle price.", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}}}, "DtoGameStatus": {"title": "DtoGameStatus", "type": "object", "properties": {"gameId": {"description": "The game id", "allOf": [{"$ref": "#/components/schemas/DtoGameId"}]}, "turnPrice": {"description": "The price for the each turn", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "turnsLeft": {"description": "The total count of turns left", "minimum": 0, "type": "integer"}, "turnResetTime": {"description": "The time to reset the turn count", "minimum": 0, "type": "integer"}, "shufflePrice": {"description": "[Deprecated: Use power_up_prices.shuffle instead] The price for shuffling tiles", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "powerUpPrices": {"description": "The prices for power ups.", "allOf": [{"$ref": "#/components/schemas/DtoPowerUpPrices"}]}}}, "GameStatusResp": {"title": "GameStatusResp", "type": "object", "properties": {"availableGames": {"description": "The available games", "type": "array", "items": {"$ref": "#/components/schemas/DtoGameId"}}, "status": {"description": "The status of games", "type": "array", "items": {"$ref": "#/components/schemas/DtoStatus"}}, "gameStatus": {"description": "Game status including turns left and prices", "type": "array", "items": {"$ref": "#/components/schemas/DtoGameStatus"}}}}, "BuyTurnReq": {"title": "BuyTurnReq", "type": "object", "properties": {"gameId": {"description": "The ID of the game", "allOf": [{"$ref": "#/components/schemas/DtoGameId"}]}}, "required": ["gameId"]}, "BuyTurnResp": {"title": "BuyTurnResp", "type": "object", "properties": {"turnsLeft": {"description": "The total count of turns left", "minimum": 0, "type": "integer"}}}, "CandyJamInfoReq": {"title": "CandyJamInfoReq", "type": "object", "properties": {}}, "DtoPowerUpFreeCounts": {"title": "DtoPowerUpFreeCounts", "type": "object", "properties": {"snipesCount": {"description": "Free sniper power up count.", "type": "integer"}, "movesCount": {"description": "Free moves power up count.", "type": "integer"}, "shufflesCount": {"description": "Free shuffle count.", "type": "integer"}}}, "DtoCandyJamLevel": {"title": "DtoCandyJamLevel", "type": "object", "properties": {"level": {"description": "The level number", "minimum": 0, "type": "integer"}, "reward": {"description": "The reward for the level", "allOf": [{"$ref": "#/components/schemas/DtoReward"}]}, "highlightedInMap": {"description": "Whether the reward is highlighted in map", "type": "boolean"}, "freeShuffleCount": {"description": "[Deprecated: use power_up_free_counts.shuffle] The amount of free shuffles for the level.", "type": "integer"}, "powerUpFreeCounts": {"description": "The free counts for power ups.", "allOf": [{"$ref": "#/components/schemas/DtoPowerUpFreeCounts"}]}}}, "CandyJamInfoResp": {"title": "CandyJamInfoResp", "type": "object", "properties": {"levels": {"description": "The levels of the game", "type": "array", "items": {"$ref": "#/components/schemas/DtoCandyJamLevel"}}, "nextLevel": {"description": "The next level number to be unlocked", "minimum": 0, "type": "integer"}, "turnPrice": {"description": "The price for the each turn", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "turnsLeft": {"description": "The total count of turns left", "minimum": 0, "type": "integer"}, "turnResetTime": {"description": "The time to reset the turn count", "minimum": 0, "type": "integer"}, "shufflePrice": {"description": "The price for shuffling tiles", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "powerUpPrices": {"description": "The prices for power ups.", "allOf": [{"$ref": "#/components/schemas/DtoPowerUpPrices"}]}, "freeTurnsPerDay": {"description": "The number of free turn per day", "exclusiveMinimum": 0, "type": "integer"}}}, "CandyJamStartLevelReq": {"title": "CandyJamStartLevelReq", "type": "object", "properties": {"level": {"description": "The level number. If not specified, start the next level, or last level if no next level", "minimum": 0, "type": "integer"}}}, "DtoCandyJamObjective": {"title": "DtoCandyJamObjective", "type": "object", "properties": {"item": {"description": "The item to be collected", "type": "string"}, "count": {"description": "The number of items to be collected", "exclusiveMinimum": 0, "type": "integer"}}}, "CandyJamStartLevelResp": {"title": "CandyJamStartLevelResp", "type": "object", "properties": {"reward": {"description": "The reward for the level. Will not appear in production", "allOf": [{"$ref": "#/components/schemas/DtoReward"}]}, "rows": {"description": "The number of rows in the level. This field will not appear in production", "exclusiveMinimum": 0, "type": "integer"}, "cols": {"description": "The number of columns in the level. This field will not appear in production", "exclusiveMinimum": 0, "type": "integer"}, "moves": {"description": "The number of moves allowed in the level. This field will not appear in production", "exclusiveMinimum": 0, "type": "integer"}, "grid": {"description": "The grid of the level. This field will not appear in production", "type": "array", "items": {"type": "string"}}, "objectives": {"description": "The objectives of the level. This field will not appear in production", "type": "array", "items": {"$ref": "#/components/schemas/DtoCandyJamObjective"}}, "turnsLeft": {"description": "The total count of turns left. This field will not appear in production", "minimum": 0, "type": "integer"}, "freeShuffleCount": {"description": "The amount of free shuffles the user has. This field will not appear in production.", "minimum": 0, "type": "integer"}, "powerUpFreeCounts": {"description": "The free counts for power ups.", "allOf": [{"$ref": "#/components/schemas/DtoPowerUpFreeCounts"}]}, "z": {"description": "The encrypted data of the response. In production, only this field will be returned. The structure of the decrypted data is: {\"reward\": {...}, \"rows\": ..., }", "type": "string"}}}, "CandyJamFinishLevelReq": {"title": "CandyJamFinishLevelReq", "type": "object", "properties": {"finishedLevel": {"description": "The finished level number, this field will not appear in production", "minimum": 0, "type": "integer"}, "z": {"description": "The encrypted data of the request. In production, only this field will be used. The structure of decrypted data is: {\"finishedLevel\": ...}", "type": "string"}}, "required": ["z"]}, "CandyJamFinishLevelResp": {"title": "CandyJamFinishLevelResp", "type": "object", "properties": {"reward": {"description": "The reward for the level", "allOf": [{"$ref": "#/components/schemas/DtoReward"}]}}}, "BuyShuffleRequest": {"title": "BuyShuffleRequest", "description": "Todo: This is deprecated.", "type": "object", "properties": {"level": {"description": "The level to buy the shuffle for.", "type": "integer"}}, "required": ["level"]}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "SnakeInfoReq": {"title": "SnakeInfoReq", "type": "object", "properties": {}}, "SnakeInfoResp": {"title": "SnakeInfoResp", "type": "object", "properties": {"turnPrice": {"description": "The price for the each turn", "allOf": [{"$ref": "#/components/schemas/DtoPrice"}]}, "levelDuration": {"description": "The duration of each level", "exclusiveMinimum": 0, "type": "integer"}, "turnsLeft": {"description": "The total count of turns left", "minimum": 0, "type": "integer"}, "turnResetTime": {"description": "The time to reset the turn count", "minimum": 0, "type": "integer"}, "freeTurnsPerDay": {"description": "The number of free turn per day", "exclusiveMinimum": 0, "type": "integer"}}}, "SnakeStartLevelReq": {"title": "SnakeStartLevelReq", "type": "object", "properties": {}}, "SnakeStartLevelResp": {"title": "SnakeStartLevelResp", "type": "object", "properties": {"turnsLeft": {"description": "The total count of turns left. This field will not appear in production", "minimum": 0, "type": "integer"}, "rewards": {"description": "Generated random rewards for the snake game.", "type": "array", "items": {"$ref": "#/components/schemas/DtoReward"}}, "z": {"description": "The encrypted data of the response. In production, only this field will be returned. The structure of the decrypted data is: {\"turns_left\": 1}", "type": "string"}}}, "SnakeFinishLevelReq": {"title": "SnakeFinishLevelReq", "type": "object", "properties": {"rewards": {"description": "The collected rewards from the snake game.", "type": "array", "items": {"$ref": "#/components/schemas/DtoReward"}}, "z": {"description": "The encrypted data of the request. In production, only this field is used.", "type": "string"}}, "required": ["z"]}, "SnakeFinishLevelResp": {"title": "SnakeFinishLevelResp", "type": "object", "properties": {}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}