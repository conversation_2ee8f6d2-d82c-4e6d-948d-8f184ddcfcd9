{"openapi": "3.1.0", "info": {"title": "Blidz Ab_Test API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/ab-test/get-abtest": {"post": {"description": "Get abtest info from abtest system", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAbtestReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetAbtestResp"}}}}}}}}}}, "components": {"schemas": {"GetAbtestReq": {"title": "GetAbtestReq", "type": "object", "properties": {}}, "GetAbtestResp": {"title": "GetAbtestResp", "type": "object", "properties": {"abIds": {"description": "List of ab ids", "type": "array", "items": {"type": "string"}}, "abParams": {"description": "Dictionary of ab params", "type": "object"}, "userId": {"description": "User id", "type": "string"}, "lastUpdated": {"description": "Last updated time in milliseconds from epoch", "exclusiveMinimum": 0, "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}