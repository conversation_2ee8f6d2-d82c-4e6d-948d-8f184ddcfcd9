{"openapi": "3.1.0", "info": {"title": "Blidz Shopping_Cart API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/shopping_cart/add_to_cart": {"post": {"description": "Add deal to shoppign cart", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddToCartReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/shopping_cart/remove_from_cart": {"post": {"description": "Remove deal to shoppign cart", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveFromCartReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/shopping_cart/list_deals": {"post": {"description": "List deals in shoppign cart", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDealsInCartReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ListDealsInCartResp"}}}}}}}}}, "/shopping_cart/change_sku": {"post": {"description": "Change the SKU of an item in shopping cart", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeSKUReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ChangeSKUResp"}}}}}}}}}}, "components": {"schemas": {"DtoCartPriceLevel": {"title": "DtoCartPriceLevel", "description": "An enumeration.", "enum": ["target", "join", "solo"], "type": "string"}, "AddToCartReq": {"title": "AddToCartReq", "type": "object", "properties": {"dealId": {"description": "Deal ID", "type": "string"}, "skuId": {"description": "SKU ID of the deal", "type": "string"}, "priceLevel": {"description": "The price level", "allOf": [{"$ref": "#/components/schemas/DtoCartPriceLevel"}]}, "duoDealEntryId": {"description": "The duo deal entry Id. If price_level is 'join', this field is required", "type": "string"}, "channel": {"description": "Algo channel defined by client", "type": "string"}, "reqId": {"description": "Deal req_id", "type": "string"}}, "required": ["dealId", "priceLevel"]}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "RemoveFromCartReq": {"title": "RemoveFromCartReq", "type": "object", "properties": {"cartItemIds": {"description": "List of deal item Id in shopping cart", "type": "array", "items": {"type": "string"}}}, "required": ["cartItemIds"]}, "ListDealsInCartReq": {"title": "ListDealsInCartReq", "type": "object", "properties": {"priceLevel": {"description": "The price level", "allOf": [{"$ref": "#/components/schemas/DtoCartPriceLevel"}]}, "page": {"description": "The page number. Start from 1", "exclusiveMinimum": 0, "type": "integer"}, "pageSize": {"description": "Count of item in page. De<PERSON>ult is 10", "default": 10, "exclusiveMinimum": 0, "maximum": 50, "type": "integer"}}, "required": ["priceLevel"]}, "DtoOptionDef": {"title": "DtoOptionDef", "type": "object", "properties": {"id": {"description": "The option Id", "type": "string"}, "name": {"description": "The option name", "type": "string"}}}, "DtoAttributeDef": {"title": "DtoAttributeDef", "type": "object", "properties": {"id": {"description": "The attribute Id", "type": "string"}, "name": {"description": "The attribute name", "type": "string"}, "options": {"description": "The options of this attribute", "type": "array", "items": {"$ref": "#/components/schemas/DtoOptionDef"}}}}, "DtoSkuOption": {"title": "DtoSkuOption", "type": "object", "properties": {"attributeId": {"description": "The attribute Id of this SKU", "type": "string"}, "optionId": {"description": "The option Id of this SKU", "type": "string"}}}, "DtoShoppingCartItemSku": {"title": "DtoShoppingCartItemSku", "type": "object", "properties": {"skuId": {"description": "SKU Id", "type": "string"}, "priceOffset": {"description": "The offset related to the base price of deal", "type": "integer"}, "image": {"description": "The image name of SKU. Need client to construct the whole image URL", "type": "string"}, "options": {"description": "The attributes and options of this SKU", "type": "array", "items": {"$ref": "#/components/schemas/DtoSkuOption"}}}}, "DtoShoppingCartItem": {"title": "DtoShoppingCartItem", "type": "object", "properties": {"cartItemId": {"description": "Shopping cart item Id", "type": "string"}, "productId": {"description": "Product Id", "type": "string"}, "dealId": {"description": "Deal Id", "type": "string"}, "skuId": {"description": "The picked SKU Id. Note: some SKU has no Id", "type": "string"}, "dealName": {"description": "The name of deal", "type": "string"}, "priceLevel": {"description": "The price level", "allOf": [{"$ref": "#/components/schemas/DtoCartPriceLevel"}]}, "basePrice": {"description": "The base price of this deal. The 'item_price' from algo", "minimum": 0, "type": "integer"}, "retailPrice": {"description": "The retail/strikethrough price of the deal", "minimum": 0, "type": "integer"}, "productCost": {"description": "The product cost price of the deal in USD, after price reallocation", "minimum": 0, "type": "integer"}, "currency": {"description": "The currency of price", "type": "string"}, "image": {"description": "The image name of the deal. Need client to construct the whole image URL", "type": "string"}, "attributes": {"description": "The attributes of SKUs. Provides the mapping from attribute Id to attribute name", "type": "array", "items": {"$ref": "#/components/schemas/DtoAttributeDef"}}, "allSkus": {"description": "All SKUs info of this deal", "type": "array", "items": {"$ref": "#/components/schemas/DtoShoppingCartItemSku"}}, "available": {"description": "It's false when deal is closed or duo deal entry is closed", "type": "boolean"}, "reqId": {"description": "Original deal req_id", "type": "string"}, "algoChannel": {"description": "Original deal channel", "type": "string"}, "duoDealEntryId": {"description": "For join deal, the duo deal entry Id", "type": "string"}, "hostName": {"description": "For join deal, the name of host", "type": "string"}, "hostAvatarUrl": {"description": "For join deal, the avatar URL of host", "type": "string"}}}, "ListDealsInCartResp": {"title": "ListDealsInCartResp", "type": "object", "properties": {"deals": {"description": "One page of deals in shopping cart", "type": "array", "items": {"$ref": "#/components/schemas/DtoShoppingCartItem"}}, "total": {"description": "Total count of deals in shopping cart", "exclusiveMinimum": 0, "type": "integer"}}}, "ChangeSKUReq": {"title": "ChangeSKUReq", "type": "object", "properties": {"cartItemId": {"description": "The ID of shopping cart item", "type": "string"}, "skuId": {"description": "The ID of SKU that we want to change to", "type": "string"}}, "required": ["cartItemId", "skuId"]}, "ChangeSKUResp": {"title": "ChangeSKUResp", "type": "object", "properties": {"item": {"description": "The shopping cart item after change", "allOf": [{"$ref": "#/components/schemas/DtoShoppingCartItem"}]}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}