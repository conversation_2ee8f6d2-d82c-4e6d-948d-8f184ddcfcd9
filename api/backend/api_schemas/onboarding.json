{"openapi": "3.1.0", "info": {"title": "Blidz Onboarding API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/onboarding/duo_deal_pool": {"post": {"description": "Get the onboarding duo deal pool", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/OnboardingDuoDealPoolResp"}}}}}}}}}, "/onboarding/duo_deal_data": {"post": {"description": "Get the onboarding duo deal data", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/OnboardingDuoDealDataResp"}}}}}}}}}, "/onboarding/start_duo_deal_entry": {"post": {"description": "Start the onboarding duo deal entry", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartOnboardingDuoDealEntryReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/StartOnboardingDuoDealEntryResp"}}}}}}}}}, "/onboarding/join_duo_deal_entry": {"post": {"description": "Join the onboarding duo deal entry", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JoinOnboardingDuoDealEntryReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/JoinOnboardingDuoDealEntryResp"}}}}}}}}}, "/onboarding/claim_completed_duo_deal_entry_reward": {"post": {"description": "Claim the completed onboarding duo deal entry reward", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/ClaimCompletedOnboardingDuoDealEntryRewardResp"}}}}}}}}}}, "components": {"schemas": {"EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "LockCost": {"title": "LockCost", "type": "object", "properties": {"coins": {"title": "Coins", "default": 0, "type": "integer"}}}, "PriceDetail": {"title": "PriceDetail", "type": "object", "properties": {"coins": {"title": "Coins", "type": "integer"}, "price": {"title": "Price", "type": "integer"}, "shipping": {"title": "Shipping", "type": "integer"}, "shippingAsCoins": {"title": "Shippingascoins", "type": "integer"}}, "required": ["coins", "price", "shipping", "shippingAsCoins"]}, "PriceProperty": {"title": "PriceProperty", "type": "object", "properties": {"lockCost": {"$ref": "#/components/schemas/LockCost"}, "restricted": {"title": "Restricted", "type": "boolean"}, "price": {"title": "Price", "default": {}, "type": "object", "additionalProperties": {"$ref": "#/components/schemas/PriceDetail"}}}}, "PriceLevelSet": {"title": "PriceLevelSet", "type": "object", "properties": {"solo": {"$ref": "#/components/schemas/PriceProperty"}, "target": {"$ref": "#/components/schemas/PriceProperty"}, "join": {"$ref": "#/components/schemas/PriceProperty"}, "retail": {"$ref": "#/components/schemas/PriceProperty"}, "sale": {"$ref": "#/components/schemas/PriceProperty"}, "min": {"$ref": "#/components/schemas/PriceProperty"}, "max": {"$ref": "#/components/schemas/PriceProperty"}}}, "DealAttributes": {"title": "DealAttributes", "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "optionCount": {"title": "Optioncount", "type": "integer"}}}, "DealClientListData": {"title": "DealClientListData", "type": "object", "properties": {"_id": {"pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "state": {"type": "string"}, "priceLevels": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "array", "items": {}}}}, "lowestPriceInUsdCent": {"type": "integer"}, "reallocatedPrice": {"type": "integer"}, "price": {"type": "integer"}, "fincoinPrice": {"type": "integer"}, "strikethroughPrice": {"type": "integer"}, "_productId": {"type": "string"}, "productName": {"type": "string"}, "image": {"type": "string"}, "rating": {"type": "number"}, "ratingCount": {"type": "integer"}, "reviews": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "retailPrice": {"type": "object", "additionalProperties": {"type": "integer"}}, "duoDeal": {"default": true, "type": "boolean"}, "gamified": {"default": false, "type": "boolean"}, "dealRace": {"default": false, "type": "boolean"}, "quantity": {"default": {}, "type": "object"}, "minLockCost": {"default": 1, "type": "integer"}, "priceCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "coinCostCurve": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "number"}}}, "imageKitEnabled": {"default": true, "type": "boolean"}, "lastUpdated": {"type": "integer"}, "duoDealEntries": {"type": "array", "items": {"type": "object"}}, "timerStart": {"type": "integer"}, "timerEnd": {"type": "integer"}, "endTime": {"type": "integer"}, "recentRacers": {"type": "object"}, "amountOfRacers": {"type": "integer"}, "usersWon": {"type": "object"}, "maxRacer": {"type": "integer"}, "leaderboardActivated": {"type": "boolean"}, "productLikes": {"type": "integer"}, "productCost": {"type": "integer"}, "supplierName": {"type": "string"}, "supplierStoreId": {"type": "string"}, "fromProducer": {"type": "string"}, "reqId": {"type": "string"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/DealAttributes"}}, "soldCount": {"type": "integer"}, "data": {"description": "Client List Data as a string.", "type": "string"}, "branded": {"type": "boolean"}, "startTime": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}, "hasAdultContent": {"type": "boolean"}}, "required": ["_id", "state", "_productId", "productName", "type"]}, "OnboardingDuoDealPoolResp": {"title": "OnboardingDuoDealPoolResp", "type": "object", "properties": {"deals": {"description": "The deals in the onboarding duo deal pool", "type": "array", "items": {"$ref": "#/components/schemas/DealClientListData"}}}}, "OnboardingDuoDealEntry": {"title": "OnboardingDuoDealEntry", "type": "object", "properties": {"duoDealEntryId": {"description": "The duo deal entry ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "orderId": {"description": "The order ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "requireFillAddress": {"description": "Whether the user needs to fill in the address", "default": false, "type": "boolean"}}, "required": ["duoDealEntryId", "orderId"]}, "OnboardingDuoDealDataResp": {"title": "OnboardingDuoDealDataResp", "type": "object", "properties": {"canStartDuoDeal": {"description": "Whether the user can start onboarding duo deal", "type": "boolean"}, "canJoinDuoDeal": {"description": "Whether the user can join onboarding duo deal", "type": "boolean"}, "canClaimDuoDealEntryReward": {"description": "Whether the user can claim the onboarding duo deal entry reward", "type": "boolean"}, "ongoingDuoDealEntry": {"description": "The ongoing onboarding duo deal entry", "allOf": [{"$ref": "#/components/schemas/OnboardingDuoDealEntry"}]}}}, "Options": {"title": "Options", "type": "object", "properties": {"skuId": {"description": "SKU ID", "type": "string"}, "attributes": {"description": "The attribute of sku. For example: {\"color\": \"red\"}", "type": "object"}, "priceLevel": {"description": "Price level, one of min, max, sale, target, solo, join. In Meituan model, when duodeal is team_buy, price_level must be none.", "type": "string"}}}, "Item": {"title": "<PERSON><PERSON>", "type": "object", "properties": {"type": {"description": "should be 'deal', because other types of items are all deprecated", "type": "string"}, "id": {"description": "should be deal id, because other types of items are all deprecated", "type": "string"}, "currency": {"description": "Currency of payment", "type": "string"}, "options": {"description": "Options", "allOf": [{"$ref": "#/components/schemas/Options"}]}, "shippingOptionCode": {"description": "The shipping option code. If omitted, use default blidz shipping", "type": "string"}, "buySource": {"description": "Whether user place orders in app or via an external link, one of internal, external", "type": "string"}, "duoDealEntryId": {"description": "The duo deal entry ID if user is joining a duo deal", "type": "string"}, "reqId": {"description": "Request Id", "type": "string"}, "shoppingCartItemId": {"description": "The ID of shopping cart item", "type": "string"}, "channel": {"description": "Original deal channel", "type": "string"}, "allowAutoCompleteByTicket": {"description": "Whether allow auto complete by ticket", "type": "boolean"}}}, "StartOnboardingDuoDealEntryReq": {"title": "StartOnboardingDuoDealEntryReq", "type": "object", "properties": {"item": {"description": "The checkout item", "allOf": [{"$ref": "#/components/schemas/Item"}]}}, "required": ["item"]}, "StartOnboardingDuoDealEntryResp": {"title": "StartOnboardingDuoDealEntryResp", "type": "object", "properties": {"duoDealEntryId": {"description": "The duo deal entry ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "orderId": {"description": "The order ID.", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}}}, "Address": {"title": "Address", "type": "object", "properties": {"email": {"description": "Email", "type": "string"}, "firstName": {"description": "First name", "type": "string"}, "lastName": {"description": "Last name", "type": "string"}, "street": {"description": "Street address", "type": "string"}, "city": {"description": "City", "type": "string"}, "zip": {"description": "Zip code", "type": "string"}, "state": {"description": "State", "type": "string"}, "country": {"description": "Country", "type": "string"}, "phoneNumber": {"description": "Phone number", "type": "string"}, "recipient": {"description": "Recipient", "type": "string"}, "line2": {"description": "Address line 2", "type": "string"}, "timeZone": {"description": "Timezone", "type": "string"}}}, "Addresses": {"title": "Addresses", "type": "object", "properties": {"shipping": {"description": "Shipping address", "allOf": [{"$ref": "#/components/schemas/Address"}]}, "billing": {"description": "Billing address", "allOf": [{"$ref": "#/components/schemas/Address"}]}}}, "JoinOnboardingDuoDealEntryReq": {"title": "JoinOnboardingDuoDealEntryReq", "type": "object", "properties": {"duoDealEntryId": {"description": "The duo deal entry ID", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}, "item": {"description": "The checkout item", "allOf": [{"$ref": "#/components/schemas/Item"}]}, "addresses": {"description": "The billing/shipping addresses.", "allOf": [{"$ref": "#/components/schemas/Addresses"}]}}, "required": ["duoDealEntryId", "item"]}, "JoinOnboardingDuoDealEntryResp": {"title": "JoinOnboardingDuoDealEntryResp", "type": "object", "properties": {"orderId": {"description": "The order ID.", "pattern": "^[1-9a-f][0-9a-f]{23}$", "type": "string"}}}, "ClaimCompletedOnboardingDuoDealEntryRewardResp": {"title": "ClaimCompletedOnboardingDuoDealEntryRewardResp", "type": "object", "properties": {"fincoinAmount": {"description": "The fincoin amount", "type": "integer"}, "centAmount": {"description": "The cent amount", "type": "integer"}}}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}