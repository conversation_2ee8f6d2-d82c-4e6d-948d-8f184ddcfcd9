{"openapi": "3.1.0", "info": {"title": "Blidz Payment API", "version": "1.0"}, "servers": [{"url": "{{host}}"}], "paths": {"/payment/paypal/store_payout_account": {"post": {"description": "Store PayPal payout email account", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayPalStorePayoutAccountRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/payment/paypal/payout": {"post": {"description": "Do the actual payout", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}, "/payment/paypal/login_with_paypal": {"post": {"description": "Login with paypal", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginWithPaypalRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/LoginWithPaypalResponse"}}}}}}}}}, "/payment/paypal/get_paypal_user_info": {"post": {"description": "Get user info in paypal", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmptyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/GetPaypalUserInfoResponse"}}}}}}}}}, "/payment/verify_card_iin": {"post": {"description": "Verify card by iin", "parameters": [{"$ref": "#/components/schemas/device-id"}, {"$ref": "#/components/schemas/x-blidz-client-version"}, {"$ref": "#/components/schemas/sessionid"}, {"$ref": "#/components/schemas/csrftoken"}, {"$ref": "#/components/schemas/X-CSRFToken"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyCardIinReq"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "description": "The HTTP status"}, "type": {"type": "string", "description": "Whether it's success or error", "enum": ["success", "error"]}, "content": {"$ref": "#/components/schemas/EmptyResponse"}}}}}}}}}}, "components": {"schemas": {"PayPalStorePayoutAccountRequest": {"title": "PayPalStorePayoutAccountRequest", "type": "object", "properties": {"email": {"description": "The PayPal Account email address.", "pattern": "[^@\\s-]+@[^@\\s-]+\\.[^@\\s-]+", "type": "string"}}, "required": ["email"]}, "EmptyResponse": {"title": "EmptyResponse", "description": "Empty response, without a body.", "type": "object", "properties": {}}, "EmptyRequest": {"title": "EmptyRequest", "description": "Empty request, without a body.", "type": "object", "properties": {}}, "LoginWithPaypalRequest": {"title": "LoginWithPaypalRequest", "type": "object", "properties": {"authorizationCode": {"description": "The token paypal gives client after user logins in paypal view", "type": "string"}}, "required": ["authorizationCode"]}, "LoginWithPaypalResponse": {"title": "LoginWithPaypalResponse", "type": "object", "properties": {"email": {"description": "The email", "type": "string"}, "fullName": {"description": "The full name", "type": "string"}, "readyForPayout": {"description": "If paypal id or paypal email has been stored", "type": "boolean"}}}, "CurrentPaypalAccountType": {"title": "CurrentPaypalAccountType", "description": "An enumeration.", "enum": ["paypal_login", "paypal_email"], "type": "string"}, "GetPaypalUserInfoResponse": {"title": "GetPaypalUserInfoResponse", "type": "object", "properties": {"email": {"description": "The email", "type": "string"}, "fullName": {"description": "The full name", "type": "string"}, "readyForPayout": {"description": "If paypal id or paypal email has been stored", "type": "boolean"}, "canChangeAccount": {"description": "Whether user is allow to change paypal account now", "type": "boolean"}, "currentAccountType": {"description": "User submitted paypal account by email or paypal login", "allOf": [{"$ref": "#/components/schemas/CurrentPaypalAccountType"}]}}}, "VerifyCardIinReq": {"title": "VerifyCardIinReq", "type": "object", "properties": {"iin": {"description": "<PERSON>'s first 8 numbers", "type": "integer"}, "country": {"description": "<PERSON>'s country", "type": "string"}, "currency": {"description": "Card's currency", "type": "string"}}, "required": ["iin"]}, "Error": {"title": "error", "type": "object", "properties": {"status": {"description": "The status of the response.", "type": "string"}, "type": {"description": "The response type. Error in this case.", "type": "string"}, "error": {"description": "The error message.", "type": "string"}}}, "device-id": {"name": "device-id", "in": "header", "description": "device id", "schema": {"type": "string", "default": "{{device-id}}"}}, "x-blidz-client-version": {"name": "x-blidz-client-version", "in": "header", "description": "client version", "schema": {"type": "string", "default": "{{x-blidz-client-version}}"}}, "X-CSRFToken": {"name": "X-CSRFToken", "in": "header", "description": "CSRF token in header", "schema": {"type": "string", "default": "{{X-CSRFToken}}"}}, "sessionid": {"name": "sessionid", "in": "cookie", "description": "session id in cookie", "schema": {"type": "string", "default": "{{sessionid}}"}}, "csrftoken": {"name": "csrftoken", "in": "cookie", "description": "CSRF token in cookie", "schema": {"type": "string", "default": "{{csrf<PERSON>en}}"}}}}}