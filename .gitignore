syntax: glob
.idea/

*.apk
*.ap_
*.dex
*.class

*.bak
*.orig
.env
.venv
bundle.json

client/biddl-prod.keystore
client/build.json
client/npm-debug.log
client/sass/.sass-cache/
client/node_modules/
client/platforms/
client/engine/
client/plugins/org.apache.cordova.engine.crosswalk/
client/www/js/config.env.js
client/www/lib
client/www/css
client/www/production.js
client/www/production.js.map
client/www/templates.js
client/www/templates.min.js
client/www/templates.modals.js
client/www/templates.modals.min.js
client/www/build
client/www/dist

staff/.idea
staff/.sass-cache
staff/build.json
staff/npm-debug.log
staff/node_modules
staff/bower_components
staff/build
staff/release
staff/tmp

homepage/api/
homepage/bower_components/
homepage/node_modules/
homepage/www/
homepage/www.tgz

bin/
gen/
venv/

local.properties

build/*

IOS/www/
.vagrant/
*.pyc
*.log

.DS_Store
*.sublime-project
*.sublime-workspace
client/log/
client/.sass-cache/
api/backend/.coverage
api/backend/coverage.xml
api/backend/ml_models/name_gender/*
!api/backend/ml_models/name_gender/PLACEHOLDER
api/tools/import_products/*/*
docs/.zim/index.db
docs/.zim/state.conf
*.rej
dealmanager/logs/*.json*
Gemfile.lock
client/cache
*.pid

commonlib/commonlib/.coverage

local_settings.py

html-angular-validate-report.json
client/report.html
client/output.xml
client/log.html

appium-screenshot*
emailer/emailer.json
emailer/logs/emailer.json*
emailer/.coverage
emailer/coverage.xml

scheduler/pytasched/local_settings.py

packer/builds
packer/packer_cache
packer/packer-*
dealmanager/coverage
settings_dev.py
gopath/
client/www/config/override.js
*/.coverage
*/coverage.xml
client/jshint.xml
www/config/serve.js
www/config/version.js
api/last_auto_import.txt
client/plugins/io.branch.sdk/node_modules

*/logs/*
data

__pycache__/
dist/
*.egg-info/
.pytest_cache/

# pyenv
.python-version

.ruff_cache

# allow the custom cursor rules
.cursorrules
.cursor/

# API schemas will be managed as git submodule
api/backend/api_schemas/
